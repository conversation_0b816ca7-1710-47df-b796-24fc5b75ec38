import fs from 'fs'
import path from 'path'
import os from 'os'
import { execSync } from 'child_process'
import parseArgs from './helpers/parseArgs.mjs'

// const nodeExePath = process.argv[0]
// const scriptPath = process.argv[1]
const args = parseArgs(process.argv.slice(2))

const sourceFile = args?.sourceFile
if (!sourceFile.includes('apps/core')) {
  console.error('❌ Error: Cannot re-export core styles outside core')
  process.exit(1)
}
if (!sourceFile.endsWith('.scss')) {
  console.error('❌ Error: reexport-core-styles only works with .scss files')
  process.exit(1)
}

const targetProject = args?.targetProject

const baseProject = 'core'
const importStatement = "@use '{{path_placeholder}}';" // NOTE: Could be extended for better flexibility (prompt for a new param?), enough for now

const absolutePathToSourceFile = path.resolve(sourceFile)
const relativePathToSourceFile = absolutePathToSourceFile.replace(/^.*?rhino-next\/apps\//, '')
const targetFilePath = 'apps/' + relativePathToSourceFile.replace(baseProject, targetProject)
const targetFileContent = importStatement.replace('{{path_placeholder}}', relativePathToSourceFile)
const targetFileDirname = path.dirname(targetFilePath)

fs.mkdirSync(targetFileDirname, { recursive: true })
fs.writeFileSync(targetFilePath, targetFileContent)
console.info(`✅ Success: File created ${targetFilePath}`)

try {
  const platform = os.platform()
  if (platform === 'darwin') {
    execSync(`open -a "Visual Studio Code" ${targetFilePath}`)
  } else if (platform === 'linux') {
    execSync(`xdg-open ${targetFilePath}`)
  } else if (platform === 'win32') {
    execSync(`start ${targetFilePath}`)
  } else {
    execSync(`open ${targetFilePath}`)
  }
} catch (err) {
  console.warn(`⚠️ Could not open the file ${err.message}`)
}
