import fs from 'fs'
import path from 'path'
import os from 'os'
import { execSync } from 'child_process'
import parseArgs from './helpers/parseArgs.mjs'

// Parse command line arguments
const args = parseArgs(process.argv.slice(2))

const sourceFile = args?.sourceFile
if (!sourceFile.includes('apps/core')) {
  console.error('❌ Error: Cannot re-export core files outside core')
  process.exit(1)
}

// Get the relative path from core app and construct import path
const absolutePathToSourceFile = path.resolve(sourceFile)
const relativePathToSourceFile = absolutePathToSourceFile.replace(/^.*?rhino-next\/apps\/core\/src\//, '')

// Remove file extension to create the import path
let importPath = relativePathToSourceFile.replace(/\.(tsx?|jsx?)$/, '')

// Convert file path to import path format
const coreImportPath = `@core/${importPath}`

// Content template for the re-export file
const fileContent = `export * from '${coreImportPath}'
export { default } from '${coreImportPath}'
`

// List of target projects (excluding core)
const targetProjects = ['luckyone', 'dreamspins']

// Create files for each target project
targetProjects.forEach(targetProject => {
  const targetFilePath = path.join('apps', targetProject, 'src', relativePathToSourceFile)
  const targetFileDirname = path.dirname(targetFilePath)

  // Create directory if it doesn't exist
  fs.mkdirSync(targetFileDirname, { recursive: true })

  // Write the re-export file
  fs.writeFileSync(targetFilePath, fileContent)
  console.info(`✅ Success: File created ${targetFilePath}`)
})

// Try to open the first created file in VS Code
const firstTargetFile = path.join('apps', targetProjects[0], 'src', relativePathToSourceFile)

try {
  const platform = os.platform()
  if (platform === 'darwin') {
    execSync(`open -a "Visual Studio Code" ${firstTargetFile}`)
  } else if (platform === 'linux') {
    execSync(`xdg-open ${firstTargetFile}`)
  } else if (platform === 'win32') {
    execSync(`start ${firstTargetFile}`)
  } else {
    execSync(`open ${firstTargetFile}`)
  }
} catch (err) {
  console.warn(`⚠️ Could not open the file ${err.message}`)
}
