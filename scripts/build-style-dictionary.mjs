#!/usr/bin/env node

import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const styleDictionaryDir = join(__dirname, 'style-dictionary');

/**
 * Build style dictionary - orchestrates all style dictionary build steps
 */
async function buildStyleDictionary() {
  const steps = [
    {
      name: 'Building global dictionaries (light)',
      script: join(styleDictionaryDir, 'build-global-dictionaries.mjs')
    },
    {
      name: 'Building global dictionaries (dark)',
      script: join(styleDictionaryDir, 'build-global-dictionaries.mjs'),
      args: ['dark']
    },
    {
      name: 'Building component dictionaries',
      script: join(styleDictionaryDir, 'build-component-dictionaries.mjs')
    },
    // {
    //   name: 'Building UI component dictionaries',
    //   script: join(styleDictionaryDir, 'build-component-stringified-dictionaries.mjs')
    // },
    {
      name: 'Merging themes',
      script: join(styleDictionaryDir, 'merge-themes.mjs')
    },
    {
      name: 'Converting CSS to SCSS variables',
      script: join(styleDictionaryDir, 'css-to-scss-variables.mjs')
    },
    {
      name: 'Converting CSS to TypeScript',
      script: join(styleDictionaryDir, 'css-to-ts.mjs')
    },
    {
      name: 'Generating HeroUI config',
      script: join(styleDictionaryDir, 'heroui', 'generate-heroui-config.mjs')
    }
  ];

  console.log('🎨 Building Style Dictionary...\n');

  for (const step of steps) {
    console.log(`⏳ ${step.name}...`);
    
    try {
      const command = `node ${step.script}${step.args ? ` ${step.args.join(' ')}` : ''}`;
      execSync(command, { stdio: 'inherit', cwd: process.cwd() });
      console.log(`✅ ${step.name} completed\n`);
    } catch (error) {
      console.error(`❌ Failed: ${step.name}`);
      console.error(error.message);
      process.exit(1);
    }
  }

  console.log('🎉 Style Dictionary build completed successfully!');
}

// Run the build
buildStyleDictionary().catch((error) => {
  console.error('❌ Style Dictionary build failed:', error);
  process.exit(1);
});
