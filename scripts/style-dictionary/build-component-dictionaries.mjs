import StyleDictionary from 'style-dictionary'
import { formats } from 'style-dictionary/enums'
import { register } from '@tokens-studio/sd-transforms'
import { kebabToPascalCase } from './utils/caseTransforms.mjs'
import { getImmediateChildren } from './utils/getImmediateChildren.mjs'
import { getTokenValueWithReference } from './utils/getTokenValueWithReference.mjs'
import { COMPONENT_DICTIONARIES_ROOT_PATH, CUSTOM_RELATIVE_PATHS_MAP } from './custom-component-dictionaries-build-paths.mjs'

register(StyleDictionary)

StyleDictionary.registerTransform({
  type: 'name',
  transitive: true,
  name: 'name/strip-component-prefixes',
  matcher: () => true,
  transform: token => {
    if (!token.filePath.includes('/components/')) {
      return token.path.join('-')
    }
    return token.path.slice(1).join('-')
  },
})

StyleDictionary.registerFormat({
  name: 'component-sd-module-format',
  format: ({ dictionary, options }) => {
    const { usesDtcg, outputReferences } = options

    return (
      '/**\n * Do not edit directly, this file was auto-generated.\n */\n' +
      `@use '@theme/style-dictionary' as *;\n` +
      dictionary.allTokens
        .map(token => getTokenValueWithReference({ token, dictionary, usesDtcg, outputReferences }))
        .join('\n')
    )
  },
})

function generateComponentDictionaryConfig(components) {
  return components.map(component => {
    const customPath = CUSTOM_RELATIVE_PATHS_MAP[component]
    const destination = customPath 
      || `components/${kebabToPascalCase(component)}/_${kebabToPascalCase(component)}.sd.module.scss`

    return {
      destination,
      format: 'component-sd-module-format',
      filter: token => token.path[0] === component,
    }
  })
}

function getStyleDictionaryConfig() {
  return {
    source: [
      'scripts/style-dictionary/tokens/brands/core/core-base.json',
      'scripts/style-dictionary/tokens/brands/core/core-light.json',
      'scripts/style-dictionary/tokens/components/*.json',
    ],
    preprocessors: ['tokens-studio'],
    platforms: {
      web: {
        transforms: ['name/strip-component-prefixes'],
        transformGroup: 'tokens-studio',
        buildPath: COMPONENT_DICTIONARIES_ROOT_PATH,
        files: [
          ...generateComponentDictionaryConfig(getImmediateChildren('scripts/style-dictionary/tokens/components')),
        ],
      },
    },
    outputReferences: true,
  }
}


;['web'].forEach(function (platform) {
  const sd = new StyleDictionary(getStyleDictionaryConfig())
  sd.buildPlatform(platform)
})
