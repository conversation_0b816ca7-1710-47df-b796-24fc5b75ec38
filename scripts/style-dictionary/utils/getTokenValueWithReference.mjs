import { usesReferences, getReferences } from 'style-dictionary/utils'

export function getTokenValueWithReference ({ token, dictionary, usesDtcg, outputReferences }) {
  let value = JSON.stringify(token.value)
  const originalValue = token.original.value
  const doesUseRefs = usesReferences(originalValue)
  const areOutputRefsEnabled =
    typeof outputReferences === 'function'
      ? outputReferences(token, { dictionary, usesDtcg })
      : outputReferences
  const isEligibleToOutputRef = doesUseRefs && areOutputRefsEnabled
  if (isEligibleToOutputRef) {
    const references = getReferences(originalValue, dictionary.unfilteredTokens)
    references.forEach(ref => {
      value = value.replace(ref.value, '$' + ref.name)
    })
  }
  const normalizedReferenceVar = value.replaceAll('"', '')
  return `$${token.name.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()}: ${isEligibleToOutputRef ? normalizedReferenceVar : token.value};`
}