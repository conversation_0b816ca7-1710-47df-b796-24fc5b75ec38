/**
 * Transforms a kebab-case string to PascalCase
 * @param {string} kebabString - The kebab-case string to transform
 * @returns {string} The PascalCase version of the string
 * 
 * @example
 * kebabToPascalCase('progress-bar') // returns 'ProgressBar'
 * kebabToPascalCase('icon-button') // returns 'IconButton'
 * kebabToPascalCase('button') // returns 'Button'
 */
export function kebabToPascalCase(kebabString) {
  if (!kebabString || typeof kebabString !== 'string') {
    return '';
  }
  return kebabString
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
}

/**
 * Transforms a kebab-case string to camelCase
 * @param {string} kebabString - The kebab-case string to transform
 * @returns {string} The camelCase version of the string
 * 
 * @example
 * kebabToCamelCase('progress-bar') // returns 'progressBar'
 * kebabToCamelCase('icon-button') // returns 'iconButton'
 * kebabToCamelCase('button') // returns 'button'
 */
export function kebabToCamelCase(kebabString) {
  if (!kebabString || typeof kebabString !== 'string') {
    return '';
  }
  return kebabString
    .split('-')
    .map((word, index) => index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
}
