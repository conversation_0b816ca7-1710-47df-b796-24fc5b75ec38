import { readdirSync, statSync } from 'fs';
import { join, parse } from 'path';

/**
 * Returns the immediate children (files and directories) of a given directory,
 * stripping all file extensions (e.g., .json, .ts, .md).
 * @param {string} dirPath - Path to the directory.
 * @param {Object} options - Configuration options.
 * @param {boolean} options.directoriesOnly - If true, only return directories.
 * @returns {string[]} Array of cleaned child names.
 */
export function getImmediateChildren(dirPath, options = {}) {
  try {
    const items = readdirSync(dirPath);
    const { directoriesOnly = false } = options;

    const names = items
      .map(item => {
        const itemPath = join(dirPath, item);
        const isDirectory = statSync(itemPath).isDirectory();

        if (directoriesOnly && !isDirectory) {
          return null;
        }

        return isDirectory ? item : parse(item).name;
      })
      .filter(name => name !== null);

    return names;
  } catch (error) {
    console.error(`Error reading directory "${dirPath}":`, error);
    return [];
  }
}
