import StyleDictionary from 'style-dictionary'
import { register } from '@tokens-studio/sd-transforms'
import { usesReferences, getReferences } from 'style-dictionary/utils'
import { kebabToPascalCase, kebabToCamelCase } from './utils/caseTransforms.mjs'
import { formatFileWithPrettier } from './heroui/build-utils.mjs'
import { join } from 'path'

register(StyleDictionary)

// Define the specific components to generate CSS dictionaries for
const TARGET_COMPONENTS = ['button']

StyleDictionary.registerTransform({
  type: 'name',
  transitive: true,
  name: 'name/strip-component-prefixes',
  matcher: () => true,
  transform: token => {
    if (!token.filePath.includes('/components/')) {
      return token.path.join('-')
    }
    return token.path.slice(1).join('-')
  },
})

StyleDictionary.registerFormat({
  name: 'component-stringified-module-format',
  format: ({ dictionary }) => {
    const tokenEntries = dictionary.allTokens
      .map(token => {
        const tokenName = kebabToCamelCase(
          token.name.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()
        )

        // Check if the token uses references
        const originalValue = token.original.value
        const doesUseRefs = usesReferences(originalValue)

        let tokenValue
        if (doesUseRefs) {
          // Get the references and use the first one as the CSS variable name
          const references = getReferences(originalValue, dictionary.unfilteredTokens)
          if (references && references.length > 0) {
            const referencedToken = references[0]
            const cssVarName = `--${referencedToken.name.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()}`
            tokenValue = `var(${cssVarName})`
          } else {
            // Fallback to token name if references exist but can't be resolved
            tokenValue = `var(--${tokenName})`
          }
        } else {
          // If no references, use the actual resolved value
          tokenValue = token.value
        }

        return `  ${tokenName}: '${tokenValue}'`
      })
      .join(',\n')

    return (
      '/**\n' +
      ' * Do not edit directly, this file was auto-generated.\n' +
      ' */\n\n' +
      'export default {\n' +
      tokenEntries +
      '\n' +
      '}\n'
    )
  },
})

function generateComponentDictionaryConfig(components) {
  return components.map(component => {
    const componentName = kebabToPascalCase(component)
    return {
      destination: `${componentName}/${componentName}.sd.ts`,
      format: 'component-stringified-module-format',
      filter: token => token.path[0] === component,
    }
  })
}

function getStyleDictionaryConfig() {
  return {
    source: [
      'scripts/style-dictionary/tokens/brands/core/core-base.json',
      'scripts/style-dictionary/tokens/brands/core/core-light.json',
      'scripts/style-dictionary/tokens/components/*.json',
    ],
    preprocessors: ['tokens-studio'],
    platforms: {
      web: {
        transforms: ['name/strip-component-prefixes'],
        transformGroup: 'tokens-studio',
        buildPath: 'apps/core/src/components/ui/',
        files: [...generateComponentDictionaryConfig(TARGET_COMPONENTS)],
      },
    },
    outputReferences: false,
  }
}

console.log('Building UI component dictionaries for:', TARGET_COMPONENTS.join(', '))

async function buildAndFormatDictionaries() {
  const config = getStyleDictionaryConfig()
  const sd = new StyleDictionary(config)

  // Build the platform
  sd.buildPlatform('web')

  // Format each generated file with Prettier
  const buildPath = config.platforms.web.buildPath
  for (const component of TARGET_COMPONENTS) {
    const componentName = kebabToPascalCase(component)
    const filePath = join(buildPath, `${componentName}/${componentName}.sd.ts`)
    await formatFileWithPrettier(filePath)
  }
}

buildAndFormatDictionaries()
  .then(() => {
    console.log('✅ UI component dictionaries built successfully!')
  })
  .catch(error => {
    console.error('❌ Error building dictionaries:', error)
    process.exit(1)
  })
