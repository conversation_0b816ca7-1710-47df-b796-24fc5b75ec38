import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { getImmediateChildren } from './utils/getImmediateChildren.mjs';

/**
 * Convert CSS custom properties to SCSS variables for multiple brands
 */

function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      if (key === 'brands') {
        options[key] = value.split(',').map(brand => brand.trim());
      } else {
        options[key] = value === 'false' ? false : (value === 'true' ? true : value);
      }
    }
  });
  
  return options;
}

function extractCSSVariables(cssContent) {
  const variables = {
    root: {}
  };
  
  const rootMatch = cssContent.match(/:root\s*\{([^}]+)\}/s);
  if (rootMatch) {
    const rootContent = rootMatch[1];
    const rootVars = rootContent.match(/--[\w-]+:\s*[^;]+;/g);
    if (rootVars) {
      rootVars.forEach(varDecl => {
        const match = varDecl.match(/--([^:]+):\s*([^;]+);/);
        if (match) {
          const [, name, value] = match;
          variables.root[name.trim()] = value.trim();
        }
      });
    }
  }
  
  return variables;
}

function convertToSCSSVariables(variables) {
  let scss = `/**
 * Do not edit directly, this file was auto-generated.
 */

`;

  Object.entries(variables.root).forEach(([name, value]) => {
    scss += `$${name}: var(--${name});\n`;
  });

  return scss;
}

function processBrand(brand) {
  const inputPath = `apps/${brand}/src/theme/style-dictionary.css`;
  const outputPath = `apps/${brand}/src/theme/_style-dictionary.scss`;
  
  if (!fs.existsSync(inputPath)) {
    console.warn(`Could not generate SCSS from CSS for ${brand}: Input file does not exist: ${inputPath}`);
    return false;
  }
  
  try {
    const cssContent = fs.readFileSync(inputPath, 'utf8');
    const variables = extractCSSVariables(cssContent);
    const scssContent = convertToSCSSVariables(variables);
    
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, scssContent);
    console.log(`Generated: ${outputPath}`);
    
    return {
      brand,
      rootVariables: Object.keys(variables.root).length,
      outputPath
    };
    
  } catch (error) {
    console.error(`Could not generate SCSS from CSS for ${brand}: `, error.message);
    return false;
  }
}

function main() {
  const options = parseArgs();
  const brands = options.brands || getImmediateChildren('apps', { directoriesOnly: true });
  const results = [];
  const errors = [];
  
  brands.forEach(brand => {
    const result = processBrand(brand);
    if (result) {
      results.push(result);
    } else {
      errors.push(brand);
    }
  });

  if (results.length === 0) {
    console.error('Could not generate any SCSS files from CSS.');
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { extractCSSVariables, convertToSCSSVariables, processBrand };
