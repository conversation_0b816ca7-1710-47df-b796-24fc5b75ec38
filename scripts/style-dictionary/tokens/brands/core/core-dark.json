{"color": {"primary": {"value": "#F8F8F8", "type": "color"}, "primary-border": {"value": "#F8F8F8", "type": "color"}, "primary-focus": {"value": "#FFFFFF", "type": "color"}, "on-primary": {"value": "#1F1F1F", "type": "color"}, "secondary": {"value": "#9C9C9C", "type": "color"}, "secondary-focus": {"value": "#a4a4a4", "type": "color"}, "secondary-border": {"value": "#969696", "type": "color"}, "on-secondary": {"value": "#FFFFFF", "type": "color"}, "tertiary": {"value": "#616161", "type": "color"}, "tertiary-border": {"value": "#616161", "type": "color"}, "tertiary-focus": {"value": "#717171", "type": "color"}, "on-tertiary": {"value": "#FFFFFF", "type": "color"}, "error": {"value": "#C73636", "type": "color"}, "error-container": {"value": "#FF9898", "type": "color"}, "success": {"value": "#82D02F", "type": "color"}, "on-success": {"value": "#FFFFFF", "type": "color"}, "background": {"value": "#1F1F1F", "type": "color"}, "fade": {"value": "linear-gradient(180deg, rgba( {color.background} ,0) 0%, {color.background} 100%)", "type": "color"}, "scrim": {"value": "rgba( {color.background} ,0.6)", "type": "color"}, "transparent": {"value": "rgba(0,0,0,0)", "type": "color"}, "surface": {"100": {"value": "#292929", "type": "color"}, "200": {"value": "#414141", "type": "color"}, "300": {"value": "#585858", "type": "color"}, "400": {"value": "#707070", "type": "color"}, "500": {"value": "#888888", "type": "color"}, "600": {"value": "#A0A0A0", "type": "color"}, "700": {"value": "#B8B8B8", "type": "color"}, "800": {"value": "#CFCFCF", "type": "color"}, "900": {"value": "#E7E7E7", "type": "color"}, "1000": {"value": "#FFFFFF", "type": "color"}}}}