{"color": {"primary": {"value": "#FFAA00", "type": "color"}, "primary-border": {"value": "#FFAA00", "type": "color"}, "primary-focus": {"value": "#E1A123", "type": "color"}, "on-primary": {"value": "#08082A", "type": "color"}, "secondary": {"value": "#FFFFFF", "type": "color"}, "secondary-border": {"value": "#D7D6E4", "type": "color"}, "secondary-focus": {"value": "#FFFFFF", "type": "color"}, "on-secondary": {"value": "{color.surface.1000}", "type": "color"}, "tertiary": {"value": "linear-gradient(180deg, #741CC2 0%, #B02FD8 100%)", "type": "color"}, "tertiary-border": {"value": "linear-gradient(180deg, #9736E2 0%, #D153FF 100%)", "type": "color"}, "tertiary-focus": {"value": "linear-gradient(180deg, #741CC2 0%, #B02FD8 100%)", "type": "color"}, "on-tertiary": {"value": "#FFFFFF", "type": "color"}, "error": {"value": "#C73636", "type": "color"}, "error-container": {"value": "#FF9898", "type": "color"}, "success": {"value": "#82D02F", "type": "color"}, "on-success": {"value": "#FFFFFF", "type": "color"}, "background": {"value": "#E8E7F3", "type": "color"}, "fade": {"value": "linear-gradient(180deg, rgba( {color.background} ,0) 0%, {color.background} 100%)", "type": "color"}, "scrim": {"value": "rgba( {color.background} ,0.6)", "type": "color"}, "surface": {"100": {"value": "#FAFAFF", "type": "color"}, "200": {"value": "#D7D6E4", "type": "color"}, "300": {"value": "#CDCBDE", "type": "color"}, "400": {"value": "#BAB8D1", "type": "color"}, "500": {"value": "#9997B1", "type": "color"}, "600": {"value": "#8A88A1", "type": "color"}, "700": {"value": "#75738E", "type": "color"}, "800": {"value": "#52506B", "type": "color"}, "900": {"value": "#2F2E42", "type": "color"}, "1000": {"value": "#1E1C38", "type": "color"}}}}