{"button-group": {"size": {"default": {"radius": {"value": "{radius.md}", "type": "dimension"}, "padding": {"value": "{size.xxs}", "type": "dimension"}, "gap": {"value": "{size.xxs}", "type": "dimension"}}, "compact": {"radius": {"value": "{radius.sm}", "type": "dimension"}, "padding": {"value": "{size.xxs}", "type": "dimension"}, "gap": {"value": "{size.xxs}", "type": "dimension"}}}, "default": {"background": {"value": "{color.surface.200}", "type": "color"}}}}