{"label": {"default": {"background": {"value": "rgba( {color.primary}, 0.15)", "type": "color"}, "text": {"value": "{color.surface.1000}", "type": "color"}, "icon": {"background": {"value": "{color.primary}", "type": "color"}, "foreground": {"value": "{color.surface.100}", "type": "color"}}}, "size": {"sm": {"radius": {"value": "{radius.sm}", "type": "dimension"}, "padding": {"value": "{size.xxs} {size.xs} {size.xxs} {size.xxs}", "type": "dimension"}, "gap": {"value": "{size.xs}", "type": "dimension"}, "icon-box": {"value": "{font.size.lg}", "type": "dimension"}, "icon": {"value": "{icon.size.xxs}", "type": "dimension"}, "font": {"value": "{typography.body.baseline.xs}", "type": "typography"}}}}}