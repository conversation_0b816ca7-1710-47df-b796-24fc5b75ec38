{"input": {"default": {"background": {"value": "{color.surface.100}", "type": "color"}, "text": {"value": "{color.surface.700}", "type": "color"}, "border": {"value": {"color": "{color.surface.200}", "width": "{border.width.default}", "style": "solid"}, "type": "border"}, "supporting-text": {"value": "{color.surface.600}", "type": "color"}}, "hover": {"border": {"value": "{color.surface.300}", "type": "color"}}, "active": {"text": {"value": "{color.surface.1000}", "type": "color"}, "border": {"value": "{color.surface.300}", "type": "color"}}, "disabled": {"background": {"value": "{color.surface.200}", "type": "color"}}, "error": {"border": {"value": "{color.error}", "type": "color"}, "supporting-text": {"value": "{color.error-container}", "type": "color"}}, "size": {"default": {"padding": {"value": "{size.sm} {size.md}", "type": "dimension"}, "gap": {"value": "{size.xs}", "type": "dimension"}, "height": {"value": "{size.3xl}", "type": "dimension"}, "font": {"value": "{typography.body.baseline.md}", "type": "typography"}}, "compact": {"padding": {"value": "{size.sm}", "type": "dimension"}, "gap": {"value": "{size.xs}", "type": "dimension"}, "height": {"value": "{size.3xl-3}", "type": "dimension"}, "font": {"value": "{typography.body.baseline.sm}", "type": "typography"}}}}}