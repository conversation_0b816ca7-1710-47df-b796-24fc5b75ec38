{"scroll-navigation-item": {"size": {"sm": {"radius": {"value": "{radius.sm}", "type": "dimension"}, "padding": {"value": "{size.xs}", "type": "dimension"}, "font": {"value": "{typography.body.baseline.xs}", "type": "typography"}}, "md": {"radius": {"value": "{radius.sm}", "type": "dimension"}, "padding": {"value": "{font.size.sm}", "type": "dimension"}, "font": {"value": "{typography.body.baseline.sm}", "type": "typography"}}}, "default": {"background": {"value": "{color.surface.200}", "type": "color"}, "icon": {"value": "{color.surface.800}", "type": "color"}, "text": {"value": "{color.surface.900}", "type": "color"}}, "active": {"background": {"value": "{color.tertiary}", "type": "color"}, "icon": {"value": "{color.on-tertiary}", "type": "color"}, "text": {"value": "{color.on-tertiary}", "type": "color"}}}}