{"switch": {"size": {"default": {"radius": {"value": "{radius.full}", "type": "dimension"}, "min-width": {"value": "{size.3xl-3}", "type": "dimension"}, "padding": {"value": "{size.xxs}", "type": "dimension"}, "toggle-box": {"value": "{size.lg}", "type": "dimension"}, "toggle-radius": {"value": "{radius.full}", "type": "dimension"}, "toggle-icon": {"value": "{icon.size.xxs}", "type": "dimension"}}}, "state": {"on": {"default": {"background": {"value": "{color.surface.400}", "type": "color"}, "toggle-background": {"value": "{color.surface.800}", "type": "color"}, "toggle-foreground": {"value": "{color.surface.1000}", "type": "color"}}}, "off": {"default": {"background": {"value": "{color.tertiary}", "type": "color"}, "toggle-background": {"value": "{color.on-tertiary}", "type": "color"}, "toggle-foreground": {"value": "{color.tertiary}", "type": "color"}}}}}}