import tinycolor from 'tinycolor2'

// Shade level mapping for generating color variations
// Positive values lighten, negative values darken, 0 is the base color
const SHADE_LEVELS_LIGHT = {
  50: 45,
  100: 40,
  200: 30,
  300: 20,
  400: 10,
  500: 0, // base color
  600: -8,
  700: -16,
  800: -24,
  900: -32,
}
const SHADE_LEVELS_DARK = {
  50: -32,
  100: -24,
  200: -16,
  300: -8,
  400: 0, // base color
  500: 10,
  600: 20,
  700: 30,
  800: 40,
  900: 45,
}

/**
 * Generate a complete color palette with shades from a base color
 * @param {string} baseColor - The base color (hex, rgb, hsl, etc.)
 * @param {string} [foregroundColor] - Optional foreground color for the palette
 * @returns {Object} Object with shade levels (50-900), DEFAULT, and optionally foreground
 */
function generateColorShades(baseColor, foregroundColor = null, themeName) {
  const shades = {}
  const SHADE_LEVELS = themeName === 'dark' ? SHADE_LEVELS_DARK : SHADE_LEVELS_LIGHT

  Object.entries(SHADE_LEVELS).forEach(([shade, modifier]) => {
    try {
      const color =
        modifier === 0
          ? tinycolor(baseColor)
          : modifier > 0
            ? tinycolor(baseColor).lighten(modifier)
            : tinycolor(baseColor).darken(Math.abs(modifier))

      shades[shade] = color.toHexString()
    } catch (error) {
      console.warn(`   ⚠️  Could not generate shade ${shade} for color ${baseColor}: ${error.message}`)
      shades[shade] = baseColor // Fallback to original color
    }
  })

  // Add the DEFAULT value (same as 500 - the base color)
  shades.DEFAULT = shades['500']

  // Add foreground color - use provided color or fallback to black
  if (foregroundColor && isValidColor(foregroundColor)) {
    shades.foreground = foregroundColor
  } else {
    shades.foreground = '#000000' // Default to black when no foreground color is provided
  }

  return shades
}

/**
 * Validate if a color is valid for shade generation
 * @param {string} color - The color to validate
 * @returns {boolean} True if valid, false otherwise
 */
function isValidColor(color) {
  return tinycolor(color).isValid()
}

export { generateColorShades, isValidColor }
