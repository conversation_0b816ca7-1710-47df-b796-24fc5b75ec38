import { writeFileSync } from 'fs'
import { join } from 'path'
import { BASE_PATHS, APPS_PATH, FILE_HEADER } from './constants.mjs'
import { parseStyleDictionary, getDirname, getBrands } from './helpers.mjs'
import { processColors, processLayout } from './token-processor.mjs'
import { buildStyleDictionaries, cleanupStyleDictionaries, formatFileWithPrettier } from './build-utils.mjs'

const __dirname = getDirname(import.meta.url)

console.log('🚀 Starting HeroUI config generation for all brands...')

// Generate the HeroUI config for a specific brand
async function generateHeroUIConfigForBrand(brand, themePaths) {
  console.log(`\n📱 Processing brand: ${brand}`)

  // Parse the style dictionary files
  const themes = {}
  for (const [themeName, filePath] of Object.entries(themePaths)) {
    themes[themeName] = await parseStyleDictionary(filePath)
  }

  // Generate the config object
  const config = {
    themes: Object.keys(themes).reduce((acc, themeName) => {
      acc[themeName] = {
        colors: {},
      }
      return acc
    }, {}),
    layout: {},
  }

  console.log('🔗 Mapping tokens to HeroUI config...')

  // Process all themes for colors
  Object.entries(themes).forEach(([themeName, themeData]) => {
    processColors(themeName, themeData, config.themes[themeName])
  })

  // Process layout once from light theme (theme-agnostic)
  if (themes.light) {
    processLayout('light', themes.light, config)
  }

  console.log('📝 Generating file content...')

  // Generate the config object with inlined values
  const configString = JSON.stringify(config, null, 2)
    .replace(/"/g, "'") // Use single quotes for string values
    .replace(/'(\w+)':/g, '$1:') // Remove quotes from property names

  return `${FILE_HEADER}export default ${configString}\n`
}

// Generate and write the config files for all brands
async function main() {
  try {
    // First, build the style dictionaries
    await buildStyleDictionaries()

    console.log('\n🎯 Generating HeroUI configs...')

    // Discover all brands
    const appsPath = join(__dirname, APPS_PATH)
    const brands = getBrands(appsPath)

    if (brands.length === 0) {
      console.log('⚠️  No brands found in apps directory')
      return
    }

    console.log(`\n🎯 Generating HeroUI configs for ${brands.length} brands...`)

    // Process each brand
    for (const brand of brands) {
      try {
        // Get brand-specific paths using the functional BASE_PATHS
        const brandPaths = BASE_PATHS(brand)
        const themePaths = {
          light: join(__dirname, brandPaths.styleDictionary.light),
          dark: join(__dirname, brandPaths.styleDictionary.dark),
        }

        // Generate config content
        const configContent = await generateHeroUIConfigForBrand(brand, themePaths)

        // Generate output path
        const outputPath = join(__dirname, brandPaths.output)

        console.log('💾 Writing file...')
        writeFileSync(outputPath, configContent, 'utf8')

        // Format the file with Prettier to fix linting issues
        await formatFileWithPrettier(outputPath)

        console.log(`✅ ${brand}: HeroUI config generated successfully!`)
        console.log(`📁 Output: ${outputPath}`)
      } catch (error) {
        console.error(`❌ Error generating HeroUI config for ${brand}:`, error)
        // Continue with other brands instead of exiting
      }
    }

    console.log('\n🎉 All brand configurations completed!')
    console.log('🎯 You can now use these configs in your HeroUI setup!')

    // Clean up temporary style dictionary files
    console.log('')
    cleanupStyleDictionaries()
  } catch (error) {
    if (error.message.includes('Style dictionary build failed')) {
      console.error('❌ Failed to build style dictionaries. HeroUI config generation aborted.')
      console.error('   Please check that your token files are valid and try again.')
    } else {
      console.error('❌ Error in main process:', error)
    }

    // Attempt cleanup even if there was an error
    console.log('')
    cleanupStyleDictionaries()

    process.exit(1)
  }
}

main()
