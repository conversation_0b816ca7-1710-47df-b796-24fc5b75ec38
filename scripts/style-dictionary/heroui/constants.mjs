// File paths configuration - function that returns paths for a given brand
const BASE_PATHS = (brand) => ({
  styleDictionary: {
    light: `../../../apps/${brand}/src/theme/.temp/style-dictionary.js`,
    dark: `../../../apps/${brand}/src/theme/.temp/style-dictionary-dark.js`
  },
  output: `../../../apps/${brand}/src/theme/heroui.config.js`
})

// Base path for discovering brands
const APPS_PATH = '../../../apps'

// Template strings
const FILE_HEADER = '// This file was auto-generated by generate-heroui-config.mjs\n// Do not edit directly - run "yarn generate-heroui-config" to regenerate\n\n'

export { BASE_PATHS, APPS_PATH, FILE_HEADER }
