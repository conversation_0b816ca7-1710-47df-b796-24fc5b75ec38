// Token mapping configuration - defines how style dictionary tokens map to HeroUI color schema
const TOKEN_MAP = {
  colors: {
    primary: {
      color: 'color.primary',
      foreground: 'color.on-primary',
    },
    secondary: {
      color: 'color.secondary',
      foreground: 'color.on-secondary',
    },
    success: {
      color: 'color.success',
      foreground: 'color.on-success',
    },
    danger: {
      color: 'color.error',
      // Note: No foreground mapping as on-error tokens don't exist in the design tokens
    },
    warning: {
      color: 'color.error', // No warning token provided yet
      // Note: No foreground mapping as on-error tokens don't exist in the design tokens
    },
    background: {
      color: 'color.background',
      isSingleValue: true, // Single value, not an object with shades
    },
    foreground: {
      color: 'color.surface.800',
      isSingleValue: true, // Single value, not an object with shades
    },
    focus: {
      color: 'color.primary-focus',
      isSingleValue: true, // Single value, not an object with shades and foreground
    },
    overlay: {
      color: 'color.surface.500',
      isSingleValue: true, // Single value, not an object with shades and foreground
    },
    default: {
      color: 'color.surface.500',
      foreground: 'color.surface.1000',
    },
    content1: {
      color: 'color.surface.100',
      foreground: 'color.surface.900',
      isDefaultOnly: true, // Only DEFAULT and foreground, no shades
    },
    content2: {
      color: 'color.surface.200',
      foreground: 'color.surface.800',
      isDefaultOnly: true, // Only DEFAULT and foreground, no shades
    },
    content3: {
      color: 'color.surface.300',
      foreground: 'color.surface.700', // Consider typo here
      isDefaultOnly: true, // Only DEFAULT and foreground, no shades
    },
    content4: {
      color: 'color.tertiary', // Don't have a quaternary
      foreground: 'color.on-tertiary',
      isDefaultOnly: true, // Only DEFAULT and foreground, no shades
    },
  },
  layout: {
    radius: {
      small: 'radius.sm',
      medium: 'radius.md',
      large: 'radius.lg',
    },
    borderWidth: {
      small: 'border.width.default', // Only a single default value coming from tokens
      medium: 'border.width.default', // Only a single default value coming from tokens
      large: 'border.width.default', // Only a single default value coming from tokens
    },
  },
}

export { TOKEN_MAP }
