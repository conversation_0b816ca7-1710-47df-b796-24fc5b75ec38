// Build and cleanup utilities for HeroUI config generation

import { unlinkSync, existsSync, rmSync } from 'fs'
import { join } from 'path'
import { spawn } from 'child_process'
import { BASE_PATHS, APPS_PATH } from './constants.mjs'
import { getBrands, getDirname } from './helpers.mjs'

const __dirname = getDirname(import.meta.url)

export function buildStyleDictionaries() {
  return new Promise((resolve, reject) => {
    console.log('🔨 Building style dictionaries needed for HeroUI config...')
    
    const buildScript = join(__dirname, '../build-global-dictionaries-esm.mjs')
    
    const build = spawn('node', [buildScript], {
      stdio: 'inherit',
      shell: true,
      cwd: join(__dirname, '../../../') // Go to project root
    })
    
    build.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Style dictionaries built successfully!')
        resolve()
      } else {
        reject(new Error(`Style dictionary build failed with exit code ${code}`))
      }
    })
    
    build.on('error', (error) => {
      reject(error)
    })
  })
}

/**
 * Clean up generated style dictionary files and temp directories
 */
export function cleanupStyleDictionaries() {
  console.log('🧹 Cleaning up temporary style dictionary files and directories...')

  // Discover all brands to clean up their files
  const appsPath = join(__dirname, APPS_PATH)
  const brands = getBrands(appsPath)

  let cleanedCount = 0

  brands.forEach(brand => {
    const brandPaths = BASE_PATHS(brand)

    // Clean up style dictionary files
    const filesToClean = [
      join(__dirname, brandPaths.styleDictionary.light),
      join(__dirname, brandPaths.styleDictionary.dark),
    ]

    filesToClean.forEach(filePath => {
      if (existsSync(filePath)) {
        try {
          unlinkSync(filePath)
          cleanedCount++
        } catch (error) {
          console.warn(`   ⚠️  Could not remove ${filePath}: ${error.message}`)
        }
      }
    })
  })

  if (cleanedCount > 0) {
    console.log(`🧹 Cleaned up ${cleanedCount} temporary files`)
  } else {
    console.log('ℹ️  No temporary files to clean up')
  }
}

/**
 * Format a file using Prettier
 * @param {string} filePath - Path to the file to format
 * @returns {Promise<void>}
 */
export function formatFileWithPrettier(filePath) {
  return new Promise((resolve, reject) => {
    const prettierProcess = spawn('npx', ['prettier', '--write', filePath], {
      stdio: 'pipe',
      shell: true,
      cwd: join(__dirname, '../../../'), // Go to project root where prettier config is
    })

    let errorOutput = ''

    prettierProcess.stderr.on('data', data => {
      errorOutput += data.toString()
    })

    prettierProcess.on('close', code => {
      if (code === 0) {
        resolve()
      } else {
        console.warn(`   ⚠️  Prettier formatting failed (exit code ${code}): ${errorOutput}`)
        // Don't reject - continue even if formatting fails
        resolve()
      }
    })

    prettierProcess.on('error', error => {
      console.warn(`   ⚠️  Could not run prettier: ${error.message}`)
      // Don't reject - continue even if formatting fails
      resolve()
    })
  })
}
