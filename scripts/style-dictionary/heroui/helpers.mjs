import { dirname } from 'path'
import { fileURLToPath } from 'url'
import { getImmediateChildren } from '../utils/getImmediateChildren.mjs'

// Utility function to capitalize strings
const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1)

// Function to parse default export from a JavaScript file and get nested values
function parseStyleDictionary(filePath) {
  console.log(`📖 Parsing file: ${filePath}`)

  // Import the module dynamically to get the default export
  const moduleUrl = `file://${filePath}`
  return import(moduleUrl)
    .then(module => {
      const tokens = module.default
      console.log(`   Found tokens object with ${Object.keys(tokens).length} top-level properties`)
      return tokens
    })
    .catch(error => {
      console.error(`Error importing ${filePath}:`, error)
      throw error
    })
}

// Function to get nested value from object using dot notation
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// Helper function to get __dirname in ES modules
function getDirname(importMetaUrl) {
  const __filename = fileURLToPath(importMetaUrl)
  return dirname(__filename)
}

// Function to discover all brands in the apps directory
function getBrands(appsPath) {
  const brands = getImmediateChildren(appsPath, { directoriesOnly: true })
  console.log(`🔍 Discovered brands: ${brands.join(', ')}`)
  return brands
}

export { 
  capitalize, 
  parseStyleDictionary, 
  getNestedValue, 
  getDirname,
  getBrands
}
