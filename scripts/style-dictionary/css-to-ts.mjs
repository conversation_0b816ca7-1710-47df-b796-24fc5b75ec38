import fs from 'fs'
import { getImmediateChildren } from './utils/getImmediateChildren.mjs'

const brands = getImmediateChildren('apps', { directoriesOnly: true })

brands.forEach(brand => {
  const inputPath = `apps/${brand}/src/theme/style-dictionary.css`
  const outputPath = inputPath.replace(/\.css$/, '.ts')

  if (!fs.existsSync(inputPath)) {
    console.warn(`Input file not found: ${inputPath}`)
    return
  }

  const css = fs.readFileSync(inputPath, 'utf-8')

  const varRegex = /--[\w-]+:\s*[^;]+;/g
  const darkBlockRegex = /\.dark\s*{([^}]*)}/m

  const vars = {}
  let darkVars = {}

  const rootBlock = css.match(/:root\s*{([^}]*)}/m)
  if (rootBlock) {
    const matches = rootBlock[1].match(varRegex) || []
    matches.forEach(line => {
      const [key, value] = line.split(':').map(s => s.trim().replace(';', ''))
      vars[key] = value
    })
  }

  const darkBlock = css.match(darkBlockRegex)
  if (darkBlock) {
    const matches = darkBlock[1].match(varRegex) || []
    matches.forEach(line => {
      const [key, value] = line.split(':').map(s => s.trim().replace(';', ''))
      darkVars[key] = value
    })
  }

  const ts = `// This file is auto-generated from ${inputPath.split('/').pop()}
// Do not edit directly. Update the CSS source and regenerate if needed.

export const styleDictionaryVars = ${JSON.stringify(vars, null, 2)
  .replace(/"/g, "'")
  .replace(/''/g, "'")}

export const styleDictionaryVarsDark = {
  ...styleDictionaryVars,
  ${Object.entries(darkVars)
    .map(([k, v]) => `'${k}': '${v.replace(/'/g, "\\'")}'`)
    .join(',\n  ')}
}
`

  fs.writeFileSync(outputPath, ts, 'utf-8')
  console.log(`Generated: ${outputPath}`)
})
