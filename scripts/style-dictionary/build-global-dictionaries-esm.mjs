import StyleDictionary from 'style-dictionary'
import { register } from '@tokens-studio/sd-transforms'
import { getImmediateChildren } from './utils/getImmediateChildren.mjs'

register(StyleDictionary)

function generateVariablesDictionaryConfig(isDark) {
  const fileNameSuffix = isDark ? '-dark' : ''
  return [
    {
      destination: `style-dictionary${fileNameSuffix}.js`,
      format: "javascript/esm",
      options: { minify: true },
      filter: token => !token.filePath.includes('/components/'),
    },
  ]
}

function getStyleDictionaryConfig(brand, isDark) {
  return {
    include: isDark
      ? ['scripts/style-dictionary/tokens/brands/core/core-dark.json']
      : [
          'scripts/style-dictionary/tokens/brands/core/core-base.json',
          'scripts/style-dictionary/tokens/brands/core/core-light.json',
        ],
    source: isDark
      ? [`scripts/style-dictionary/tokens/brands/${brand}/${brand}-dark.json`]
      : [
          `scripts/style-dictionary/tokens/brands/${brand}/${brand}-base.json`,
          `scripts/style-dictionary/tokens/brands/${brand}/${brand}-light.json`,
        ],
    preprocessors: ['tokens-studio'],
    platforms: {
      js: {
        transformGroup: 'js',
        buildPath: `apps/${brand}/src/theme/.temp/`,
        files: [...generateVariablesDictionaryConfig(isDark)],
      },
    },
    outputReferences: true,
  }
}

;[...getImmediateChildren('scripts/style-dictionary/tokens/brands')].map(function (brand) {
  ;['light', 'dark'].forEach(function (theme) {
    ;['js'].forEach(function (platform) {
      const sd = new StyleDictionary(getStyleDictionaryConfig(brand, theme === 'dark'))
      sd.buildPlatform(platform)
    })
  })
})
