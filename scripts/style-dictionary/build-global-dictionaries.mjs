import StyleDictionary from 'style-dictionary'
import { formats } from 'style-dictionary/enums'
import { register } from '@tokens-studio/sd-transforms'
import { getImmediateChildren } from './utils/getImmediateChildren.mjs'

register(StyleDictionary)

const { cssVariables } = formats

const isDark = process.argv.includes('dark')

function generateVariablesDictionaryConfig(isDark) {
  const fileNameSuffix = isDark ? '-dark' : ''
  return [
    {
      destination: `style-dictionary${fileNameSuffix}.css`,
      format: cssVariables,
      filter: token => !token.filePath.includes('/components/'),
    },
  ]
}

function getStyleDictionaryConfig(brand, isDark) {
  return {
    include: isDark
      ? ['scripts/style-dictionary/tokens/brands/core/core-dark.json']
      : [
          'scripts/style-dictionary/tokens/brands/core/core-base.json',
          'scripts/style-dictionary/tokens/brands/core/core-light.json',
        ],
    source: isDark
      ? [`scripts/style-dictionary/tokens/brands/${brand}/${brand}-dark.json`]
      : [
          `scripts/style-dictionary/tokens/brands/${brand}/${brand}-base.json`,
          `scripts/style-dictionary/tokens/brands/${brand}/${brand}-light.json`,
        ],
    preprocessors: ['tokens-studio'],
    platforms: {
      web: {
        transforms: ['name/kebab'],
        transformGroup: 'tokens-studio',
        buildPath: `apps/${brand}/src/theme/`,
        files: [...generateVariablesDictionaryConfig(isDark)],
      },
    },
    outputReferences: true,
  }
}

;[...getImmediateChildren('scripts/style-dictionary/tokens/brands')].map(function (brand) {
  ;['web'].forEach(function (platform) {
    const sd = new StyleDictionary(getStyleDictionaryConfig(brand, isDark))
    sd.buildPlatform(platform)
  })
})