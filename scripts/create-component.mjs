#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import readline from 'readline'
import { fileURLToPath } from 'url'
import { spawnSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

function isPascalCase(str) {
  return /^[A-Z][a-zA-Z0-9]*$/.test(str)
}

function isValidRelativeDirPath(dir) {
  return !path.isAbsolute(dir) && !dir.startsWith('/') && /^[a-zA-Z0-9/_\-]+\/?$/.test(dir)
}

function toPascalCase(str) {
  return str
    .replace(/[-_\s]+(.)?/g, (_, c) => c ? c.toUpperCase() : '')
    .replace(/^(.)/, (m) => m.toUpperCase())
}

function prompt(question) {
  return new Promise(resolve => rl.question(question, resolve))
}

const DEFAULT_DIR = 'apps/core/src/components/'

function getModuleScssContent() {
  return [
    "@use '@theme/variables.scss' as *;",
    '',
    '.container {',
    '  background: $color-primary;',
    '}',
  ].join('\n')
}

function getTsxContent(componentName, relativePath) {
  return [
    "import type { FC } from 'react'",
    `import styles from '@components/${relativePath}/${componentName}.module.scss'`,
    '',
    `interface I${componentName}Props {`,
    '  placeholderProp?: string',
    '}',
    '',
    `const ${componentName}: FC<I${componentName}Props> = ({ placeholderProp, ...props }) => {`,
    `  console.log('${componentName} props:', props)`,
    '  return (',
    '    <div className={styles.container}>',
    `      <h2>a ${componentName} placeholder</h2>`,
    '    </div>',
    '  )',
    '}',
    '',
    `export default ${componentName}`,
    '',
  ].join('\n')
}

function getIndexContent(componentName, relativePath) {
  return [
    `export { default as ${componentName} } from '@components/${relativePath}/${componentName}'`,
    `export * from '@components/${relativePath}/${componentName}'`,
    '',
  ].join('\n')
}

function getStoriesContent(componentName, relativePath) {
  return [
    `// TEMP: Placeholder Story - Remove comment after implementation`,
    `import { ${componentName} } from '@components/${relativePath}'`,
    `import type { Meta, StoryObj } from '@storybook/nextjs'`,
    '',
    `const meta: Meta<typeof ${componentName}> = {`,
    `  title: '${componentName}',`,
    `  component: ${componentName},`,
    `  tags: ['autodocs'],`,
    `  args: {`,
    `    placeholderProp: 'placeholder value',`,
    `  },`,
    `  argTypes: {`,
    `    placeholderProp: {`,
    `      control: 'text',`,
    `      description: 'Placeholder prop description',`,
    `    },`,
    `  },`,
    `}`,
    '',
    `export default meta`,
    '',
    `export const Default: StoryObj<typeof ${componentName}> = {`,
    `  parameters: {`,
    `    docs: {`,
    `      description: {`,
    `        story: 'Default ${componentName} component',`,
    `      },`,
    `    },`,
    `  },`,
    `}`,
    '',
    `export const Playground: StoryObj<typeof ${componentName}> = {`,
    `  parameters: {`,
    `    docs: {`,
    `      description: {`,
    `        story: 'Interactive playground for ${componentName} component',`,
    `      },`,
    `    },`,
    `  },`,
    `  args: {`,
    `    placeholderProp: 'Interactive placeholder',`,
    `  },`,
    `  render: args => (`,
    `    <div>`,
    `      <${componentName} {...args} />`,
    `    </div>`,
    `  ),`,
    `}`,
    '',
  ].join('\n')
}

async function createComponent() {
  const componentName = await prompt('Enter component name (PascalCase): ')
  if (!isPascalCase(componentName)) {
    console.error('❌ Error: Component name must be in PascalCase (e.g., MyComponent)')
    createComponent()
    return
  }

  const _targetDirInput = await prompt(
    `Enter a custom directory relative to [${DEFAULT_DIR}] to save the component, or, leave empty to use default dir: `,
  )
  const targetDirInput = _targetDirInput.trim()
  const isAbsolutePathProvided = targetDirInput.startsWith('apps/') || targetDirInput.startsWith('/apps/')
  const userTargetDir = isAbsolutePathProvided ? targetDirInput : path.join(DEFAULT_DIR, targetDirInput)
  const userTargetDirTrimmed = userTargetDir.replace(/^\//, '') // Remove trailing slash if exists
  const targetDir = userTargetDirTrimmed || DEFAULT_DIR

  if (!isValidRelativeDirPath(targetDir)) {
    console.error(
      '❌ Error: Directory path must be relative, contain only letters/numbers/-/_/, and end with a slash (e.g., apps/core/src/components/)',
    )
    createComponent()
    return
  }

  const basePath = path.resolve(__dirname, '..')
  const componentPath = path.join(basePath, targetDir, componentName)

  fs.mkdirSync(componentPath, { recursive: true })

  const pascalName = toPascalCase(componentName)

  const relativeComponentPath = path
    .join(targetDir, componentName)
    .replace(DEFAULT_DIR, '')
    .replace(/\\/g, '/')
    .replace(/\/$/, '')

  const isRootComponent = targetDir === DEFAULT_DIR

  const files = [
    {
      path: path.join(componentPath, `${componentName}.tsx`),
      content: getTsxContent(componentName, relativeComponentPath),
    },
    {
      path: path.join(componentPath, `${componentName}.module.scss`),
      content: getModuleScssContent(),
    },
    {
      path: path.join(componentPath, 'index.ts'),
      content: getIndexContent(componentName, relativeComponentPath),
    },
    ...(isRootComponent ? [{
      path: path.join(componentPath, `${componentName}.stories.tsx`),
      content: getStoriesContent(componentName, relativeComponentPath),
    }] : []),
  ]

  files.forEach(file => {
    fs.writeFileSync(file.path, file.content)
    console.log(`📝 Created file: ${file.path}`)
    spawnSync('code', [file.path], { stdio: 'inherit' })
  })

  console.log('✅ Component scaffold created successfully.')
  rl.close()
}

createComponent()
