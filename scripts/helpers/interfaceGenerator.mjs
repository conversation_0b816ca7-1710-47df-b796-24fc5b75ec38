// Function to generate TypeScript interfaces from JSON
export function generateInterface(name, json) {
  const lines = []

  // Helper function to determine TypeScript type
  function getType(value) {
    if (Array.isArray(value)) {
      if (value.length > 0) {
        const elementType = getType(value[0])
        if (typeof value[0] === 'object' && value[0] !== null && !Array.isArray(value[0])) {
          return `Array<${elementType}>`
        }
        return `${elementType}[]`
      } else {
        return 'any[]'
      }
    } else if (value === null) {
      return 'null'
    } else if (typeof value === 'object') {
      return `{
${processObject(value, 2)}
${' '.repeat(2)}}`
    } else if (typeof value === 'string') {
      return 'string'
    } else if (typeof value === 'number') {
      return 'number'
    } else if (typeof value === 'boolean') {
      return 'boolean'
    } else {
      return 'any'
    }
  }

  // Updated to handle keys with spaces
  function processObject(obj, indent = 2) {
    const keys = Object.keys(obj)
    return keys
      .map(key => {
        const quotedKey = key.includes(' ') ? `'${key}'` : key // Quote keys with spaces
        const value = obj[key]
        const type =
          typeof value === 'object' && value !== null && !Array.isArray(value)
            ? `{
${processObject(value, indent + 2)}
${' '.repeat(indent)}}`
            : getType(value)
        return `${' '.repeat(indent)}${quotedKey}: ${type};`
      })
      .join('\n')
  }

  // Generate the interface
  lines.push(`export type ${name} = {`)
  lines.push(processObject(json, 2))
  lines.push('}')

  return lines.join('\n')
}
