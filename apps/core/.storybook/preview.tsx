import '@/app/globals.css'
import '@/app/globals.scss'
import '@theme/_variables.scss'
import '@theme/style-dictionary.css'
import { PrimaryFont, SecondaryFont } from '@app/layout.settings'
import type { Decorator } from '@storybook/react'
import { PROVIDERS_DECORATOR } from '../../../.storybook/global-decorators'
import { THEME_TYPE } from '../../../.storybook/global-types'

export const globalTypes = {
  theme: THEME_TYPE,
}

export const decorators: Decorator[] = [
  (Story, context) => {
    const theme = context.globals.theme
    document.documentElement.classList.remove('dark', 'light')
    document.documentElement.classList.add(theme)
    return <Story />
  },
  Story => {
    return (
      <div className={`${PrimaryFont.className} ${SecondaryFont.className}`}>
        <Story />
      </div>
    )
  },
  PROVIDERS_DECORATOR,
]
