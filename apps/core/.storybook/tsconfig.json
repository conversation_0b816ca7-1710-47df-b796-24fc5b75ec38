{"extends": "../../../.storybook/tsconfig.json", "compilerOptions": {"baseUrl": "../src", "paths": {"@app/*": ["./app/*"], "@screens/*": ["./components/@screens/*"], "@modals/*": ["./components/@modals/*"], "@widgets/*": ["./components/@widgets/*"], "@components/*": ["./components/*"], "@modules/*": ["./modules/*"], "@theme/*": ["./theme/*"], "@/*": ["./*"]}}, "include": ["../src/**/*", "../**/*.stories.*", "../**/*.story.*", "../.storybook/*.ts", "../.storybook/*.tsx"], "exclude": ["../node_modules"]}