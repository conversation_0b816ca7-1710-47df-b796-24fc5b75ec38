{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
    "plugins": [
      {
        "name": "next"
      },
      {
        "name": "typescript-plugin-css-modules",
        "options": {
          "classnameTransform": "camelCaseOnly"
        }
      }
    ],
    "baseUrl": "./src",
    "paths": {
      "@app/*": [
        "./app/*"
      ],
      "@screens/*": [
        "./components/@screens/*"
      ],
      "@modals/*": [
        "./components/@modals/*"
      ],
      "@widgets/*": [
        "./components/@widgets/*"
      ],
      "@components/*": [
        "./components/*"
      ],
      "@modules/*": [
        "./modules/*"
      ],
      "@theme/*": [
        "./theme/*"
      ],
      "@constants/*": [
        "./constants/*",
      ],
      // fallback alias
      "@/*": [
        "./*"
      ],
    },
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    "global.d.ts",
    "next.config.ts",
    ".next/types/**/*.ts",
    "/types/*.ts",
  ],
  "exclude": [
    "node_modules"
  ]
}