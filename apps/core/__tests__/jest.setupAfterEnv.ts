// Jest setup file for additional matchers and global test setup
import '@testing-library/jest-dom'
import { beforeEach, jest } from '@jest/globals'

// Mock next-auth to prevent ES module import issues
jest.mock('next-auth', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    handlers: {},
    auth: jest.fn(),
    signIn: jest.fn(),
    signOut: jest.fn(),
  })),
  CredentialsSignin: class CredentialsSignin extends Error {
    code = 'custom'
  },
}))

jest.mock('next-auth/react', () => ({
  getSession: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn(),
  useSession: jest.fn(() => ({ data: null, status: 'unauthenticated' })),
}))

jest.mock('next-auth/providers/credentials', () => ({
  __esModule: true,
  default: jest.fn(),
}))

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks()
})

// Mock console.warn to avoid output noise during tests
console.warn = jest.fn()
global.console.warn = jest.fn()

// Add any other global setup for tests here
