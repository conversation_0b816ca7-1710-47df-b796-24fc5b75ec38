/* eslint-disable no-undef */
import bundleAnalyzer from '@next/bundle-analyzer'
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    minimumCacheTTL: 60 * 5, // 5 minutes
    deviceSizes: [640, 1080, 1920],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 's3.eu-central-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: '*.imgix.net',
      },
    ],
  },
  experimental: {
    useCache: true,
    optimizePackageImports: [
      '@repo/ui',
      '@repo/api',
      '@repo/constants',
      '@repo/helpers',
      'react-blurhash',
      'lodash-es',
      'motion/react',
      'framer-motion',
      'lucide-react',
      '@heroui/*',
      '@radix-ui/*',
      'class-variance-authority',
      'tailwind-merge',
      'clsx',
    ],
    staleTimes: {
      dynamic: 30,
      static: 180,
    },
    optimizeCss: true,
    webpackMemoryOptimizations: true,
  },
  // logging: {
  //   fetches: {
  //     fullUrl: true,
  //   },
  // },
  sassOptions: {
    //  prependData: "@use '@theme/functions.scss' as *;\n@use '@theme/variables.scss' as *;\n",
  },
}

const withBundleAnalyzer = bundleAnalyzer({
  // eslint-disable-next-line turbo/no-undeclared-env-vars
  enabled: process.env.ANALYZE === 'true',
})

export default withBundleAnalyzer(nextConfig)
