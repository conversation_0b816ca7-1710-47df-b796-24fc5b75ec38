{"name": "core", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "open http://localhost:3000 && npx next dev --turbopack --port 3000", "dev:staging": "npx env-cmd -f .env.staging yarn dev", "dev:alpha": "npx env-cmd -f .env.alpha yarn dev", "dev:msw": "USE_MSW=true npx yarn dev", "build": "NODE_ENV=production npx next build", "start": "NODE_ENV=production npx next start", "lint": "npx next lint", "clear-cache": "rm -rf ./.next", "check-types": "npx tsc --noEmit", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test npx jest --watch", "test:coverage": "NODE_ENV=test npx jest --coverage", "storybook": "storybook dev -p 6006 -c .storybook"}, "dependencies": {"@repo/api": "*", "@repo/constants": "*", "@repo/helpers": "*", "@repo/hooks": "*", "@repo/types": "*", "@repo/ui": "*"}, "devDependencies": {"dotenv": "^16.5.0", "jest": "^30.0.4"}}