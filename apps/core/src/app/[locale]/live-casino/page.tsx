import { withSuspense } from '@/HOC/withSuspense'
import type { PageProps } from '@/types/nextjs'
import { PageContainer, PageTitle } from '@modules/page'
import { LobbyType } from '@repo/constants/common/lobby'
import { LobbyScreen } from '@screens/lobby/LobbyScreen'

export const revalidate = 10

export default async function LiveCasinoPage({ params }: PageProps) {
  const { locale } = await params

  return (
    <PageContainer>
      <PageTitle title={'Live Casino Page'} />
      {withSuspense(<LobbyScreen locale={locale} type={LobbyType.LiveCasino} />)}
    </PageContainer>
  )
}
