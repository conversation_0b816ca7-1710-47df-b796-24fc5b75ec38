import { getHomePageConfig } from '@/network/server-utils/s3/getters'
import type { PageProps } from '@/types/nextjs'
import DynamicContentRenderer from '@components/DynamicContentRenderer/DynamicContentRenderer'
import { PageContainer } from '@modules/page'

export const revalidate = 10

export default async function HomePage({ params }: PageProps) {
  const { locale } = await params
  const config = await getHomePageConfig()

  if (!config) {
    console.error('[HomePage] Configuration not found')
    return null
  }

  return (
    <PageContainer>
      <DynamicContentRenderer config={config} locale={locale} />
    </PageContainer>
  )
}
