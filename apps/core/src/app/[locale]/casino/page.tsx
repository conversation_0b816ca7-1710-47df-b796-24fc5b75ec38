import { withSuspense } from '@/HOC/withSuspense'
import type { PageProps } from '@/types/nextjs'
import { PageContainer, PageTitle } from '@modules/page'
import { LobbyType } from '@repo/constants/common/lobby'
import { LobbyScreen } from '@screens/lobby/LobbyScreen'

export const revalidate = 10

export default async function CasinoPage({ params }: PageProps) {
  const { locale } = await params
  // const isAuthenticated = await authService.isAuthenticated()
  return (
    <>
      <PageContainer>
        <PageTitle title={'Casino Page'} />
        {withSuspense(<LobbyScreen locale={locale} type={LobbyType.Casino} />)}
      </PageContainer>
    </>
  )
}
