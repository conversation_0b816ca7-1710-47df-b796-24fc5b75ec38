import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { cacheService } from '@/services/Cache.service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const detailed = searchParams.get('detailed') === 'true'

    if (detailed) {
      const detailedStats = cacheService.getDetailedStats()

      return NextResponse.json({
        success: true,
        stats: detailedStats,
      })
    } else {
      const stats = cacheService.getStats()

      return NextResponse.json({
        success: true,
        stats,
      })
    }
  } catch (error) {
    console.error('Error getting cache stats:', error)
    return NextResponse.json({ success: false, error: 'Failed to get cache stats' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')

    if (type === 'games') {
      //   invalidateGamesCache()
      return NextResponse.json({
        success: false,
        message: 'Not implemented yet',
      })
    } else if (type === 'all') {
      cacheService.clearAll()
      return NextResponse.json({
        success: true,
        message: 'All cache cleared',
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid type parameter. Use "games", "all", "stale", or "stale-list"' },
        { status: 400 },
      )
    }
  } catch (error) {
    console.error('Error invalidating cache:', error)
    return NextResponse.json({ success: false, error: 'Failed to invalidate cache' }, { status: 500 })
  }
}
