import { NextResponse } from 'next/server'
import { withCache } from '@/services/Cache.service'

export async function GET() {
  try {
    // Simulate an expensive operation that takes time
    const result = await withCache(
      'demo-data',
      async () => {
        console.log('[Demo] Simulating expensive operation...')

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 2000))

        return {
          data: `Fresh data generated at ${new Date().toISOString()}`,
          timestamp: Date.now(),
          random: Math.random(),
        }
      },
      10,
    )

    return NextResponse.json({
      success: true,
      cached: result,
      explanation:
        'First request will be slow (2s), subsequent requests within 10s will be fast, ' +
        'requests after 10s will return stale data instantly and refresh in background. ' +
        'Data never expires automatically.',
    })
  } catch (error) {
    console.error('Error in demo route:', error)
    return NextResponse.json({ success: false, error: 'Demo failed' }, { status: 500 })
  }
}
