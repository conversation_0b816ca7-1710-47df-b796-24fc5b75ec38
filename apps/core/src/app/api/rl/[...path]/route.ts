import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { envVars } from '@/env'
import { checkApiRateLimit, getRequestClientId, validateRequestPath } from '@/utils/server/proxy'

const MAX_REQUEST_BODY_SIZE = 10 * 1024 * 1024 // 10MB
const REQUEST_TIMEOUT_MS = 30000

export async function GET(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, params)
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, params)
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, params)
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, params)
}

export async function PATCH(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, params)
}

async function handleRequest(request: NextRequest, params: Promise<{ path: string[] }>) {
  try {
    // Security: Rate limiting
    const clientId = getRequestClientId(request)
    const clientIp = clientId.split(':')[0]
    if (!checkApiRateLimit(clientId)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded', code: 'RATE_LIMIT_EXCEEDED' },
        { status: 429, headers: { 'Retry-After': '60' } },
      )
    }

    const { path } = await params

    // Security: Validate path to prevent path traversal and injection attacks
    if (!validateRequestPath(path)) {
      return NextResponse.json({ error: 'Invalid path', code: 'INVALID_PATH' }, { status: 400 })
    }

    const targetUrl = `${envVars.RL_API_URL}/${path.join('/')}`
    const url = new URL(targetUrl)

    // Security: Sanitize and validate query parameters
    const searchParams = request.nextUrl.searchParams
    for (const [key, value] of searchParams.entries()) {
      // Basic validation: reject potentially dangerous parameter names/values
      if (key.includes('<') || key.includes('>') || value.includes('<script')) {
        return NextResponse.json({ error: 'Invalid query parameters', code: 'INVALID_PARAMS' }, { status: 400 })
      }
      url.searchParams.set(key, value)
    }

    const headers = new Headers()
    headers.set('X-Api-Key', envVars.RL_API_KEY)

    // Security: Allowlist of headers to forward (prevent header injection)
    const allowedHeaders = [
      'content-type',
      'accept',
      'accept-language',
      'accept-encoding',
      'user-agent',
      'authorization', // Only if you need client auth
      'x-requested-with',
    ]

    // First, validate all headers before processing
    for (const [key, value] of request.headers.entries()) {
      const normalizedKey = key.toLowerCase()
      if (normalizedKey !== 'host' && allowedHeaders.includes(normalizedKey)) {
        // Additional validation for specific headers
        if (normalizedKey === 'content-type' && !value.startsWith('application/json') && value.includes('<script')) {
          return NextResponse.json({ error: 'Invalid content-type', code: 'INVALID_HEADER' }, { status: 400 })
        }
        headers.set(key, value)
      }
    }

    headers.set('X-Forwarded-For', clientIp || '')
    headers.set('X-Proxy-Source', 'rhino-next')

    const requestOptions: RequestInit = {
      method: request.method,
      headers,
    }

    // Security: Limit body size and validate content for non-GET/HEAD requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      try {
        const body = await request.text()

        // Security: Limit body size (10MB)
        if (body.length > MAX_REQUEST_BODY_SIZE) {
          return NextResponse.json({ error: 'Request body too large', code: 'BODY_TOO_LARGE' }, { status: 413 })
        }

        // Security: Basic validation for malicious content
        if (body.includes('<script') || body.includes('javascript:')) {
          return NextResponse.json({ error: 'Invalid request body', code: 'INVALID_BODY' }, { status: 400 })
        }

        requestOptions.body = body
      } catch (error) {
        return NextResponse.json({ error: 'Invalid request body', code: 'BODY_PARSE_ERROR' }, { status: 400 })
      }
    }

    // Security: Set timeout for the request to prevent hanging
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT_MS)
    requestOptions.signal = controller.signal

    let response: Response
    try {
      response = await fetch(url.toString(), requestOptions)
    } catch (error: any) {
      clearTimeout(timeoutId)
      if (error.name === 'AbortError') {
        return NextResponse.json({ error: 'Request timeout', code: 'TIMEOUT' }, { status: 504 })
      }
      throw error
    } finally {
      clearTimeout(timeoutId)
    }

    // Security: Filter response headers
    const responseHeaders = new Headers()
    const allowedResponseHeaders = [
      'content-type',
      'content-length',
      'cache-control',
      'expires',
      'last-modified',
      'etag',
    ]

    response.headers.forEach((value, key) => {
      const normalizedKey = key.toLowerCase()
      if (allowedResponseHeaders.includes(normalizedKey)) {
        responseHeaders.set(key, value)
      }
    })

    // Security: Add security headers to response
    responseHeaders.set('X-Content-Type-Options', 'nosniff')
    responseHeaders.set('X-Frame-Options', 'DENY')
    responseHeaders.set('X-XSS-Protection', '1; mode=block')

    if (process.env.NODE_ENV === 'development') {
      console.log('Proxy request:', {
        method: request.method,
        path: path.join('/'),
        targetUrl: url.toString(),
        clientIp: clientIp,
      })
    }

    return new NextResponse(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    })
  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json({ error: 'Internal server error', code: 'INTERNAL_ERROR' }, { status: 500 })
  }
}
