import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { WinnerCard } from '@modules/winners/components'

const meta: Meta<typeof WinnerCard> = {
  title: 'Modules/Winners/WinnerCard',
  component: WinnerCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    username: {
      control: 'text',
      description: 'The username of the winner',
    },
    winAmount: {
      control: 'number',
      description: 'The amount won by the user',
    },
    gameImageSrc: {
      control: 'text',
      description: 'The source URL for the game thumbnail image',
    },
    currency: {
      control: 'text',
      description: 'The currency symbol',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    username: 'johnwa...',
    winAmount: 109500,
    gameImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    currency: '$',
  },
}

export const LongGameName: Story = {
  args: {
    username: 'player123...',
    winAmount: 25009,
    gameImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    currency: '$',
  },
}

export const SmallWin: Story = {
  args: {
    username: 'winner...',
    winAmount: 14000,
    gameImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    currency: '$C',
  },
}

export const EurosCurrency: Story = {
  args: {
    username: 'europa...',
    winAmount: 890000,
    gameImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/vampire.png',
    currency: 'SC',
  },
}

// Showcase multiple winner cards in a horizontal layout
export const WinnersShowcase: Story = {
  render: () => {
    const winners = [
      {
        username: 'johnwa',
        winAmount: 109500,
        gameImageSrc:  'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
      },
      {
        username: 'player',
        winAmount: 109500,
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/milky-ways.png',
      },
      {
        username: 'winner',
        winAmount: 109500,
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/vampires-vs-wolves.png',
      },
      {
        username: 'lucky',
        winAmount: 109500,
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/winter-wonders.png',
      },
      {
        username: 'gamer',
        winAmount: 109500,
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gods-of-giza.png',
      },
    ]

    return (
      <div
        style={{
          display: 'flex',
          gap: '8px',
          overflowX: 'auto',
          padding: '20px',
          maxWidth: '800px',
        }}>
        {winners.map((winner, index) => (
          <div key={index} style={{ flexShrink: 0 }}>
            <WinnerCard {...winner} />
          </div>
        ))}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          'Multiple winner cards displayed in a horizontal scrolling layout, similar to how they appear in the winners widget.',
      },
    },
  },
}
