import Image from 'next/image'
import { Card } from '@components/Card'
import styles from '@modules/winners/components/WinnerCard.module.scss'
import { Avatar } from '@heroui/avatar'
import { formatCompact } from '@repo/helpers/formatHelpers'

export interface IWinnerCardProps {
  username: string
  winAmount: number
  gameImageSrc: string
  currency?: string
}

const WinnerCard = ({ username, winAmount, gameImageSrc, currency = '$' }: IWinnerCardProps) => {
  return (
    <Card className={styles.winnerCard} aria-label="Winner Card">
      <div className={styles.imageContainer}>
        <Image src={gameImageSrc} alt="winners game thumbnail" width={70} height={70} className={styles.thumbnail} />
      </div>

      <div className={styles.content}>
        <div>
          <p className={styles.username}>{username}</p>
        </div>

        <div className={styles.currencyAmount}>
          <div>
            <Avatar
              size="sm"
              name={currency.slice(0, 2)}
              classNames={{ base: styles.avatar, name: styles.avatarName }}
            />
          </div>
          <p className={styles.winAmount}>{formatCompact(winAmount)}</p>
        </div>
      </div>
    </Card>
  )
}

export default WinnerCard
