import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import styles from '@modules/promotions/PromotionsWidget.module.scss'

const PromotionsWidget: FC<IDynamicallyRenderedContentProps> = () => {
  return <div className={styles.container}>PromotionsWidget</div>
}

export default PromotionsWidget
