'use client'
import React from 'react'
import { clsx } from 'clsx'
import { Controller } from 'react-hook-form'
import { Alert } from '@heroui/alert'
import { Button } from '@heroui/button'
import { Form } from '@heroui/form'
import { Input } from '@heroui/input'
import { useIsAuthenticated } from '@modules/auth'
import { useLoginForm } from '@modules/auth/hooks/useLoginForm'
import styles from '@modules/auth/components/LoginForm.module.scss'

interface ILoginFormProps {
  onSuccess?: VoidFunction
}

export const LoginForm: React.FC<ILoginFormProps> = ({ onSuccess }) => {
  const { form, loginError, pending, handleSubmit } = useLoginForm({ onSuccess })
  const isAuthenticated = useIsAuthenticated()

  return (
    <Form onSubmit={form.handleSubmit(handleSubmit)} className={clsx(styles.form)} validationBehavior="aria">
      <div className="flex w-full flex-wrap gap-4">
        <Controller
          name="email"
          control={form.control}
          render={({ field }) => {
            return (
              <Input
                label="Email"
                type="email"
                autoComplete="email"
                variant="bordered"
                {...field}
                {...form.register('email')}
                onClear={() => {
                  field.onChange('')
                }}
                errorMessage={form.formState.errors['email']?.message}
                isInvalid={!!form.formState.errors['email']?.message}
                isClearable
              />
            )
          }}
        />
        <Controller
          name="password"
          control={form.control}
          render={({ field }) => (
            <Input
              label="Password"
              type="password"
              variant="bordered"
              autoComplete="current-password"
              {...field}
              {...form.register('password')}
              onClear={() => {
                field.onChange('')
              }}
              errorMessage={form.formState.errors['password']?.message}
              isInvalid={!!form.formState.errors['password']?.message}
              isClearable
            />
          )}
        />
        {!!loginError && (
          <div className="flex flex-col gap-4 w-full">
            <Alert color="danger" variant="solid" description={loginError} title={'Login Error'} />
          </div>
        )}
        <Button color="secondary" type="submit" isLoading={pending} disabled={isAuthenticated}>
          Login
        </Button>
      </div>
    </Form>
  )
}
