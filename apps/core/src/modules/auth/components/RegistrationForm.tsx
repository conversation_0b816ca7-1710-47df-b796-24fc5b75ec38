'use client'
import React from 'react'
import { clsx } from 'clsx'
import { Form } from '@heroui/form'
import { useIsAuthenticated } from '@modules/auth'
import { RegistrationStep1 } from '@modules/auth/components/RegistrationStep1'
import { RegistrationStep2 } from '@modules/auth/components/RegistrationStep2'
import { useRegistrationForm } from '@modules/auth/hooks/useRegistrationForm'
import styles from '@modules/auth/components/RegistrationForm.module.scss'

interface IRegistrationFormProps {
  onSuccess?: VoidFunction
}

export const RegistrationForm: React.FC<IRegistrationFormProps> = ({ onSuccess }) => {
  const { form, currentStep, registerError, pending, handleSubmit, handleNextStep, handlePrevStep } =
    useRegistrationForm({
      onSuccess,
    })
  const isAuthenticated = useIsAuthenticated()

  return (
    <Form onSubmit={form.handleSubmit(handleSubmit)} className={clsx(styles.form)} validationBehavior="aria">
      <div className={styles.formContent}>
        {currentStep === 1 && (
          <RegistrationStep1 form={form} pending={pending} registerError={registerError} onSubmit={handleNextStep} />
        )}

        {currentStep === 2 && (
          <RegistrationStep2 form={form} pending={pending} registerError={registerError} onBack={handlePrevStep} />
        )}
      </div>
    </Form>
  )
}
