'use client'
import React from 'react'
import { Controller } from 'react-hook-form'
import type { UseFormReturn } from 'react-hook-form'
import type { IRegistrationSchema } from '@/modules/auth'
import { Alert } from '@heroui/alert'
import { Button } from '@heroui/button'
import { Checkbox } from '@heroui/checkbox'
import { Input } from '@heroui/input'
import styles from '@modules/auth/components/RegistrationForm.module.scss'

interface IRegistrationStep1Props {
  form: UseFormReturn<IRegistrationSchema>
  pending: boolean
  registerError: string
  onSubmit: () => void
}

export const RegistrationStep1: React.FC<IRegistrationStep1Props> = ({ form, pending, registerError, onSubmit }) => {
  const { watch, setValue } = form
  const hasPromoCode = watch('hasPromoCode')

  return (
    <>
      <Controller
        name="email"
        control={form.control}
        render={({ field }) => (
          <Input
            label="Enter your e-mail"
            type="email"
            autoComplete="email"
            variant="bordered"
            isClearable
            {...field}
            onClear={() => {
              field.onChange('')
            }}
            errorMessage={form.formState.errors.email?.message}
            isInvalid={!!form.formState.errors.email?.message}
          />
        )}
      />

      <Controller
        name="password"
        control={form.control}
        render={({ field }) => (
          <Input
            label="Create password"
            type="password"
            autoComplete="new-password"
            variant="bordered"
            isClearable
            {...field}
            onClear={() => {
              field.onChange('')
            }}
            errorMessage={form.formState.errors.password?.message}
            isInvalid={!!form.formState.errors.password?.message}
          />
        )}
      />

      {/* Promo code section */}
      <Controller
        name="hasPromoCode"
        control={form.control}
        render={({ field }) => (
          <Checkbox
            isSelected={field.value}
            onValueChange={checked => {
              field.onChange(checked)
              if (!checked) {
                setValue('promoCode', '')
              }
            }}>
            <span className={styles.checkboxText}>Dont have a code? Have the best one on us</span>
          </Checkbox>
        )}
      />

      {/* Promo code input - shown when checkbox is checked */}
      {hasPromoCode ? (
        <Controller
          name="promoCode"
          control={form.control}
          render={({ field }) => <Input placeholder="Enter promo code" variant="bordered" {...field} />}
        />
      ) : null}

      {/* Terms and conditions */}
      <Controller
        name="agreeToTerms"
        control={form.control}
        render={({ field }) => (
          <Checkbox
            isSelected={field.value}
            onValueChange={field.onChange}
            classNames={{ label: styles.termsText }}
            isInvalid={!!form.formState.errors.agreeToTerms?.message}>
            I agree to the <span className={styles.link}>User Agreement</span> & confirm I am at least 18 years old
          </Checkbox>
        )}
      />

      {/* Marketing emails */}
      <Controller
        name="agreeToMarketing"
        control={form.control}
        render={({ field }) => (
          <Checkbox
            isSelected={field.value}
            onValueChange={field.onChange}
            classNames={{ label: styles.marketingText }}>
            I agree to receive marketing emails to my email address
          </Checkbox>
        )}
      />

      {/* Error message */}
      {!!registerError && (
        <div className={styles.errorContainer}>
          <Alert color="danger" variant="solid" description={registerError} title="Registration Error" />
        </div>
      )}

      {/* Continue button */}
      <Button type="button" isLoading={pending} size="lg" onPress={onSubmit}>
        Continue
      </Button>

      {/* Divider */}
      <div className={styles.divider}>
        <span className={styles.dividerText}>Log in directly with</span>
      </div>

      {/* Social login buttons */}
      <div className={styles.socialButtons}>
        <Button variant="bordered">
          <span className={styles.socialIcon}>f</span>
        </Button>
        <Button variant="bordered">
          <span className={styles.socialIcon}>G</span>
        </Button>
      </div>
    </>
  )
}
