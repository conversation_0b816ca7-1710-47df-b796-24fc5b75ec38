@use '@theme/_variables.scss' as *;
@use '@theme/functions.scss' as *;

.form {
  width: 100%;
}

.formContent {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(5px);
  width: 100%;
  overflow: hidden;
}

.tabs {
  display: flex;
  gap: calculate-rem(8px);
  margin-bottom: calculate-rem(14px);
}

.checkboxText {
  color: var(--heroui-default-700);
  font-size: calculate-rem(14px);
}

.termsText {
  color: var(--heroui-default-700);
  font-size: calculate-rem(14px);
  line-height: 1.4;
}

.link {
  color: var(--heroui-primary);
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: var(--heroui-primary-600);
  }
}

.marketingText {
  color: var(--heroui-default-700);
  font-size: calculate-rem(14px);
  line-height: 1.4;
}

.errorContainer {
  margin-top: calculate-rem(16px);
}

.divider {
  display: flex;
  align-items: center;
  margin: calculate-rem(16px) 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: calculate-rem(1px);
    background: var(--heroui-default-300);
  }
}

.dividerText {
  margin: 0 calculate-rem(16px);
  color: var(--heroui-default-500);
  font-size: calculate-rem(14px);
  white-space: nowrap;
}

.socialButtons {
  display: flex;
  gap: calculate-rem(12px);
  justify-content: center;
}

.socialIcon {
  font-size: calculate-rem(20px);
  font-weight: 700;
  color: var(--heroui-default-700);
}

// Step-specific styles
.stepHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: calculate-rem(24px);
}

.backButton {
  display: flex;
  align-items: center;
  gap: calculate-rem(8px);
  color: var(--heroui-default-700);
  font-weight: 500;
  padding: calculate-rem(8px) calculate-rem(16px);
  border-radius: calculate-rem(8px);

  &:hover {
    background: var(--heroui-default-100);
    color: var(--heroui-default-800);
  }
}

.stepIndicator {
  display: flex;
  align-items: center;
  gap: calculate-rem(8px);
}

.stepNumber {
  width: calculate-rem(32px);
  height: calculate-rem(32px);
  border-radius: 50%;
  background: var(--heroui-secondary);
  color: var(--heroui-secondary-foreground);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: calculate-rem(16px);
}

.stepLabel {
  font-weight: 600;
  color: var(--heroui-default-800);
  font-size: calculate-rem(16px);
}

.stepTitle {
  font-size: calculate-rem(32px);
  font-weight: 700;
  color: var(--heroui-default-900);
  margin: 0 0 calculate-rem(8px) 0;
}

.stepDescription {
  font-size: calculate-rem(16px);
  color: var(--heroui-default-600);
  margin: 0 0 calculate-rem(32px) 0;
  line-height: 1.5;
}

// Step 2 specific styles
.nameFields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: calculate-rem(5px);

  @media (max-width: $breakpoint-mobile) {
    grid-template-columns: 1fr;
  }
}

.namesSection,
.dobSection,
.addressSection,
.phoneSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(5px);
}

.sectionTitle {
  font-size: calculate-rem(18px);
  font-weight: 600;
  color: var(--heroui-default-900);
  margin: 0;
}

.dobFields {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: calculate-rem(12px);

  @media (max-width: $breakpoint-mobile) {
    grid-template-columns: 1fr;
  }
}

.addressRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: calculate-rem(16px);

  @media (max-width: $breakpoint-mobile) {
    grid-template-columns: 1fr;
  }
}

.phoneFields {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: calculate-rem(12px);
  align-items: end;
}

.phoneCodeSelect {
  min-width: calculate-rem(120px);
  background: var(--heroui-default-100);
  border: 1px solid var(--heroui-default-300);
  border-radius: calculate-rem(8px);
}

.phoneInput {
  background: var(--heroui-default-100);
  border: 1px solid var(--heroui-default-300);
  border-radius: calculate-rem(8px);
  flex: 1;
}

.phoneNote {
  font-size: calculate-rem(12px);
  color: var(--heroui-warning);
  margin: calculate-rem(8px) 0 0 0;
  display: flex;
  align-items: center;
  gap: calculate-rem(4px);
}

.selectTrigger {
  background: var(--heroui-default-100);
  border: 1px solid var(--heroui-default-300);
  border-radius: calculate-rem(8px);
  color: var(--heroui-default-800);
}
