'use client'
import { useEffect, useRef } from 'react'
import type { Session } from 'next-auth'
import { useSession } from 'next-auth/react'
import type { StoreApi, UseBoundStore } from 'zustand'
import { AuthContext } from '@modules/auth/auth.context'
import type { IAuthState } from '@modules/auth/auth.store'
import { createAuthStore } from '@modules/auth/auth.store'

export const AuthProvider = ({ children, session }: { children: React.ReactNode; session?: Session | null }) => {
  const authStoreRef = useRef<UseBoundStore<StoreApi<IAuthState>> | null>(null)
  const { data: sessionData, status } = useSession()

  useEffect(() => {
    if (authStoreRef.current) {
      console.info('[Session status]', status)
      authStoreRef.current.setState({
        accessToken: sessionData?.accessToken,
        refreshToken: sessionData?.refreshToken,
        userInfo: sessionData?.user,
        status: status,
      })
    }
  }, [sessionData, status])

  if (authStoreRef.current === null) {
    authStoreRef.current = createAuthStore(session)
    AuthProvider.currentStore = authStoreRef.current
  }
  return <AuthContext.Provider value={authStoreRef.current}>{children}</AuthContext.Provider>
}
AuthProvider.currentStore = null as UseBoundStore<StoreApi<IAuthState>> | null
