'use client'
import { useState } from 'react'
import type { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'
import { envVars } from '@/env'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import { zodResolver } from '@hookform/resolvers/zod'
import { loginSchema } from '@modules/auth'
import type { ILoginSchema } from '@modules/auth'

interface IUseLoginFormOptions {
  onSuccess?: () => void
}

export const useLoginForm = (options: IUseLoginFormOptions = {}) => {
  const { onSuccess } = options
  const [loginError, setLoginError] = useState('')
  const [pending, setPending] = useState(false)

  const form = useForm<ILoginSchema>({
    defaultValues: {
      email: envVars.NEXT_PUBLIC_TEST_USER_EMAIL || '',
      password: envVars.NEXT_PUBLIC_TEST_USER_PASSWORD || '',
    },
    resolver: zodResolver(loginSchema),
  })

  const router = useRouter()

  const handleSubmit: SubmitHandler<ILoginSchema> = async data => {
    setPending(true)
    setLoginError('')

    try {
      const response = await authService.login(data)

      if (!response?.error) {
        onSuccess?.()
        router.replace(getAppRouter().dashboard, { scroll: true })
      } else {
        throw new Error(response?.code || response?.error || 'Login failed')
      }
    } catch (error: unknown | AxiosError) {
      const err = error as AxiosError
      console.error('Login error:', { ...err }, err.message)
      setLoginError(err.message || err.code || 'An error occurred during login')
    } finally {
      setPending(false)
    }
  }

  return {
    form,
    loginError,
    pending,
    handleSubmit,
  }
}
