'use client'
import { useState } from 'react'
import type { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'
import { registrationSchema } from '@/modules/auth'
import type { IRegistrationSchema } from '@/modules/auth'
import { getAppRouter } from '@/services/router.service'
import { zodResolver } from '@hookform/resolvers/zod'

export type RegistrationStep = 1 | 2

interface IUseRegistrationFormOptions {
  onSuccess?: () => void
  onStepChange?: (step: RegistrationStep) => void
}

export const useRegistrationForm = (options: IUseRegistrationFormOptions = {}) => {
  const { onSuccess, onStepChange } = options
  const [currentStep, setCurrentStep] = useState<RegistrationStep>(1)
  const [registerError, setRegisterError] = useState('')
  const [pending, setPending] = useState(false)

  const form = useForm<IRegistrationSchema>({
    defaultValues: {
      // Step 1 fields
      email: '',
      password: '',
      hasPromoCode: false,
      promoCode: '',
      agreeToTerms: false,
      agreeToMarketing: false,
      // Step 2 fields
      firstName: '',
      lastName: '',
      username: '',
      birthDay: '',
      birthMonth: '',
      birthYear: '',
      streetAddress: '',
      city: '',
      country: '',
      stateRegion: '',
      postalCode: '',
      phoneCountryCode: '+1',
      phoneNumber: '',
    },
    resolver: zodResolver(registrationSchema),
    mode: 'onChange',
  })

  const router = useRouter()

  const handleStepChange = (step: RegistrationStep) => {
    setCurrentStep(step)
    onStepChange?.(step)
  }

  const validateCurrentStep = async (): Promise<boolean> => {
    if (currentStep === 1) {
      const step1Fields = ['email', 'password', 'agreeToTerms'] as const
      return await form.trigger(step1Fields)
    }

    if (currentStep === 2) {
      const step2Fields = [
        'firstName',
        'lastName',
        'username',
        'birthDay',
        'birthMonth',
        'birthYear',
        'streetAddress',
        'city',
        'country',
        'stateRegion',
        'postalCode',
        'phoneNumber',
      ] as const
      return await form.trigger(step2Fields)
    }

    return true
  }

  const handleNextStep = async () => {
    const isValid = await validateCurrentStep()
    if (isValid && currentStep === 1) {
      handleStepChange(2)
    }
  }

  const handlePrevStep = () => {
    if (currentStep === 2) {
      handleStepChange(1)
    }
  }

  const handleSubmit: SubmitHandler<IRegistrationSchema> = async data => {
    if (currentStep === 1) {
      await handleNextStep()
      return
    }

    setPending(true)
    setRegisterError('')

    try {
      // For now, we'll just simulate a registration process
      // Replace this with actual authService.register when available
      console.log('Registration data:', data)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      onSuccess?.()
      router.replace(getAppRouter().dashboard, { scroll: true })
    } catch (error: unknown | AxiosError) {
      const err = error as AxiosError
      console.error('Registration error:', { ...err }, err.message)
      setRegisterError(err.message || err.code || 'An error occurred during registration')
    } finally {
      setPending(false)
    }
  }

  return {
    form,
    currentStep,
    registerError,
    pending,
    handleSubmit,
    handleNextStep,
    handlePrevStep,
    setCurrentStep: handleStepChange,
  }
}
