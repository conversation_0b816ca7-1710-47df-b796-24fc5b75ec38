'use client'
import { useContext } from 'react'
import { AuthContext } from '@modules/auth/auth.context'
import type { IAuthState } from '@modules/auth/auth.store'

export const useIsAuthenticated = () => {
  const status = useAuthStore(state => state.status)
  return status === 'authenticated'
}

export function useAuthStore<U = IAuthState>(selector?: (state: IAuthState) => U): U {
  const authStore = useContext(AuthContext)
  if (!authStore) {
    throw new Error('AuthStore must be used within an AuthProvider')
  }
  return selector ? authStore(selector) : (authStore() as U)
}
