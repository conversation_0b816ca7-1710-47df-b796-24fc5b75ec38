import { z } from 'zod'

export const loginSchema = z.object({
  email: z
    .string()
    .trim()
    .min(1, { message: 'Please enter the email' })
    .email({ message: 'Please enter a valid email' }),
  password: z.string().trim().min(1, { message: 'Please enter the password' }),
})

export const registrationStep1Schema = z.object({
  email: z
    .string()
    .trim()
    .min(1, { message: 'Please enter the email' })
    .email({ message: 'Please enter a valid email' }),
  password: z.string().trim().min(8, { message: 'Password must be at least 8 characters' }),
  hasPromoCode: z.boolean(),
  promoCode: z.string().optional(),
  agreeToTerms: z.boolean().refine(val => val === true, {
    message: 'You must agree to the User Agreement',
  }),
  agreeToMarketing: z.boolean(),
})

export const registrationStep2Schema = z.object({
  firstName: z.string().trim().min(1, { message: 'Please enter your first name' }),
  lastName: z.string().trim().min(1, { message: 'Please enter your last name' }),
  username: z.string().trim().min(3, { message: 'Username must be at least 3 characters' }),
  birthDay: z.string().min(1, { message: 'Please select day' }),
  birthMonth: z.string().min(1, { message: 'Please select month' }),
  birthYear: z.string().min(1, { message: 'Please select year' }),
  streetAddress: z.string().trim().min(1, { message: 'Please enter your street address' }),
  city: z.string().trim().min(1, { message: 'Please enter your city' }),
  country: z.string().min(1, { message: 'Please select your country' }),
  stateRegion: z.string().trim().min(1, { message: 'Please enter your state/region' }),
  postalCode: z.string().trim().min(1, { message: 'Please enter your postal/ZIP code' }),
  phoneCountryCode: z.string().min(1, { message: 'Please select country code' }),
  phoneNumber: z.string().trim().min(10, { message: 'Please enter a valid phone number' }),
})

export const registrationSchema = registrationStep1Schema.merge(registrationStep2Schema)

export type ILoginSchema = z.infer<typeof loginSchema>
export type IRegistrationStep1Schema = z.infer<typeof registrationStep1Schema>
export type IRegistrationStep2Schema = z.infer<typeof registrationStep2Schema>
export type IRegistrationSchema = z.infer<typeof registrationSchema>
