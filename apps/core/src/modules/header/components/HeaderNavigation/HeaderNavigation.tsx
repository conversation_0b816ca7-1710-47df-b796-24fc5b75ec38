import React from 'react'
import { getAppRouter } from '@/services/router.service'
import { DynamicLink } from '@components/DynamicLink'
import { NavbarItem } from '@heroui/navbar'
import styles from '@modules/header/Header.module.scss'

export const HeaderNavigation: React.FC = () => {
  return (
    <div className={`${styles.nav} hidden md:flex`}>
      <NavbarItem>
        <DynamicLink href={getAppRouter().casino} className={styles.navLink}>
          Casino
        </DynamicLink>
      </NavbarItem>
      <NavbarItem>
        <DynamicLink href={getAppRouter().liveCasino} className={styles.navLink}>
          Live Casino
        </DynamicLink>
      </NavbarItem>
    </div>
  )
}
