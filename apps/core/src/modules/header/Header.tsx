'use client'
import React from 'react'
import { clsx } from 'clsx'
import { getAppRouter } from '@/services/router.service'
import { DynamicLink } from '@components/DynamicLink'
import { Logo } from '@components/Logo/Logo'
import { Navbar, NavbarBrand, NavbarContent } from '@heroui/navbar'
import { useAuthStore } from '@modules/auth'
import { LoggedInHeader } from '@modules/header/logged-in/Header'
import { LoggedOutHeader } from '@modules/header/logged-out/Header'
import { SidebarSideEnum } from '@repo/constants/common/sidebar'
import { DeviceTypeEnum } from '@repo/helpers/deviceHelpers'
import { useIsMounted } from '@repo/hooks/useIsMounted'
import { useIsMobile } from '@repo/hooks/useMobile'
import { SidebarToggleWidget } from '@widgets/SidebarToggleWidget/SidebarToggleWidget'
import styles from '@modules/header/Header.module.scss'

interface IHeaderProps {
  deviceType?: DeviceTypeEnum
}

const Header: React.FC<IHeaderProps> = ({ deviceType }) => {
  const status = useAuthStore(state => state.status)
  const isMobile = useIsMobile({ initialValue: deviceType === DeviceTypeEnum.MOBILE })
  const isMounted = useIsMounted()
  // const isAuthenticated = status === 'authenticated'

  let content
  if (status === 'authenticated') {
    content = <LoggedInHeader />
  } else if (status === 'unauthenticated') {
    content = <LoggedOutHeader />
  } else {
    content = <></>
  }

  return (
    <Navbar shouldHideOnScroll={isMobile} classNames={{ base: clsx(styles.container, 'bg-(--color-background)/93') }}>
      <NavbarBrand className={styles.navbarBrand}>
        <div className={styles.navbarBrandInner}>
          <DynamicLink href={getAppRouter().home} className={styles.logoLink}>
            <Logo fill />
          </DynamicLink>

          <div className="flex flex-row items-center">
            {!!isMobile && (
              <NavbarContent className={clsx(styles.navbarContent, 'gap-4')} justify="center">
                {content}
              </NavbarContent>
            )}
            <SidebarToggleWidget side={SidebarSideEnum.LEFT} />
            {!isMobile && <SidebarToggleWidget side={SidebarSideEnum.RIGHT} />}
          </div>
        </div>
      </NavbarBrand>
      {!isMobile && (
        <NavbarContent className={clsx(styles.navbarContent, 'gap-4')} justify="center">
          {content}
        </NavbarContent>
      )}
    </Navbar>
  )
}

export default Header
