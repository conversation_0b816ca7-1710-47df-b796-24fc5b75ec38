'use client'
import React from 'react'
import { ThemeToggle } from '@components/ThemeToggle'
import { Avatar } from '@heroui/avatar'
import { NavbarContent } from '@heroui/navbar'
import { useAuthStore } from '@modules/auth'
import { HeaderNavigation } from '@modules/header/components'
import { LogoutButton } from '@modules/header/logged-in/LogoutButton'

export const LoggedInHeader = () => {
  const status = useAuthStore(state => state.status)
  const userInfo = useAuthStore(state => state.userInfo)

  return (
    <>
      <HeaderNavigation />
      <NavbarContent justify="end">
        <ThemeToggle />
        {status === 'authenticated' && <LogoutButton />}
        <Avatar name="JD" color="primary" classNames={{ name: 'font-bold text-lg' }} />
        <p>{userInfo?.playerId}</p>
      </NavbarContent>
    </>
  )
}
