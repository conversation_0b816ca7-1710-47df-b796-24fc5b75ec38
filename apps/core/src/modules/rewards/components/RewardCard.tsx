import Image from 'next/image'
import { Card } from '@components/Card'
import { RewardCardCTA } from '@modules/rewards/components/RewardCardCTA.client'
import styles from '@modules/rewards/components/RewardCard.module.scss'

export interface IRewardCardProps {
  title: string
  subTitle: string
  backgroundImageSrc?: string
  thumbnailSrc?: string
  ctaLabel?: string
  ctaHref?: string
  ctaActionKey?: string
}

const RewardCard = ({
  title,
  subTitle,
  backgroundImageSrc,
  thumbnailSrc,
  ctaHref,
  ctaLabel,
  ctaActionKey,
}: IRewardCardProps) => {
  return (
    <Card className={styles.rewardCard} aria-label="Reward Card">
      {backgroundImageSrc ? (
        <Image src={backgroundImageSrc} className={styles.background} quality={100} fill alt="Reward Card background" />
      ) : null}

      {thumbnailSrc ? (
        <div className={styles.thumbnail}>
          <Image src={thumbnailSrc} quality={100} alt={`${title || 'Reward'} thumbnail`} width={68} height={68} />
        </div>
      ) : null}

      <div className={styles.content}>
        <div className={styles.titleContainer}>
          {title ? <p className={styles.title}>{title}</p> : null}
          {subTitle ? <p className={styles.subTitle}>{subTitle}</p> : null}
        </div>

        {ctaLabel ? <RewardCardCTA ctaLabel={ctaLabel} ctaHref={ctaHref} ctaActionKey={ctaActionKey} /> : null}
      </div>
    </Card>
  )
}

export default RewardCard
