import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.REWARDS),
  meta: z.object({
    title: z.string().optional(),
  }),
})

export type DynamicallyRenderedRewardsConfigType = z.infer<typeof DynamicallyRenderedRewardsConfigSchema>['meta']
