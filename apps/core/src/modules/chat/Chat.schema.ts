import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedModule } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedChatConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedModule.CHAT),
})
