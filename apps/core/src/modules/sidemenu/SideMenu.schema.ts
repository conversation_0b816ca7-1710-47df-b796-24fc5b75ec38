import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedModule } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedSideMenuConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedModule.SIDE_MENU),
})
