import type { FC } from 'react'
import { getSidemenuConfig } from '@/network/server-utils/s3/getters'
import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import type { Locale } from '@constants/locale'

interface ISideMenuProps {
  locale?: Locale
}

const SideMenu: FC<ISideMenuProps> = async ({ locale }) => {
  const config = await getSidemenuConfig()

  if (!config) {
    console.error('[SideMenu] Configuration not found')
    return null
  }

  return <DynamicContentRenderer config={config} locale={locale} />
}

export default SideMenu
