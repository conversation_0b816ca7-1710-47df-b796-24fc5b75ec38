import type { FC } from 'react'
import React from 'react'
import dynamic from 'next/dynamic'
import { withSuspense } from '@/HOC/withSuspense'
import { getCasinoConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { LoadingComponentSkeleton } from '@components/DynamicContentRenderer/LoadingComponenSkeleton'

const GamesSection = dynamic(() => import('@components/GamesSection').then(mod => mod.GamesSection))

const SlotsContent: FC<IDynamicallyRenderedContentProps> = async ({ locale }) => {
  const casinoConfig = await getCasinoConfig(locale)

  return (
    <>
      {casinoConfig?.sections?.map((section, index) => (
        <React.Fragment key={section.id}>
          {withSuspense(
            <GamesSection section={section} locale={locale} priority={index < 3} />,
            <LoadingComponentSkeleton height={310} />,
          )}
        </React.Fragment>
      ))}
    </>
  )
}

export default SlotsContent
