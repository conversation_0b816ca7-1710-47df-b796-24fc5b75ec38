import type { FC } from 'react'
import React from 'react'
import { withSuspense } from '@/HOC/withSuspense'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { LoadingComponentSkeleton } from '@components/DynamicContentRenderer/LoadingComponenSkeleton'
import SlotsContent from '@modules/slots/Slots.content'
import styles from '@modules/slots/Slots.module.scss'

const Slots: FC<IDynamicallyRenderedContentProps> = async ({ locale }) => {
  return (
    <div className={styles.container}>
      <h2>Slots</h2>
      {withSuspense(
        <SlotsContent locale={locale} />,
        <>
          <LoadingComponentSkeleton height={300} />
          <LoadingComponentSkeleton height={300} />
        </>,
      )}
    </div>
  )
}

export default Slots
