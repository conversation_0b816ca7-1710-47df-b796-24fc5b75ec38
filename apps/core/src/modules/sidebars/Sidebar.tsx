import type { FC } from 'react'
import React from 'react'
import { getSidebarConfig } from '@/network/server-utils/s3/getters'
import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import { SidebarClient } from '@modules/sidebars/Sidebar.client'
import type { SidebarSideEnum } from '@repo/constants/common/sidebar'

interface ISidebarProps {
  side: SidebarSideEnum
}

export const Sidebar: FC<ISidebarProps> = async ({ side }) => {
  const sidebarsConfig = await getSidebarConfig()
  const sidebarConfig = sidebarsConfig?.[side]

  if (!sidebarConfig) {
    // already validated on previous step
    return null
  }

  return (
    <SidebarClient side={side}>
      <DynamicContentRenderer config={sidebarConfig} />
    </SidebarClient>
  )
}
