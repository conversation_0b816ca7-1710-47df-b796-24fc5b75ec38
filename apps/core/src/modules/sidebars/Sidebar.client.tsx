'use client'
import React from 'react'
import Sidebar<PERSON> from '@modules/sidebars/SidebarUI'
import { SidebarSideEnum } from '@repo/constants/common/sidebar'
import { useSidebar } from '@repo/ui/shadcn/sidebar'

interface ISidebarBaseClientProps {
  side: SidebarSideEnum
  children: React.ReactNode
}

export const SidebarClient = ({ side, children }: ISidebarBaseClientProps) => {
  const { isOpen, setIsOpen } = useSidebar(side)
  const sideConstant = side === SidebarSideEnum.LEFT ? SidebarSideEnum.LEFT : SidebarSideEnum.RIGHT

  return <SidebarUI side={sideConstant} isOpen={isOpen} setIsOpen={setIsOpen} content={children} />
}
