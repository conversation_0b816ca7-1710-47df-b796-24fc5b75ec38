import type { ReactNode } from 'react'
import type { ISidebarProps } from '@repo/ui/shadcn/sidebar'
import { Sidebar as ShadcnSidebar, SidebarContent } from '@repo/ui/shadcn/sidebar'
import styles from '@modules/sidebars/Sidebar.module.scss'

interface ISidebarComponentProps extends Omit<ISidebarProps, 'content'> {
  content?: ReactNode
}

// data-scroll-locked="1" on open side bar on mobile hides the header (shouldHideOnScroll={isMobile})
function Sidebar({ content, ...props }: ISidebarComponentProps) {
  return (
    <ShadcnSidebar {...props} variant="inset" collapsible="offcanvas" className={styles.container}>
      <SidebarContent>{content}</SidebarContent>
    </ShadcnSidebar>
  )
}

export default Sidebar
