import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { authMiddleware } from '@/middlewares/authMiddleware'
import { localeMiddleware } from '@/middlewares/localeMiddleware'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  console.log({ pathname })
  let processedResponse: NextResponse | undefined
  let rewriteResponse: NextResponse | undefined
  const localeMiddlewareResponse = (await localeMiddleware(request)) as
    | { isRewrite: boolean; response: NextResponse }
    | undefined
  if (localeMiddlewareResponse?.isRewrite) {
    rewriteResponse = localeMiddlewareResponse.response
  } else {
    processedResponse = localeMiddlewareResponse as unknown as NextResponse
  }

  processedResponse = processedResponse ?? (await authMiddleware(request))

  return processedResponse ?? rewriteResponse ?? NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - .well-known (well-known URI paths)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    // Skip Next.js internals and all static files, unless found in search params
    // eslint-disable-next-line max-len
    '/((?!api|_next/static|_next|_vercel|\\.well-known|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|svg|docx?|zip|webmanifest)).*)',
  ],
  unstable_allowDynamic: ['**/node_modules/lodash-es/**/*.js'],
}
