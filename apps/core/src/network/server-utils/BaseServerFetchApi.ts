import 'server-only'
import type { Keyable } from '@repo/types/common'

interface IBaseServerFetchApiOptions {
  params?: Keyable
  rawResponse?: boolean
}

export class BaseServerFetchApi {
  baseUrl = 'unknown'
  revalidate = 15
  defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  }

  async fetch<T>(endpoint: string, options: RequestInit & IBaseServerFetchApiOptions = {}): Promise<T | null> {
    const { params, rawResponse, ...fetchOptions } = options
    try {
      let url = `${this.baseUrl}${endpoint}`
      if (params && Object.keys(params).length > 0) {
        const queryParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value))
          }
        })

        const queryString = queryParams.toString()
        if (queryString) {
          url += `?${queryString}`
        }
      }

      const response = await fetch(url, {
        method: 'GET',
        ...fetchOptions,
        headers: { ...this.defaultHeaders, ...fetchOptions.headers },
        next: {
          revalidate: this.revalidate,
          ...fetchOptions.next,
        },
      })

      if (rawResponse) {
        return response as unknown as T
      }

      if (!response.ok) {
        if (response.headers.get('Content-Type')?.includes('application/json')) {
          try {
            const errorData = await response.json()
            console.error('Error response from', url, { response: errorData })
          } catch (error) {
            console.error(`Failed to parse error response from ${url}`, error)
          }
        }
        console.error(`[HTTP error] status: ${response.status}`, { url })
        throw new Error(`[HTTP error] status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error)
      if (rawResponse) {
        throw error
      }
      return null
    }
  }
}
