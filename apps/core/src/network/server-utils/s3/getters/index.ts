import 'server-only'
import { merge } from 'lodash-es'
import { unstable_cache } from 'next/cache'
import { s3FetchApi } from '@/network/server-utils/s3/S3FetchApi'
import type { Locale } from '@constants/locale'

export const getAppConfig = unstable_cache(
  async (locale: Locale) => {
    const globalConfig = await s3FetchApi.getAppConfigGlobal()
    const appConfig = await s3FetchApi.getAppConfig(locale)
    return merge({}, globalConfig, appConfig) // TODO - merge the configs properly
  },
  [],
  { revalidate: 30 },
)

export const getImgIxConfig = unstable_cache(
  async () => {
    const globalConfig = await s3FetchApi.getAppConfigGlobal()
    return globalConfig?.imgIxConfig
  },
  [],
  { revalidate: 60 },
)

export const getWelcomePageConfig = async (locale: Locale) => {
  const welcomePageConfig = await s3FetchApi.getWelcomePageConfig(locale)
  return welcomePageConfig
}

export const getSidebarConfig = async (options?: RequestInit) => {
  const sidebarConfig = await s3FetchApi.getSidebarConfig(options)
  return sidebarConfig
}

export const getTimerComponentConfig = async (options?: RequestInit) => {
  const timerComponentConfig = await s3FetchApi.getTimerComponentConfig(options)
  return timerComponentConfig
}

export const getFeaturedOfferConfig = async (locale: Locale) => {
  const featuredOfferConfig = await s3FetchApi.getFeaturedOfferConfig(locale)
  return featuredOfferConfig
}

export const getCasinoPageLayoutConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getCasinoPageLayoutConfigGlobal(options)
  return config
}

export const getLiveCasinoPageLayoutConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getLiveCasinoPageLayoutConfigGlobal(options)
  return config
}

export const getCasinoConfig = async (locale: Locale) => {
  const config = await s3FetchApi.getCasinoConfig(locale)
  return config
}

export const getSidemenuConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getSidemenuConfig(options)
  return config
}

export const getHomePageConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getHomePageConfig(options)
  return config
}

export const getPromotionsPageConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getPromotionsPageConfig(options)
  return config
}
