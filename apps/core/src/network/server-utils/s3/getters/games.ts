import 'server-only'
import { auth } from '@/auth'
import { gamesService } from '@/services/Games.service'
import { getLicenseByLocale } from '@/utils/locale'
import { detectCountryByIp, detectLocaleByIp } from '@/utils/server/network'
import type { Locale } from '@constants/locale'
import type { IRlGame } from '@repo/types/games'

export const getAvailableGames = async (locale: Locale) => {
  console.log('getAvailableGames for locale:', locale)
  if (!locale) {
    console.warn('Locale is not provided, returning empty games list')
    return [] as IRlGame[]
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return [] as IRlGame[]
  }

  const countryByIp = await detectCountryByIp()
  const session = await auth()

  return gamesService.prepareGames(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: undefined,
    license: getLicenseByLocale(locale),
  })
}

export const getAvailableGamesMap = async (locale: Locale) => {
  if (!locale) {
    console.warn('Locale is not provided, returning empty games list')
    return {} as ReturnType<typeof gamesService.prepareGamesMap>
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return {} as ReturnType<typeof gamesService.prepareGamesMap>
  }

  const countryByIp = await detectLocaleByIp()
  const session = await auth()

  return gamesService.prepareGamesMap(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: undefined,
    license: getLicenseByLocale(locale),
  })
}
