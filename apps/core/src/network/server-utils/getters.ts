import 'server-only'
import { getBanners } from '@/network/server-utils/rl/getters'
import { getWelcomePageConfig } from '@/network/server-utils/s3/getters'
import type { Locale } from '@constants/locale'

export const getWelcomeBanner = async (locale: Locale) => {
  const [welcomePageConfig, allMarketBanners] = await Promise.all([getWelcomePageConfig(locale), getBanners(locale)])
  const welcomePageBannerConfig = welcomePageConfig?.welcome_page_v2.find(obj => obj.type === 'banner_section')
  if (welcomePageBannerConfig?.is_hidden) {
    return undefined
  }

  const bannerPreviewConfig = welcomePageBannerConfig?.preview_config

  if (!welcomePageConfig || !allMarketBanners) {
    console.warn('Missing welcome page config or banners', { bannerPreviewConfig, allMarketBanners })
    return undefined
  }
  const welcomeBannerSlug = bannerPreviewConfig?.welcome_banner_slug
  if (!welcomeBannerSlug) {
    console.warn('Missing welcome banner slug in welcome-page config', { bannerPreviewConfig })
    return undefined
  }
  const defaultBanner = allMarketBanners.find(obj => obj.slug === welcomeBannerSlug)
  if (!defaultBanner) {
    console.warn('Could not find welcome banner by slug', { slug: welcomeBannerSlug, allMarketBanners })
  }

  return defaultBanner
}
