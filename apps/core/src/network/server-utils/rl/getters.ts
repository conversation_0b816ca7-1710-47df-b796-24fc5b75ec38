import { rlFetchA<PERSON> } from '@/network/server-utils/rl/RLFetchApi'
import { getLicenseByLocale } from '@/utils/locale'
import type { Locale } from '@constants/locale'
import type { IGetPromotionsProps } from '@repo/types/api/rl/promotions'
import 'server-only'

export const getBanners = async (locale: Locale) => {
  const banners = await rlFetchApi.getBanners({ license: getLicenseByLocale(locale), locale })
  return banners?.payload
}

export const getPromotions = async (props: IGetPromotionsProps) => {
  const response = await rlFetchApi.getPromotions(props)
  return response
}
