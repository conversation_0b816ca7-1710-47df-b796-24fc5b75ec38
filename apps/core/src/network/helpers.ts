import type { InternalAxiosRequestConfig } from 'axios'

export function cancelRequest(config: InternalAxiosRequestConfig) {
  const controller = new AbortController()
  controller.abort()
  return {
    ...config,
    signal: controller.signal,
  }
}

export function calculateExponentialDelay(attempt: number, retryDelay: number, maxRetryDelay: number): number {
  return Math.min(Math.ceil(Math.exp(attempt) * retryDelay), maxRetryDelay)
}
