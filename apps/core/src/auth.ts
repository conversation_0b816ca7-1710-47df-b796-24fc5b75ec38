import { merge } from 'lodash-es'
import type { NextAuthResult } from 'next-auth'
import NextAuth, { CredentialsSignin } from 'next-auth'
import type { AdapterUser } from 'next-auth/adapters'
import Credentials from 'next-auth/providers/credentials'
import { loginAction } from '@/actions/login'
import type { IRlLoginResponse } from '@repo/types/api/rl/auth'
import type { IRlResponseError } from '@repo/types/api/rl/index'
import type { Keyable } from '@repo/types/common'

export class InvalidLoginError extends CredentialsSignin {
  code = 'custom'
  constructor(message: string) {
    super(message)
    this.code = message
  }
}

type RefreshJwtAuthParams = {
  refreshToken: string
}

const nextAuthResult = NextAuth({
  providers: [
    Credentials({
      credentials: {
        email: { label: 'Email', type: 'text', placeholder: 'email' },
        password: { label: 'Password', type: 'password', placeholder: 'password' },
      },
      authorize: async credentials => {
        const data = {
          email: credentials.email as string,
          password: credentials.password as string,
        }

        try {
          const response = await loginAction(data)

          if (!response?.ok) {
            const errorData = (await response?.json()) as IRlResponseError
            console.error('Authorization failed:', errorData)
            throw new InvalidLoginError(errorData?.data?.message || 'Invalid credentials or missing tokens')
          }

          const resData = (await response.json()).data as IRlLoginResponse
          const sessionData = resData?.sessionData

          if (resData.status === 'SUCCESS' && sessionData && sessionData.sessionToken && sessionData.refreshToken) {
            const sessionInfo = {
              accessToken: sessionData.sessionToken,
              refreshToken: sessionData.refreshToken,
              userInfo: resData.playerData,
            }
            console.log('Authorization successful:', sessionInfo)
            return sessionInfo as any
          } else {
            console.error('Authorization failed:', resData)
            throw new InvalidLoginError('Invalid credentials or missing tokens')
          }
        } catch (err) {
          console.error('Authorization error:', err)
          if (!(err instanceof InvalidLoginError)) {
            throw new InvalidLoginError((err as Keyable)?.message || 'An unknown error occurred during authorization')
          }
          throw err
        }
      },
    }),
  ],
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 14 * 86400,
  },
  jwt: {
    maxAge: 14 * 86400, // 14 days
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      const jwtData = { ...token, ...user }
      if (trigger === 'update') {
        merge(jwtData, {
          accessToken: session?.accessToken,
          refreshToken: session?.refreshToken,
          userInfo: session?.user,
        })
      }
      return jwtData
    },
    async session({ session, token }) {
      session.user = token?.userInfo as AdapterUser
      session.accessToken = token?.accessToken as string
      session.refreshToken = token?.refreshToken as string
      return session
    },
  },
  logger: {
    error(error: Error) {
      /* We are suppressing the error if it is a CredentialsSignin error
        since there is no reason to show server error for wrong password */
      if ((error as Keyable).type === 'CredentialsSignin') {
        return
      }
      // Otherwise, log other errors
      console.log('Error may have diff format because of logger!\n')
      console.error(error)
    },
    warn(message: string) {
      console.warn(message)
    },
    debug(message: string) {
      console.debug(message)
    },
  },
})

export const auth: NextAuthResult['auth'] = nextAuthResult.auth
export const { handlers, signIn, unstable_update }: NextAuthResult = nextAuthResult
