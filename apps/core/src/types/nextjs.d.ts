import type { Locale } from '@constants/locale'

export type RouteParams = {
  locale: Locale
}

export type PageParams<T = RouteParams> = Promise<T>

export type PageProps<T = RouteParams> = {
  params: Promise<T>
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>
}

export type LayoutProps<T = RouteParams> = {
  children: React.ReactNode
  modal: React.ReactNode
  params: Promise<T>
}
