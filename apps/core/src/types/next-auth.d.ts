/* eslint-disable @typescript-eslint/naming-convention */
import type { IRlUser } from '@/types/auth'
import 'next-auth'

declare module 'next-auth' {
  interface JWT {
    accessToken: string
    refreshToken: string
    userInfo: IRlUser
  }

  interface Session {
    accessToken: string
    refreshToken: string
    user: IRlUser
  }

  export interface User {
    accessToken: string
    refreshToken: string
    userInfo: IRlUser
  }
}
