/* eslint-disable @typescript-eslint/naming-convention */
/// <reference types="jest" />

declare namespace jest {
  interface Mock<T = any, Y extends any[] = any> {
    new (...args: Y): T
    (...args: Y): T
    mockImplementation: (fn: (...args: Y) => T) => this
    mockReturnValue: (val: T) => this
    mockReturnThis: () => this
    mockResolvedValue: (val: Awaited<T>) => this
    mockRejectedValue: (val: any) => this
    mockReset: () => void
    mockClear: () => void
  }

  function fn<T = any, Y extends any[] = any>(): Mock<T, Y>
  function fn<T = any, Y extends any[] = any>(implementation: (...args: Y) => T): Mock<T, Y>

  function mock(moduleName: string, factory?: any, options?: any): void
  function clearAllMocks(): void
  function resetAllMocks(): void
}

declare function describe(name: string, fn: () => void): void
declare function it(name: string, fn: () => void | Promise<void>, timeout?: number): void
declare function expect<T>(value: T): any
declare function beforeEach(fn: () => void | Promise<void>): void
declare function afterEach(fn: () => void | Promise<void>): void
