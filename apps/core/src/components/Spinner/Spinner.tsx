'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import { clsx } from 'clsx'
import { m } from 'motion/react'
import styles from '@components/Spinner/Spinner.module.scss'

interface ISpinnerProps {
  absolute?: boolean
}

const Spinner: FC<ISpinnerProps> = ({ absolute }) => {
  return (
    <m.div
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={clsx(styles.container, absolute && styles.absolute)}
      key="box">
      <div className={styles.loader} />
    </m.div>
  )
}

export default Spinner
