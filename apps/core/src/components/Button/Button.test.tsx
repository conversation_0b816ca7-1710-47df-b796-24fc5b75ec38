import { Button } from '@components/Button'
import '@testing-library/jest-dom'
import { render, screen } from '@testing-library/react'

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} alt={props.alt || ''} />
  },
}))

describe('Button Component', () => {
  it('renders button with label', () => {
    render(<Button label="Click me" />)

    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
  })

  it('renders button with disabled state', () => {
    render(<Button label="Disabled Button" isDisabled />)

    const button = screen.getByRole('button', { name: /disabled button/i })
    expect(button).toBeDisabled()
  })

  it('renders loading state', () => {
    render(<Button label="Loading Button" isLoading />)

    const button = screen.getByRole('button', { name: /loading button/i })
    expect(button).toBeInTheDocument()
  })

  it('renders button with primary color', () => {
    render(<Button label="Primary Button" color="primary" />)

    const button = screen.getByRole('button', { name: /primary button/i })
    expect(button).toBeInTheDocument()
  })

  it('renders button with icon', () => {
    render(<Button label="Icon Button" icon="/icon.svg" />)

    const button = screen.getByRole('button', { name: /icon button/i })
    expect(button).toBeInTheDocument()

    const icon = screen.getByAltText('Button Icon')
    expect(icon).toBeInTheDocument()
  })
})
