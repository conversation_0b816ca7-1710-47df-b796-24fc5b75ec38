import type { IButtonProps } from '@components/Button'
import { Button } from '@components/Button'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof Button> = {
  title: 'Button',
  component: Button,
  tags: ['autodocs'],
  args: {
    label: 'Click me',
    color: 'primary',
    size: 'md',
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
    },
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'tertiary'],
    },
    disabled: {
      control: 'boolean',
    },
    onClick: {
      action: 'clicked',
    },
  },
}

export default meta

export const Variants: StoryObj<typeof Button> = {
  // Disable all args for variants story - it showcases all button states and variants
  argTypes: {
    onClick: { table: { disable: true } },
    href: { table: { disable: true } },
    label: { table: { disable: true } },
    color: { table: { disable: true } },
    size: { table: { disable: true } },
    disabled: { table: { disable: true } },
    isLoading: { table: { disable: true } },
    icon: { table: { disable: true } },
  },

  parameters: {
    docs: {
      description: {
        story: 'All button variants, sizes, and states',
      },
    },
  },

  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
      {/* Basic variants - all colors and sizes, default and disabled */}
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Basic Variants</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          {/* Primary buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button label="Primary Large" color="primary" size="lg" />
            <Button label="Primary Medium" color="primary" size="md" />
            <Button label="Primary Small" color="primary" size="sm" />
            <Button label="Primary Large Disabled" color="primary" size="lg" isDisabled />
            <Button label="Primary Medium Disabled" color="primary" size="md" isDisabled />
            <Button label="Primary Small Disabled" color="primary" size="sm" isDisabled />
          </div>

          {/* Secondary buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button label="Secondary Large" color="secondary" size="lg" />
            <Button label="Secondary Medium" color="secondary" size="md" />
            <Button label="Secondary Small" color="secondary" size="sm" />
            <Button label="Secondary Large Disabled" color="secondary" size="lg" isDisabled />
            <Button label="Secondary Medium Disabled" color="secondary" size="md" isDisabled />
            <Button label="Secondary Small Disabled" color="secondary" size="sm" isDisabled />
          </div>

          {/* Tertiary buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button label="Tertiary Large" color="tertiary" size="lg" />
            <Button label="Tertiary Medium" color="tertiary" size="md" />
            <Button label="Tertiary Small" color="tertiary" size="sm" />
            <Button label="Tertiary Large Disabled" color="tertiary" size="lg" isDisabled />
            <Button label="Tertiary Medium Disabled" color="tertiary" size="md" isDisabled />
            <Button label="Tertiary Small Disabled" color="tertiary" size="sm" isDisabled />
          </div>
        </div>
      </div>

      {/* Buttons with icons */}
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>With Icons</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          {/* Primary with icons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button
              label="Primary Large"
              color="primary"
              size="lg"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Primary Medium"
              color="primary"
              size="md"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Primary Small"
              color="primary"
              size="sm"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Primary Large Disabled"
              color="primary"
              size="lg"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
              isDisabled
            />
            <Button
              label="Primary Medium Disabled"
              color="primary"
              size="md"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
              isDisabled
            />
            <Button
              label="Primary Small Disabled"
              color="primary"
              size="sm"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
              isDisabled
            />
          </div>

          {/* Secondary with icons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button
              label="Secondary Large"
              color="secondary"
              size="lg"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Secondary Medium"
              color="secondary"
              size="md"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Secondary Small"
              color="secondary"
              size="sm"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Secondary Large Disabled"
              color="secondary"
              size="lg"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
              isDisabled
            />
            <Button
              label="Secondary Medium Disabled"
              color="secondary"
              size="md"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
              isDisabled
            />
            <Button
              label="Secondary Small Disabled"
              color="secondary"
              size="sm"
              icon="https://luckyspins4.imgix.net/icons/promotions.svg"
              isDisabled
            />
          </div>

          {/* Tertiary with icons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button
              label="Tertiary Large"
              color="tertiary"
              size="lg"
              icon="https://buustikasino.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Tertiary Medium"
              color="tertiary"
              size="md"
              icon="https://buustikasino.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Tertiary Small"
              color="tertiary"
              size="sm"
              icon="https://buustikasino.imgix.net/icons/promotions.svg"
            />
            <Button
              label="Tertiary Large Disabled"
              color="tertiary"
              size="lg"
              icon="https://buustikasino.imgix.net/icons/promotions.svg"
              isDisabled
            />
            <Button
              label="Tertiary Medium Disabled"
              color="tertiary"
              size="md"
              icon="https://buustikasino.imgix.net/icons/promotions.svg"
              isDisabled
            />
            <Button
              label="Tertiary Small Disabled"
              color="tertiary"
              size="sm"
              icon="https://buustikasino.imgix.net/icons/promotions.svg"
              isDisabled
            />
          </div>
        </div>
      </div>

      {/* Loading state */}
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Loading States</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          {/* Primary loading */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button label="Primary Large" color="primary" size="lg" isLoading />
            <Button label="Primary Medium" color="primary" size="md" isLoading />
            <Button label="Primary Small" color="primary" size="sm" isLoading />
          </div>

          {/* Secondary loading */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button label="Secondary Large" color="secondary" size="lg" isLoading />
            <Button label="Secondary Medium" color="secondary" size="md" isLoading />
            <Button label="Secondary Small" color="secondary" size="sm" isLoading />
          </div>

          {/* Tertiary loading */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button label="Tertiary Large" color="tertiary" size="lg" isLoading />
            <Button label="Tertiary Medium" color="tertiary" size="md" isLoading />
            <Button label="Tertiary Small" color="tertiary" size="sm" isLoading />
          </div>
        </div>
      </div>

      {/* Icon-only buttons */}
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Icon Only</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          {/* Primary icon-only */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button color="primary" size="lg" icon="https://luckyspins4.imgix.net/icons/promotions.svg" />
            <Button color="primary" size="md" icon="https://luckyspins4.imgix.net/icons/promotions.svg" />
            <Button color="primary" size="sm" icon="https://luckyspins4.imgix.net/icons/promotions.svg" />
            <Button color="primary" size="lg" icon="https://luckyspins4.imgix.net/icons/promotions.svg" isDisabled />
            <Button color="primary" size="md" icon="https://luckyspins4.imgix.net/icons/promotions.svg" isDisabled />
            <Button color="primary" size="sm" icon="https://luckyspins4.imgix.net/icons/promotions.svg" isDisabled />
          </div>

          {/* Secondary icon-only */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button color="secondary" size="lg" icon="https://luckyspins4.imgix.net/icons/promotions.svg" />
            <Button color="secondary" size="md" icon="https://luckyspins4.imgix.net/icons/promotions.svg" />
            <Button color="secondary" size="sm" icon="https://luckyspins4.imgix.net/icons/promotions.svg" />
            <Button color="secondary" size="lg" icon="https://luckyspins4.imgix.net/icons/promotions.svg" isDisabled />
            <Button color="secondary" size="md" icon="https://luckyspins4.imgix.net/icons/promotions.svg" isDisabled />
            <Button color="secondary" size="sm" icon="https://luckyspins4.imgix.net/icons/promotions.svg" isDisabled />
          </div>

          {/* Tertiary icon-only */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <Button color="tertiary" size="lg" icon="https://buustikasino.imgix.net/icons/promotions.svg" />
            <Button color="tertiary" size="md" icon="https://buustikasino.imgix.net/icons/promotions.svg" />
            <Button color="tertiary" size="sm" icon="https://buustikasino.imgix.net/icons/promotions.svg" />
            <Button color="tertiary" size="lg" icon="https://buustikasino.imgix.net/icons/promotions.svg" isDisabled />
            <Button color="tertiary" size="md" icon="https://buustikasino.imgix.net/icons/promotions.svg" isDisabled />
            <Button color="tertiary" size="sm" icon="https://buustikasino.imgix.net/icons/promotions.svg" isDisabled />
          </div>
        </div>
      </div>
    </div>
  ),
}

export const Playground: StoryObj<typeof Button> = {
  parameters: {
    docs: {
      description: {
        story: 'A playground button with all properties available for customization',
      },
    },
  },
  args: {
    label: 'Interactive Button',
    color: 'primary',
    size: 'md',
    disabled: false,
    isLoading: false,
    icon: 'https://luckyspins4.imgix.net/icons/promotions.svg',
  },
  render: args => (
    <div>
      <Button {...(args as IButtonProps)} />
    </div>
  ),
  // Disable args that are not relevant
  argTypes: {
    onClick: { table: { disable: true } },
    href: { table: { disable: true } },
  },
}
