import React from 'react'
import { clsx } from 'clsx'
import styles from '@components/Card/Card.module.scss'

type CardVariant = 'default' | 'outlined' | 'flat' | 'hoverable'

type CardProps = React.ComponentPropsWithRef<'div'> & {
  variant?: CardVariant
}

export function Card({ className, children, variant = 'default', ...props }: CardProps) {
  return (
    <div className={clsx(styles.card, variant !== 'default' && styles[variant], className)} {...props}>
      {children}
    </div>
  )
}

type CardHeaderProps = React.ComponentPropsWithRef<'div'>

export function CardHeader({ className, children, ...props }: CardHeaderProps) {
  return (
    <div className={clsx(styles.header, className)} {...props}>
      {children}
    </div>
  )
}

type CardTitleProps<T extends React.ElementType = 'h6'> = {
  as?: T
  className?: string
  children?: React.ReactNode
} & React.ComponentPropsWithRef<T>

export function CardTitle<T extends React.ElementType = 'h6'>({
  as,
  className,
  children,
  ...props
}: CardTitleProps<T>) {
  const Component = (as || 'h6') as React.ElementType
  return (
    <Component className={clsx(styles.title, className)} {...props}>
      {children}
    </Component>
  )
}

type CardContentProps = React.ComponentPropsWithRef<'div'>

export function CardContent({ className, children, ...props }: CardContentProps) {
  return (
    <div className={clsx(styles.content, className)} {...props}>
      {children}
    </div>
  )
}

type CardFooterProps = React.ComponentPropsWithRef<'div'>

export function CardFooter({ className, children, ...props }: CardFooterProps) {
  return (
    <div className={clsx(styles.footer, className)} {...props}>
      {children}
    </div>
  )
}
