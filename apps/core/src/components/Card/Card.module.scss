@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.card {
  display: flex;
  flex-direction: column;
  border-radius: calculate-rem(8px);
  padding: calculate-rem(16px) calculate-rem(10px);

  .header {
    font-size: calculate-rem(16px);
  }

  .title {
    text-align: center;
    font-size: calculate-rem(14px);
    font-weight: 500;
    color: $color-background;
    line-height: calculate-rem(14px);
  }

  .content {
    display: flex;
    justify-content: center;
    gap: calculate-rem(8px);
  }

  .footer {
    font-size: calculate-rem(12px);
  }
}

.outlined {
  border: 1px solid $color-secondary;
  background-color: transparent;
}

.flat {
  box-shadow: none;
  background-color: transparent;
}

.hoverable {
  transition: box-shadow 0.2s ease;
  cursor: pointer;

  &:hover {
    box-shadow: calculate-rem(3px) calculate-rem(5px) $color-secondary;
  }
}
