'use client'
import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/navigation'
import { Spinner } from '@components/Spinner'
import { Modal, ModalContent, ModalHeader, ModalBody } from '@heroui/modal'

// Preload the form component for better performance
const DynamicLoginForm = dynamic(() => import('@modules/auth').then(mod => mod.LoginForm), {
  ssr: false,
  loading: () => <Spinner />,
})

export function LoginModal() {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(true)

  const handleClose = () => {
    setIsOpen(false)
    setTimeout(router.back, 200)
  }

  const handleLoginSuccess = () => {
    setIsOpen(false)
    // Navigate to a success page or dashboard
    setTimeout(router.back, 200)
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      placement="bottom-center"
      size="lg"
      scrollBehavior="inside"
      backdrop="blur"
      classNames={{
        base: 'max-h-[90vh]',
        wrapper: 'px-4 pb-4',
      }}>
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">Login to your account</ModalHeader>
        <ModalBody className="min-h-[200px]">
          <DynamicLoginForm onSuccess={handleLoginSuccess} />
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}
