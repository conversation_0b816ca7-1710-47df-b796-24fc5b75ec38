@use './GameTile.sd.module' as *;
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.wrapper {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
}

.container {
  position: relative;
  height: 100%;
}

.overlayWrapper {
  position: absolute;
  inset: 0;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.overlayWrapper > * {
  pointer-events: auto;
}

.wrapper:not(:has(.hoverOverlay)) .link {
  pointer-events: auto;
}

.link {
  display: block;
  height: 100%;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.imageContainer img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: $radius-lg;
}

.image {
  border-radius: $radius-lg;
}

.hoverOverlay {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: calculate-rem(8px);
  border-radius: $radius-lg;
  overflow: hidden;
  padding: calculate-rem(10px);
  pointer-events: auto;
}

.overlayBackdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.78;
  padding: calculate-rem(10px);
  pointer-events: auto;
  border-radius: $radius-lg;
  border: 2px solid $border-color;
  background: $background-gradient;
  z-index: 0;
}

.contentContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  gap: calculate-rem(8px);
  z-index: 1;
}

.buttonsContainer {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(4px);
  width: 100%;
  z-index: 1;
}

.gameName {
  display: flex;
  align-items: center;
  color: $color-on-tertiary;
  font-weight: 800;
  font-size: calculate-rem(24px);
  letter-spacing: 0.14px;
}

.infoContainer {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(4px);
}

.tokenInfo {
  display: flex;
  flex-direction: row;
  gap: calculate-rem(4px);
  align-items: center;
}

.gameInfo,
.playerName {
  font-size: calculate-rem(13px);
  font-weight: 500;
  line-height: calculate-rem(12px);
}

.tokenAmount {
  font-size: calculate-rem(16px);
  font-weight: 800;
  line-height: normal;
}

.tokenIcon,
.tokenAmount {
  display: flex;
  align-items: center;
}

.tokenIcon {
  width: calculate-rem(14px);
  height: calculate-rem(14px);
  background-color: $color-primary;
  color: $color-on-primary;
  border-radius: 50%;
  font-size: calculate-rem(6px);
  font-weight: 900;
  line-height: normal;
  letter-spacing: 0.063px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: calculate-rem(2px);
}
