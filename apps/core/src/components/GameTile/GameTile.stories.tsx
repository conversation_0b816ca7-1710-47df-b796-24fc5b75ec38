import { GameTile } from '@components/GameTile'
import { GAME_TILE_DEFAULT_HEIGHT, GAME_TILE_DEFAULT_WIDTH } from '@components/GameTile/GameTile.settings'
import type { Meta, StoryObj } from '@storybook/nextjs'

const mockGame = {
  name: 'Sample Game',
  meta: {
    thumbnail: {
      src: 'https://casinodays2.imgix.net/games/gates-of-olympus-super-scatter.jpg?dpr=2&fit=crop&auto=format&w=200&h=240',
    },
  },
  slug: 'sample-game',
}

const mockGame2 = {
  name: 'Another Game',
  meta: {
    thumbnail: {
      src: 'https://casinodays2.imgix.net/games/gates-of-olympus-super-scatter.jpg?dpr=2&fit=crop&auto=format&w=200&h=240',
    },
  },
  slug: 'another-game',
}

const mockGame3 = {
  name: 'Third Game',
  meta: {
    thumbnail: {
      src: 'https://casinodays2.imgix.net/games/gates-of-olympus-super-scatter.jpg?dpr=2&fit=crop&auto=format&w=200&h=240',
    },
  },
  slug: 'third-game',
}

const meta: Meta<typeof GameTile> = {
  title: 'GameTile',
  component: GameTile,
  tags: ['autodocs'],
  args: {
    game: mockGame,
    priority: false,
    width: GAME_TILE_DEFAULT_WIDTH,
    height: GAME_TILE_DEFAULT_HEIGHT,
    isOverlayEnabled: true,
  },
  argTypes: {
    game: {
      control: 'object',
      description: 'Game object containing name, thumbnail, and slug',
    },
    priority: {
      control: 'boolean',
      description: 'Whether to prioritize image loading',
    },
    width: {
      control: { type: 'number', min: 100, max: 400, step: 10 },
      description: 'Width of the game tile',
    },
    height: {
      control: { type: 'number', min: 100, max: 500, step: 10 },
      description: 'Height of the game tile',
    },
    isOverlayEnabled: {
      control: 'boolean',
      description: 'Whether to show the overlay on hover',
    },
    gameTileInfo: {
      control: 'object',
      description: 'Optional game tile information (gameInfo, playerName, tokenAmount)',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    game: { table: { disable: true } },
    priority: { table: { disable: true } },
    width: { table: { disable: true } },
    height: { table: { disable: true } },
    isOverlayEnabled: { table: { disable: true } },
    gameTileInfo: { table: { disable: true } },
  },

  parameters: {
    docs: {
      description: {
        story: 'All game tile variants, sizes, and states',
      },
    },
  },

  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem', padding: '2rem' }}>
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Basic Variants</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Default Size</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame2} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Another Game</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame3} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Third Game</span>
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Different Sizes</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem', alignItems: 'end' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} width={150} height={200} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>150x200</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} width={205} height={273} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>205x273 (Default)</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} width={250} height={333} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>250x333</span>
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Overlay States</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Overlay Enabled</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Overlay Disabled</span>
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>With Game Info</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile
              game={mockGame}
              isOverlayEnabled={true}
              gameTileInfo={{
                gameInfo: 'Recently Played',
                playerName: 'John Doe',
                tokenAmount: '1,250',
              }}
            />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Full Info</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile
              game={mockGame2}
              isOverlayEnabled={true}
              gameTileInfo={{
                gameInfo: 'Hot Game',
              }}
            />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Game Info Only</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile
              game={mockGame3}
              isOverlayEnabled={true}
              gameTileInfo={{
                playerName: 'Jane Smith',
                tokenAmount: '2,500',
              }}
            />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Player Info Only</span>
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Priority Loading</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} priority={true} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Priority Loading</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <GameTile game={mockGame} priority={false} isOverlayEnabled={true} />
            <span style={{ fontSize: '0.8rem', color: '#666' }}>Lazy Loading</span>
          </div>
        </div>
      </div>
    </div>
  ),
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'A playground game tile with all properties available for customization',
      },
    },
  },
  args: {
    game: mockGame,
    priority: false,
    width: GAME_TILE_DEFAULT_WIDTH,
    height: GAME_TILE_DEFAULT_HEIGHT,
    isOverlayEnabled: true,
    gameTileInfo: {
      gameInfo: 'Recently Played',
      playerName: 'John Doe',
      tokenAmount: '1,250',
    },
  },
  render: args => (
    <div style={{ padding: '2rem', display: 'flex', justifyContent: 'center' }}>
      <GameTile {...args} />
    </div>
  ),
}
