'use client'
import React, { useState, useCallback } from 'react'
import { clsx } from 'clsx'
import { StarIcon } from 'lucide-react'
import { AnimatePresence, motion } from 'motion/react'
import { useRouter } from 'next/navigation'
import { Button } from '@components/Button'
import styles from '@components/GameTile/GameTile.module.scss'

type GameTileOverlayProps = {
  game: {
    name: string
    meta: {
      thumbnail: {
        src: string
      }
    }
    slug: string
  }
}

const GameTileOverlayContent: React.FC<{ game: GameTileOverlayProps['game'] }> = ({ game }) => {
  const router = useRouter()
  const gameLink = `/game/${game.slug}`

  const onPlayButtonClick = useCallback(() => {
    // TODO: Launch the real game - if the user is logged out, redirect to register
    router.push(gameLink)
  }, [router, gameLink])

  const onDemoButtonClick = useCallback(() => {
    // TODO: Launch the demo game
    router.push(gameLink)
  }, [gameLink, router])

  const onFavoriteButtonClick = useCallback(() => {
    // TODO: Push the game to favorites
    console.log(`Favorite button clicked for game: ${gameLink}`)
  }, [gameLink])

  return (
    <motion.div
      className={clsx(styles.hoverOverlay)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0, transition: { duration: 0.3 } }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}>
      <div className={clsx(styles.overlayBackdrop)} />
      <div className={styles.contentContainer}>
        <div className={styles.gameName}>{game.name}</div>
        <Button
          isIconOnly
          onClick={onFavoriteButtonClick}
          aria-label="Add to favorites"
          size="sm"
          color="secondary"
          className="p-2! w-10 h-10 shrink-0">
          <StarIcon width={16} height={16} />
        </Button>
      </div>
      <div className={styles.buttonsContainer}>
        <Button label="Play" color="primary" size="md" onClick={onPlayButtonClick} />
        <Button label="Demo" color="secondary" size="md" onClick={onDemoButtonClick} />
      </div>
    </motion.div>
  )
}

const GameTileOverlay: React.FC<GameTileOverlayProps> = ({ game }) => {
  const [isHovered, setIsHovered] = useState(false)

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  return (
    <div className={styles.overlayWrapper} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <AnimatePresence>{isHovered ? <GameTileOverlayContent game={game} /> : null}</AnimatePresence>
    </div>
  )
}

export default GameTileOverlay
