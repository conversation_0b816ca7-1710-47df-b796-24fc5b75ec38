import React from 'react'
import { DynamicLink } from '@components/DynamicLink'
import { GameTileOverlay } from '@components/GameTile'
import { GAME_TILE_DEFAULT_HEIGHT, GAME_TILE_DEFAULT_WIDTH } from '@components/GameTile/GameTile.settings'
import { ImgIx } from '@components/ImgIx'
import styles from '@components/GameTile/GameTile.module.scss'

type GameTileProps = {
  game: {
    name: string
    meta: {
      thumbnail: {
        src: string
      }
    }
    slug: string
  }
  priority?: boolean
  width?: number
  height?: number
  isOverlayEnabled?: boolean
  gameTileInfo?: {
    gameInfo?: string
    playerName?: string
    tokenAmount?: string
  }
}

const GameTile: React.FC<GameTileProps> = ({
  game,
  priority = false,
  width,
  height,
  isOverlayEnabled,
  gameTileInfo,
}) => {
  const gameTileWidth = width || GAME_TILE_DEFAULT_WIDTH
  const gameTileHeight = height || GAME_TILE_DEFAULT_HEIGHT
  const hasGameInfo = !!(gameTileInfo && Object.keys(gameTileInfo || {}).length)

  return (
    <div className={styles.wrapper}>
      <div className={styles.container} style={{ width: gameTileWidth, height: gameTileHeight }}>
        <DynamicLink href={`/game/${game.slug}`} prefetch={false} className={styles.link}>
          <div className={styles.imageContainer}>
            <ImgIx
              src={game.meta.thumbnail.src}
              alt={game.name}
              width={gameTileWidth}
              height={gameTileHeight}
              priority={priority}
              loading={priority ? 'eager' : 'lazy'}
              fetchPriority={priority ? 'high' : 'auto'}
              unoptimized
              fallbackText={game.name}
              containerClassName={styles.image}
            />
          </div>
        </DynamicLink>
        {!!isOverlayEnabled && <GameTileOverlay game={game} />}
      </div>

      {!!hasGameInfo && (
        <div className={styles.infoContainer}>
          {!!gameTileInfo.gameInfo && <p className={styles.gameInfo}>{gameTileInfo.gameInfo}</p>}
          {!!gameTileInfo.playerName && <p className={styles.playerName}>{gameTileInfo.playerName}</p>}
          {!!gameTileInfo.tokenAmount && (
            <div className={styles.tokenInfo}>
              <p className={styles.tokenIcon}>GC</p>
              <p className={styles.tokenAmount}>{gameTileInfo.tokenAmount}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default GameTile
