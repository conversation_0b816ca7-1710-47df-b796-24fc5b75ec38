/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$state-default-background: $color-surface-100;
$state-default-text: $color-surface-1000;
$state-default-supporting-text: $color-surface-800;
$state-active-background: $color-surface-100;
$state-active-text: $color-surface-1000;
$state-active-supporting-text: $color-surface-800;
$state-active-highlight: $color-tertiary-border;
$size-default-radius: $radius-md;
$size-default-padding: $size-xs;
$size-default-gap: $size-xs;