/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$default-background: rgba(31, 31, 31, 0.15);
$default-text: $color-surface-1000;
$default-icon-background: $color-primary;
$default-icon-foreground: $color-surface-100;
$size-sm-radius: $radius-sm;
$size-sm-padding: $size-xxs $size-xs $size-xxs $size-xxs;
$size-sm-gap: $size-xs;
$size-sm-icon-box: $font-size-lg;
$size-sm-icon: $icon-size-xxs;
$size-sm-font: $typography-body-baseline-xs;