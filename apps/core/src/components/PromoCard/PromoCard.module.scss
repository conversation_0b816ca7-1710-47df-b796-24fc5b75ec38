@use './PromoCard.sd.module' as *;
@use '@theme/variables' as *;
@use '@theme/functions.scss' as *;

.container {
  position: relative;
  width: 100%;
  border-radius: calculate-rem(8px);
  overflow: hidden;
  background-color: $color-surface-100;
  display: flex;
  flex-direction: column;
  margin-bottom: calculate-rem(16px);
  padding: calculate-rem(12px);
  gap: calculate-rem(20px);
}

.containerExpired {
  .imageContainer {
    filter: grayscale(100%);
  }

  .title,
  .description,
  .timerBlock > :first-child {
    color: $color-surface-800;
  }

  .infoLabelContainer {
    background: $color-surface-300;

    > :first-child {
      background: $color-surface-500;
    }
  }
}

.imageContainer {
  position: relative;
  width: 100%;
  height: calculate-rem(200px);
}

.image {
  border-radius: calculate-rem(12px);
}

.welcomeTag {
  left: calculate-rem(10px);
  background-color: $color-primary;
  color: $color-on-primary;
}

.content {
  padding: 0 calculate-rem(8px) calculate-rem(8px);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: calculate-rem(8px);
}

.title {
  font-size: calculate-rem(28px);
  font-weight: 700;
  margin: 0;
}

.description {
  font-size: calculate-rem(16px);
  line-height: 1.4;
  flex-grow: 1;
  color: $color-surface-800;

  p {
    margin: 0;
  }
}

.countdown {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(8px);
}

.countdownTimer {
  display: flex;
  align-items: flex-end;
  gap: calculate-rem(16px);
}

.learnMoreButton {
  display: flex;
  justify-content: flex-end;
  flex-grow: 1;
}
