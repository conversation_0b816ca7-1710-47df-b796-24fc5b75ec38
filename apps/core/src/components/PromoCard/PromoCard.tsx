'use client'
import { useCallback, useMemo, useState, type FC } from 'react'
import { clsx } from 'clsx'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { Button } from '@components/Button'
import { InfoLabel } from '@components/InfoLabel/InfoLabel'
import PromoCardSettings from '@components/PromoCard/PromoCard.settings'
import TimerClient from '@components/Timer/Timer.client'
import type { IRlPromotion } from '@repo/types/api/rl/promotions'
import styles from '@components/PromoCard/PromoCard.module.scss'

interface IPromoCardProps {
  promotion?: IRlPromotion
}

const PromoCard: FC<IPromoCardProps> = ({ promotion, ...props }) => {
  const [isExpired, setIsExpired] = useState(false)
  const router = useRouter()

  const onButtonClick = useCallback(
    (link: string) => {
      router.push(link)
    },
    [router],
  )

  // Format date to display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date)
  }

  const promotionPeriodInfo = useMemo(() => {
    if (!promotion) return ''
    const startDate = promotion.startAt ? formatDate(promotion.startAt) : ''
    const endDate = promotion.expiresAt ? formatDate(promotion.expiresAt) : 'Ongoing'
    return startDate ? `${startDate} - ${endDate}` : endDate
  }, [promotion])

  if (!promotion) {
    return null
  }

  const promotionCtas = promotion.ctas
  const ctaKeys = Object.keys(promotionCtas || {})
  const firstCta = ctaKeys.length > 0 ? promotionCtas[ctaKeys[0] as keyof typeof promotionCtas] : null

  const imageUrl = promotion.image?.desktop || promotion.image?.mobile || ''

  return (
    <div className={clsx(styles.container, isExpired && styles.containerExpired)}>
      <div className={styles.imageContainer}>
        <Image
          src={imageUrl}
          alt={promotion.name || 'Promotion'}
          fill
          style={{ objectFit: 'cover' }}
          sizes="(max-width: 768px) 100vw, (min-width: 769px) 30vw"
          className={styles.image}
        />
      </div>

      <div className={styles.content}>
        <InfoLabel
          iconUrl={
            isExpired ? PromoCardSettings.PROMOTION_INFO_FLAG_ICON_URL : PromoCardSettings.PROMOTION_PERIOD_ICON_URL
          }
          text={isExpired ? 'Finished' : promotionPeriodInfo}
          customClassName={isExpired ? styles.infoLabelContainer : ''}
        />
        <h3 className={styles.title}>{promotion.name}</h3>
        <div className={styles.description} dangerouslySetInnerHTML={{ __html: promotion.description }} />

        {!!promotion.expiresAt && (
          <div className={styles.countdown}>
            <div className={styles.countdownTimer}>
              <TimerClient
                startTime={new Date()}
                endTime={new Date(promotion.expiresAt)}
                onExpire={() => setIsExpired(true)}
                className={isExpired ? styles.timerBlock : ''}
              />

              {!!firstCta && !!firstCta.body && !isExpired && (
                <div className={styles.learnMoreButton}>
                  <Button
                    label={firstCta.body.name.toUpperCase()}
                    href={firstCta.body.link}
                    color={(firstCta.body.type?.toLowerCase() as 'primary' | 'secondary' | 'tertiary') || 'primary'}
                    onClick={() => onButtonClick(firstCta.body.link)}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PromoCard
