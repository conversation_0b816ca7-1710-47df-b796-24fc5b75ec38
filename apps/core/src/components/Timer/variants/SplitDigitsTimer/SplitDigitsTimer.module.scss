@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.countdownContainer {
  display: flex;
  gap: calculate-rem(4px);
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  max-width: 100%;
  position: relative;
  z-index: 1;
}

.timeBlock {
  background: $color-surface-200;
  border-radius: calculate-rem(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calculate-rem(44px);
  height: calculate-rem(44px);
  flex-shrink: 0;
  cursor: default;
}

.timeNumber {
  font-size: calculate-rem(18px);
  font-weight: 700;
  line-height: calculate-rem(22px);
  color: $color-on-tertiary;
  width: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-variant-numeric: tabular-nums;
  letter-spacing: calculate-rem(1px);
  cursor: default;
}

.timeLabel {
  font-size: calculate-rem(13px);
  font-weight: 500;
  color: $color-surface-800;
  line-height: calculate-rem(12px);
}
