'use client'
import type { <PERSON> } from 'react'
import { clsx } from 'clsx'
import type { ITimerDisplayProps } from '@components/Timer/variants/timer-display-props'
import { formatTimeValueWithLeadingZero, getTimeUnitLabel } from '@repo/helpers/timeHelpers'
import styles from '@components/Timer/variants/SplitDigitsTimer/SplitDigitsTimer.module.scss'

const SplitDigitsTimer: FC<ITimerDisplayProps> = ({ remainingTime, className }) => {
  return (
    <div className={styles.countdownContainer}>
      {Object.entries(remainingTime).map(([unit, value]) => (
        <div key={unit} className={clsx(styles.timeBlock, className)}>
          <div className={styles.timeNumber} suppressHydrationWarning>
            {formatTimeValueWithLeadingZero(value)}
          </div>
          <div className={styles.timeLabel} suppressHydrationWarning>
            {getTimeUnitLabel(unit)}
          </div>
        </div>
      ))}
    </div>
  )
}

export default SplitDigitsTimer
