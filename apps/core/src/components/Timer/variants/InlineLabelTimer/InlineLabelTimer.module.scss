/* stylelint-disable plugin/no-unsupported-browser-features */
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  background-color: $color-surface-100;
  border-radius: $radius-md;
  width: fit-content;
  display: flex;
  flex-direction: row;
  column-gap: 8px;
  padding: calculate-rem(6px) calculate-rem(8px);
  align-items: center;
}

.icon {
  color: $color-on-secondary;
}