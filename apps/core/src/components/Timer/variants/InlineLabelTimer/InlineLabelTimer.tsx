'use client'
import type { FC } from 'react'
import { clsx } from 'clsx'
import Image from 'next/image'
import type { ITimerDisplayProps } from '@components/Timer/variants/timer-display-props'
import type { ITimeObject } from '@repo/types/time'
import styles from '@components/Timer/variants/InlineLabelTimer/InlineLabelTimer.module.scss'
import TimerIcon from '../../../../../../core/public/assets/svg/Timer.svg'

const stringifyRemainingTime = (timeLeft: ITimeObject): string => {
  const entries = Object.entries(timeLeft)
  return (
    entries.reduce((acc, [unit, value], index) => {
      const formatted = `${value} ${unit}`
      const isLast = index === entries.length - 1
      const isSecondLast = index === entries.length - 2

      if (isLast) return acc + `and ${formatted}`
      return acc + formatted + (isSecondLast ? ' ' : ', ')
    }, '') + ' remaining'
  )
}

const InlineLabelTimer: FC<ITimerDisplayProps> = ({ remainingTime, className }) => {
  const remainingTimeString = stringifyRemainingTime(remainingTime)

  return (
    <div className={clsx(styles.container, className)} suppressHydrationWarning>
      <Image src={TimerIcon} alt="Timer Icon" className={styles.icon} />
      {remainingTimeString}
    </div>
  )
}

export default InlineLabelTimer
