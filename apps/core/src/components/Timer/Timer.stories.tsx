import { TimerVariantEnum } from '@components/Timer/consts'
import { TimerClient as Timer } from '@components/Timer/Timer.client'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof Timer> = {
  title: 'Timer',
  component: Timer,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Timer component with customizable variants for displaying countdown timers. Supports inline labels ' +
          'and split digits formats with automatic expiration handling.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    variant: TimerVariantEnum.INLINE_LABEL,
    startTime: new Date(),
    endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 12 * 60 * 60 * 1000 + 44 * 60 * 1000),
  },
  argTypes: {
    variant: {
      control: 'select',
      options: Object.values(TimerVariantEnum),
      description: 'Timer variant - can be configured globally via timer-component-global.json',
    },
    startTime: {
      control: 'date',
      description: 'The start time for the timer countdown',
    },
    endTime: {
      control: 'date',
      description: 'The end time for the timer countdown',
    },
    onExpire: {
      action: 'expired',
      description: 'Callback function called when timer reaches zero',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '350px',
          height: '200px',
          padding: '20px',
          borderRadius: '16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Timer>

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    startTime: { table: { disable: true } },
    endTime: { table: { disable: true } },
    onExpire: { table: { disable: true } },
    variant: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different timer variants showing inline labels and split digits formats',
      },
    },
  },
  render: () => {
    const now = new Date()

    // Different timer scenarios
    const endDate1 = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 + 12 * 60 * 60 * 1000 + 44 * 60 * 1000 + 9 * 1000)
    const endDate2 = new Date(now.getTime() + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 30 * 1000)
    const endDate3 = new Date(now.getTime() + 5 * 60 * 1000 + 15 * 1000)

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Timer Variants</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Inline Label Timer (2d 12h 44m 9s)
              </h4>
              <div
                style={{
                  background: '#f0f0f0',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                }}>
                <Timer variant={TimerVariantEnum.INLINE_LABEL} startTime={now} endTime={endDate1} />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Split Digits Timer (23h 59m 30s)
              </h4>
              <div
                style={{
                  background: '#f0f0f0',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                }}>
                <Timer variant={TimerVariantEnum.SPLIT_DIGITS} startTime={now} endTime={endDate2} />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Short Duration - Inline Label (5m 15s)
              </h4>
              <div
                style={{
                  background: '#fff3cd',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                  border: '2px solid #ffeaa7',
                }}>
                <Timer variant={TimerVariantEnum.INLINE_LABEL} startTime={now} endTime={endDate3} />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Short Duration - Split Digits (5m 15s)
              </h4>
              <div
                style={{
                  background: '#fff3cd',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                  border: '2px solid #ffeaa7',
                }}>
                <Timer variant={TimerVariantEnum.SPLIT_DIGITS} startTime={now} endTime={endDate3} />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive timer with customizable end date and variant selection',
      },
    },
  },
  args: {
    variant: TimerVariantEnum.INLINE_LABEL,
    startTime: new Date(),
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
  },
  render: args => (
    <div
      style={{
        background: 'lightgrey',
        padding: '40px',
        borderRadius: '16px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '120px',
      }}>
      <Timer {...args} />
    </div>
  ),
  argTypes: {
    startTime: { table: { disable: true } },
    endTime: { table: { disable: true } },
    onExpire: {
      action: 'timer-expired',
      description: 'Fires when timer reaches zero',
    },
    variant: {
      control: 'select',
      options: Object.values(TimerVariantEnum),
      description: 'Timer display variant',
    },
  },
}
