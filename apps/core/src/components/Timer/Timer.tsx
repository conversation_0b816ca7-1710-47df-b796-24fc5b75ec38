// Unused

import type { FC } from 'react'
import { getTimerComponentConfig } from '@/network/server-utils/s3/getters'
import type { TimerVariantEnum } from '@components/Timer/consts'
import TimerClient from '@components/Timer/Timer.client'

interface ITimerProps {
  variant?: TimerVariantEnum
  onExpire?: () => void
  className?: string
  startTime: Date
  endTime: Date
}

const Timer: FC<ITimerProps> = async ({ ...props }) => {
  const config = await getTimerComponentConfig()
  return <TimerClient variant={config?.variant as TimerVariantEnum} {...props} />
}

export default Timer
