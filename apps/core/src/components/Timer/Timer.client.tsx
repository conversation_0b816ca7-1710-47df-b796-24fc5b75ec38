'use client'
import { useCallback, useEffect, useMemo, useState, type FC } from 'react'
import { getTimerConfig } from '@components/Timer/actions'
import { TimerVariantEnum } from '@components/Timer/consts'
import { InlineLabelTimer } from '@components/Timer/variants/InlineLabelTimer'
import { SplitDigitsTimer } from '@components/Timer/variants/SplitDigitsTimer'
import { calculateTimeDifference } from '@repo/helpers/timeHelpers'
import type { GetTimerComponentConfigResponse } from '@repo/types/api/s3/timer-component'
import type { ITimeObject } from '@repo/types/time'

interface ITimerProps {
  variant?: TimerVariantEnum
  endTime: Date
  startTime: Date
  className?: string
  onExpire?: () => void
}

export const TimerClient: FC<ITimerProps> = ({ startTime, endTime, variant, className, onExpire }) => {
  const [remainingTime, setRemainingTime] = useState<ITimeObject>({
    years: 0,
    months: 0,
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })
  const [isExpired, setIsExpired] = useState(false)

  const updateTimeLeft = useCallback(() => {
    const updatedTimeLeft = calculateTimeDifference({ startTime, endTime })
    let isTimerExpired = false

    if (Object.values(updatedTimeLeft).every(value => value === 0)) {
      onExpire?.()
      isTimerExpired = true
      setIsExpired(true)
    }

    setRemainingTime(updatedTimeLeft)
    return isTimerExpired
  }, [endTime, startTime, onExpire])

  const getFilteredTimeUnits = useCallback((timeLeft: ITimeObject): ITimeObject => {
    const filteredTimeUnits: ITimeObject = { ...timeLeft }

    if (timeLeft.years === 0) {
      delete filteredTimeUnits.years
    }
    if (timeLeft.months === 0) {
      delete filteredTimeUnits.months
    }

    if ((timeLeft.years && timeLeft.years > 0) || (timeLeft.months && timeLeft.months > 0)) {
      delete filteredTimeUnits.seconds
    }

    return filteredTimeUnits
  }, [])

  const filteredRemainingTime = useMemo(
    () => getFilteredTimeUnits(remainingTime),
    [getFilteredTimeUnits, remainingTime],
  )

  useEffect(() => {
    updateTimeLeft()

    const timer = setInterval(() => {
      if (isExpired) {
        return
      }
      const isTimerExpired = updateTimeLeft()
      if (isTimerExpired) {
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [endTime, isExpired, updateTimeLeft])

  if (variant === TimerVariantEnum.SPLIT_DIGITS) {
    return <SplitDigitsTimer remainingTime={filteredRemainingTime} className={className} />
  }

  if (variant === TimerVariantEnum.INLINE_LABEL) {
    return <InlineLabelTimer remainingTime={filteredRemainingTime} className={className} />
  }

  console.error(`[Timer] Unsupported timer variant: ${variant}. Defaulting to SplitDigitsTimer.`)

  return <SplitDigitsTimer remainingTime={filteredRemainingTime} />
}

const TimerClientWithConfig: FC<Omit<ITimerProps, 'variant'>> = props => {
  const [config, setConfig] = useState<GetTimerComponentConfigResponse | null>(null)
  useEffect(() => {
    getTimerConfig().then(config => {
      setConfig(config)
    })
  }, [])
  const variant = config?.variant || TimerVariantEnum.SPLIT_DIGITS
  return <TimerClient variant={variant as TimerVariantEnum} {...props} />
}

export default TimerClientWithConfig
