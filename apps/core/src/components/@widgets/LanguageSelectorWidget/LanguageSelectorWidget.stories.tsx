import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/nextjs'
import LanguageSelector from '@widgets/LanguageSelectorWidget/LanguageSelectorWidget'

const meta: Meta<typeof LanguageSelector> = {
  title: 'Widgets/LanguageSelector',
  component: LanguageSelector,
  tags: ['autodocs'],
  parameters: {
    layout: 'padded',
  },
  args: {},
  argTypes: {
    locale: { table: { disable: true } },
  },
  decorators: [
    Story => (
      <div
        style={{
          maxWidth: '300px',
          padding: '20px',
          background: '#dedede',
          borderRadius: '8px',
          border: '1px solid #eee',
          boxSizing: 'border-box',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta

type Story = StoryObj<typeof meta>

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Showcases different display states of the LanguageSelector component',
      },
    },
  },
  render: () => {
    const sectionTitleStyle = {
      marginBottom: '0.8rem',
      fontSize: '1.1rem',
      fontWeight: 'bold',
      color: 'var(--color-surface-500)',
    }

    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '2rem',
        }}>
        <div>
          <h3 style={sectionTitleStyle}>Default State</h3>
          <LanguageSelector locale={Locale.EN} />
        </div>

        <div>
          <h3 style={sectionTitleStyle}>Few Languages *To be properly displayed*</h3>
          {/* TODO: Mock available languages endpoint to return a few languages */}
          <LanguageSelector locale={Locale.CA_FR} />
        </div>

        <div>
          <h3 style={sectionTitleStyle}>Pre-selected (French) *To be properly displayed*</h3>
          {/* TODO: Mock app language to French */}
          <LanguageSelector locale={Locale.EN} />
        </div>

        <div>
          <h3 style={sectionTitleStyle}>Long Language Label *To be properly displayed*</h3>
          {/* TODO: Mock available languages endpoint to return a language with a long label */}
          {/* TODO: Mock app language to set the long label language as preselected */}
          <LanguageSelector locale={Locale.EN} />
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground to test Lang.',
      },
    },
  },
  args: {
    locale: Locale.EN,
  },
  argTypes: {
    locale: {
      table: { disable: false },
    },
  },
  render: args => <LanguageSelector {...args} />,
}
