'use client'

import type { FC } from 'react'
import { useState } from 'react'
import { clsx } from 'clsx'
import { ChevronDownIcon } from '@/ui/icons/ChevronDownIcon'
import { CogIcon } from '@/ui/icons/CogIcon'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { Avatar } from '@heroui/avatar'
import { Select, SelectItem } from '@heroui/select'
import type { SharedSelection } from '@heroui/system'
import styles from '@widgets/LanguageSelectorWidget/LanguageSelectorWidget.module.scss'

const FLAG_DOMAIN = 'https://flagcdn.com'

const LanguageSelectorWidget: FC<IDynamicallyRenderedContentProps> = ({ locale }) => {
  // TODO: Fetch languages properly
  const languages = [
    { id: 'en', label: 'English', countryCode: 'gb' },
    { id: 'fr', label: 'French', countryCode: 'fr' },
    { id: 'de', label: 'German', countryCode: 'de' },
    { id: 'es', label: 'Spanish', countryCode: 'es' },
  ]
  const onChange = (newLanguage: any) => console.log('Language changed to ' + newLanguage.label)
  const defaultSelectedId = 'en'
  const defaultKey =
    (defaultSelectedId && languages.find(lang => lang.id === defaultSelectedId)?.id) || languages[0]?.id || 'en'

  const [selected, setSelectedLanguage] = useState<Set<string>>(new Set([defaultKey]))

  const selectedLanguage = languages.find(lang => selected.has(lang.id))

  const handleSelectedLanguage = (keys: SharedSelection) => {
    const keySet = keys as Set<string>
    setSelectedLanguage(keySet)

    if (onChange) {
      const newSelected = languages.find(lang => keySet.has(lang.id))
      onChange(newSelected)
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.labelContainer}>
        <CogIcon />
        <span className={clsx(styles.labelText)}>Language</span>
      </div>

      <Select
        selectionMode="single"
        selectedKeys={selected}
        onSelectionChange={handleSelectedLanguage}
        aria-label="Select Language"
        placeholder="Select language"
        className={styles.selectContainer}
        classNames={{
          trigger: styles.selectItem,
          value: styles.selectItemLabel,
          popoverContent: styles.selectPopoverContent,
        }}
        startContent={
          selectedLanguage ? (
            <div>
              <Avatar
                alt={selectedLanguage.label}
                className={styles.flagAvatar}
                src={`${FLAG_DOMAIN}/${selectedLanguage.countryCode}.svg`}
              />
            </div>
          ) : undefined
        }
        selectorIcon={<ChevronDownIcon />}>
        {languages.map(({ id, label, countryCode }) => (
          <SelectItem
            key={id}
            startContent={
              <Avatar alt={label} className={styles.flagAvatar} src={`${FLAG_DOMAIN}/${countryCode}.svg`} />
            }>
            {label}
          </SelectItem>
        ))}
      </Select>
    </div>
  )
}

export default LanguageSelectorWidget
