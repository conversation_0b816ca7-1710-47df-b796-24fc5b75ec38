@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  display: flex;
  gap: calculate-rem(8px);
  background-color: $color-background;
  padding: calculate-rem(12px) 0 calculate-rem(12px) calculate-rem(12px);
  border-radius: calculate-rem(6px);
}

.labelContainer {
  display: flex;
  align-items: center;
  gap: calculate-rem(6px);
}

.labelStyle {
  color: $color-surface-900;
  font-size: calculate-rem(13px);
  font-weight: 600;
}

.selectPopoverContent {
  @extend .labelStyle;
  background-color: $color-background;
}

.selectContainer {
  min-width: 0;
}

.selectItem,
.selectItemLabel {
  @extend .labelStyle;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.labelText,
.selectItemLabel {
  @extend .labelStyle;
}

.flagAvatar {
  width: calculate-rem(18px);
  height: calculate-rem(18px);
}
