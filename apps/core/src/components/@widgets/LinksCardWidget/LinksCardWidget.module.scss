@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.socialMediaCard {
  position: relative;
  gap: calculate-rem(20px);
  border-radius: calculate-rem(8px);
  overflow: hidden;
}

.backgroundImage {
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
}

.header {
  z-index: 1;
}

.header .title {
  white-space: pre-line;
  font-weight: 800;
  color: $color-on-tertiary;
  letter-spacing: calculate-rem(0.14px);
}

.content {
  justify-content: flex-start;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: calculate-rem(4px);
}
