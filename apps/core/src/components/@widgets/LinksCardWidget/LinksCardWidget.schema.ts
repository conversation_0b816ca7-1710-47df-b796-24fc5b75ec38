import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedLinksCardConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.LINKS_CARD),
  meta: z.object({
    headline: z.string(),
    backgroundUrl: z.string(),
    items: z.array(
      z.object({
        icon: z.string(),
        icon_dark: z.string(),
        alt_text: z.string(),
        href: z.string(),
      }),
    ),
  }),
})

export type DynamicallyRenderedLinksCardConfigType = z.infer<typeof DynamicallyRenderedLinksCardConfigSchema>['meta']
