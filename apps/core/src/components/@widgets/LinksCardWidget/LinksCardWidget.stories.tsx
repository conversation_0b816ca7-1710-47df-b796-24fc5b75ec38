import { Locale } from '@constants/locale'
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { LinksCardWidget } from '@widgets/LinksCardWidget'

const meta: Meta<typeof LinksCardWidget> = {
  title: 'Widgets/LinksCardWidget',
  component: LinksCardWidget,
  tags: ['autodocs'],
  args: {
    config: {
      headline: 'Check us out on social media',
      backgroundUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/promo.png',
      items: [
        {
          icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
          icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
          alt_text: 'Facebook',
          href: 'https://facebook.com',
        },
        {
          icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
          icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
          alt_text: 'Twitter',
          href: 'https://twitter.com',
        },
        {
          icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
          icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
          alt_text: 'Instagram',
          href: 'https://instagram.com',
        },
        {
          icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
          icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
          alt_text: 'YouTube',
          href: 'https://youtube.com',
        },
      ],
    },
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Meta object containing headline, backgroundUrl, and items',
    },
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Different display states of LinksCardWidget including full set, background-less, empty, and various icon counts.',
      },
    },
  },
  render: () => {
    const allItems = [
      {
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
        alt_text: 'Facebook',
        href: 'https://facebook.com',
      },
      {
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
        alt_text: 'Twitter',
        href: 'https://twitter.com',
      },
      {
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
        alt_text: 'Instagram',
        href: 'https://instagram.com',
      },
      {
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
        alt_text: 'YouTube',
        href: 'https://youtube.com',
      },
    ]
    const bg = 'https://luckyspins-staging4.imgix.net/icons/sweeps/promo.png'
    return (
      <div
        style={{
          width: '300px',
          borderRadius: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '1.5rem',
          padding: 16,
        }}>
        <LinksCardWidget
          config={{ headline: 'Default full set', backgroundUrl: bg, items: allItems }}
          locale={Locale.EN}
        />
        <LinksCardWidget
          config={{ headline: 'No background image', backgroundUrl: '', items: allItems }}
          locale={Locale.EN}
        />
        <LinksCardWidget config={{ headline: 'Empty items', backgroundUrl: bg, items: [] }} locale={Locale.EN} />
        <LinksCardWidget
          config={{
            headline: 'Very long title for layout testing in small components like this card that should not overflow.',
            backgroundUrl: bg,
            items: allItems,
          }}
          locale={Locale.EN}
        />
        <LinksCardWidget
          config={{ headline: 'Three icons', backgroundUrl: bg, items: allItems.slice(0, 3) }}
          locale={Locale.EN}
        />
        <LinksCardWidget
          config={{ headline: 'Two icons', backgroundUrl: bg, items: allItems.slice(0, 2) }}
          locale={Locale.EN}
        />
        <LinksCardWidget
          config={{ headline: 'One icon', backgroundUrl: bg, items: allItems.slice(0, 1) }}
          locale={Locale.EN}
        />
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground to test different props on the LinksCardWidget.',
      },
    },
  },
  args: {
    config: {
      headline: 'Try customizing me!',
      backgroundUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/promo.png',
      items: [
        {
          icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
          icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
          alt_text: 'Facebook',
          href: 'https://facebook.com',
        },
        {
          icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
          icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
          alt_text: 'Twitter',
          href: 'https://twitter.com',
        },
      ],
    },
  },
  render: args => (
    <div style={{ maxWidth: 300, padding: 16, borderRadius: 12 }}>
      <LinksCardWidget {...args} />
    </div>
  ),
}
