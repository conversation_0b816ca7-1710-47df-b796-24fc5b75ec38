'use client'
import type { FC } from 'react'
import { useTheme } from 'next-themes'
import { Button } from '@components/Button'
import { DynamicLink } from '@components/DynamicLink'

export interface ILinksCardWidgetCTAProps {
  icon: string
  icon_dark: string
  alt_text: string
  href: string
}

const LinksCardWidgetCTA: FC<ILinksCardWidgetCTAProps> = ({ icon, icon_dark, alt_text, href }) => {
  const { resolvedTheme: theme } = useTheme()
  const isDarkTheme = theme ? theme === 'dark' : true

  return (
    <Button
      as={DynamicLink}
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      aria-label={alt_text}
      color="overlay"
      size="sm"
      icon={isDarkTheme ? icon_dark : icon}
    />
  )
}

export default LinksCardWidgetCTA
