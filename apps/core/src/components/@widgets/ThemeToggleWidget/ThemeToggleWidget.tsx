import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { ThemeToggle } from '@components/ThemeToggle'
import styles from '@widgets/ThemeToggleWidget/ThemeToggleWidget.module.scss'

export const ThemeToggleWidget: FC<IDynamicallyRenderedContentProps> = ({ locale }) => {
  return (
    <div className={styles.container}>
      <ThemeToggle />
    </div>
  )
}
