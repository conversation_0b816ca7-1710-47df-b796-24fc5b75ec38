'use client'

import { useCallback } from 'react'
import { MenuIcon, PanelRightIcon } from 'lucide-react'
import { SidebarSideEnum } from '@repo/constants/common/sidebar'
import { Button } from '@repo/ui/shadcn/button'
import { useSidebar } from '@repo/ui/shadcn/sidebar'

interface ISidebarToggleWidgetProps {
  side: SidebarSideEnum
}

export const SidebarToggleWidget = ({ side }: ISidebarToggleWidgetProps) => {
  const { isOpen, setIsOpen } = useSidebar(side)

  const onToggleButtonClick = useCallback(() => {
    setIsOpen(!isOpen)
  }, [isOpen, setIsOpen])

  const Icon = side === SidebarSideEnum.LEFT ? MenuIcon : PanelRightIcon

  return (
    <Button
      data-sidebar="trigger"
      data-slot="sidebar-trigger"
      variant="ghost"
      size="icon"
      className="size-7"
      onClick={onToggleButtonClick}>
      <Icon />
      <span className="sr-only">Toggle {side === SidebarSideEnum.LEFT ? 'Left' : 'Right'} Sidebar</span>
    </Button>
  )
}
