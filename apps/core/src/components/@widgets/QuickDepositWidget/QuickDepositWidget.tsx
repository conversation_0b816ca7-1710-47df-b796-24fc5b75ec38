import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import styles from '@widgets/QuickDepositWidget/QuickDepositWidget.module.scss'

export const QuickDepositWidget: FC<IDynamicallyRenderedContentProps> = () => {
  return <div className={styles.container}>QuickDepositWidget</div>
}
