import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedCardConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.CARD),
  meta: z.object({
    headline: z.string(),
    subline: z.string(),
    backgroundUrl: z.string(),
    ctaTitle: z.string(),
    ctaAction: z.string(),
    bonusId: z.number(),
  }),
})

export type DynamicallyRenderedCardConfigType = z.infer<typeof DynamicallyRenderedCardConfigSchema>['meta']
