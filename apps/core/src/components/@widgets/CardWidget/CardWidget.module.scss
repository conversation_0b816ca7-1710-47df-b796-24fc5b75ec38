@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: auto;
  min-height: auto;
  position: relative;
  border-radius: calculate-rem(12px);
  overflow: hidden;
}

.featuredOfferBackground {
  position: absolute;
  top: 0;
  left: 0;
}

.featuredOffer {
  display: flex;
  flex-direction: column;
  padding: calculate-rem(16px);
  position: relative;
  z-index: 1;
}

.content {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
  width: 100%;
  min-height: auto;
}

.textContent {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(6px);
  max-width: 65%;
}

.headline {
  font-size: calculate-rem(24px);
  font-weight: 800;
  line-height: calculate-rem(20px);
  color: $color-on-tertiary;
  margin: 0;
}

.subline {
  font-size: calculate-rem(13px);
  font-weight: 500;
  line-height: calculate-rem(16px);
  color: $color-on-tertiary;
  margin: 0;
}

.ctaSection {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  gap: calculate-rem(8px);
  width: 100%;
  padding-bottom: calculate-rem(2px);
}

.countdownSection {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.featuredOfferWithoutIllustration {
  @extend .featuredOffer;
  flex-direction: row;
  gap: calculate-rem(6px);

  .ctaSection {
    justify-content: center;
  }

  .timer {
    flex-basis: calc(50% - calculate-rem(4px));
  }
}
