import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { CardWidgetClient } from '@widgets/CardWidget/CardWidget.client'

type CardWidgetStoryProps = {
  config: {
    bonusId: number
    headline: string
    subline?: string
    ctaTitle: string
    ctaAction: string
    backgroundUrl: string
  }
  initialBonus?: {
    id: number
    name: string
    expiresAt: string
    active?: boolean
    startAt?: string
    status?: string
  }
}

const meta: Meta<typeof CardWidgetClient> = {
  title: 'Widgets/CardWidget',
  component: CardWidgetClient,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A featured offer component for the sidebar that supports multiple states: initial (with CTA button), ' +
          'countdown (after claiming), and expired. Features Next.js optimized background images, responsive layout, ' +
          'and self-contained claim logic with optional post-claim callbacks. ' +
          'Now uses configuration object and initial data props.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      bonusId: 1,
      headline: 'Daily Bonus',
      subline: 'Make first purchase and receive free spins!',
      ctaTitle: 'Claim',
      ctaAction: 'claim-bonus',
      backgroundUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg',
    },
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Config object containing card display properties',
    },
    initialBonus: {
      control: 'object',
      description: 'Initial bonus data (optional) - used when bonus is already claimed',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '350px',
          minHeight: '180px',
          padding: '20px',
          borderRadius: '8px',
          position: 'relative',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
    initialBonus: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All CardWidget variants, layouts, and content variations using config and initialBonus props',
      },
    },
  },
  render: () => {
    const defaultBgImage = 'https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg'
    const customBgImage = 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Layout Variants</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Default Layout</h4>
              <div
                style={{
                  width: '350px',
                  minHeight: '180px',
                  padding: '20px',
                  borderRadius: '8px',
                  position: 'relative',
                }}>
                <CardWidgetClient
                  config={{
                    bonusId: 1,
                    headline: 'Daily Bonus',
                    subline: 'Make first purchase and receive free spins!',
                    ctaTitle: 'Claim',
                    ctaAction: 'claim-bonus',
                    backgroundUrl: defaultBgImage,
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Content Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Long Content Text</h4>
              <div
                style={{
                  width: '350px',
                  minHeight: '180px',
                  padding: '20px',
                  borderRadius: '8px',
                  position: 'relative',
                }}>
                <CardWidgetClient
                  config={{
                    bonusId: 3,
                    headline: 'Premium Weekend Special',
                    subline:
                      'This is a longer description that shows how the component handles more content with proper wrapping.',
                    ctaTitle: 'Activate Premium',
                    ctaAction: 'activate-premium',
                    backgroundUrl: defaultBgImage,
                  }}
                />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Different CTA Text</h4>
              <div
                style={{
                  width: '350px',
                  minHeight: '180px',
                  padding: '20px',
                  borderRadius: '8px',
                  position: 'relative',
                }}>
                <CardWidgetClient
                  config={{
                    bonusId: 4,
                    headline: 'VIP Bonus',
                    subline: 'Exclusive offer for VIP members with amazing rewards!',
                    ctaTitle: 'Unlock VIP Benefits',
                    ctaAction: 'unlock-vip',
                    backgroundUrl: defaultBgImage,
                  }}
                />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Custom Background Image</h4>
              <div
                style={{
                  width: '350px',
                  minHeight: '180px',
                  padding: '20px',
                  borderRadius: '8px',
                  position: 'relative',
                }}>
                <CardWidgetClient
                  config={{
                    bonusId: 5,
                    headline: 'Special Offer',
                    subline: 'Limited time bonus available now!',
                    ctaTitle: 'Get Bonus',
                    ctaAction: 'get-bonus',
                    backgroundUrl: customBgImage,
                  }}
                />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Claimed State (with Timer)
              </h4>
              <div
                style={{
                  width: '350px',
                  minHeight: '180px',
                  padding: '20px',
                  borderRadius: '8px',
                  position: 'relative',
                }}>
                <CardWidgetClient
                  config={{
                    bonusId: 1,
                    headline: 'Claimed Bonus',
                    subline: 'Your bonus is active and expires soon!',
                    ctaTitle: 'Claim',
                    ctaAction: 'claim-bonus',
                    backgroundUrl: defaultBgImage,
                  }}
                  initialBonus={{
                    id: 1,
                    name: 'Test Bonus',
                    expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
                    active: true,
                    startAt: '2025-01-01 17:00:00 GMT+2',
                    status: 'ACTIVE',
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive card widget with all properties available for customization via config object',
      },
    },
  },
  args: {
    config: {
      bonusId: 1,
      headline: 'Interactive Offer',
      subline: 'Customize all properties to see how the component responds!',
      ctaTitle: 'Claim Now',
      ctaAction: 'claim-now',
      backgroundUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg',
    },
  },
  render: args => (
    <div
      style={{
        width: '350px',
        minHeight: '180px',
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <CardWidgetClient {...args} />
    </div>
  ),
}
