import type { FC } from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { CardWidgetClient } from '@widgets/CardWidget/CardWidget.client'
import type { DynamicallyRenderedCardConfigType } from '@widgets/CardWidget/CardWidget.schema'

export interface ICardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedCardConfigType
}

const CardWidget: FC<ICardWidgetProps> = async ({ config, locale }) => {
  if (!config || !config.bonusId) {
    return null
  }

  // const bonus = await rlFetchApi.getBonus({ id: config.bonusId, locale, license: getLicenseByLocale(locale) })
  const bonus = {
    id: 1,
    name: 'Test Bonus',
    active: true,
    startAt: '2025-01-01 17:00:00 GMT+2',
    expiresAt: '2025-09-02 12:12:00 GMT+2',
    status: 'ACTIVE',
  }

  return <CardWidgetClient config={config} initialBonus={bonus || undefined} />
}

export default CardWidget
