import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/react'
import TextWidget from '@widgets/TextWidget/TextWidget'

const meta: Meta<typeof TextWidget> = {
  title: 'Widgets/TextWidget',
  component: TextWidget,
  parameters: {
    layout: 'padded',
  },
  decorators: [
    Story => (
      <div style={{ maxWidth: '600px', padding: '20px' }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Typography</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Heading text with semibold weight',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'semibold',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Body text with normal weight and secondary color',
              alignment: 'left',
              color: 'secondary',
              size: 'medium',
              weight: 'normal',
            }}
          />
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Alignment</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Left aligned text',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Center aligned text',
              alignment: 'center',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Right aligned text',
              alignment: 'right',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Colors</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Primary color',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Secondary color',
              alignment: 'left',
              color: 'secondary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Tertiary color',
              alignment: 'left',
              color: 'tertiary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Success color',
              alignment: 'left',
              color: 'success',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Error color',
              alignment: 'left',
              color: 'error',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Surface color',
              alignment: 'left',
              color: 'surface',
              size: 'medium',
              weight: 'normal',
            }}
          />
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Sizes</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Small text',
              alignment: 'left',
              color: 'primary',
              size: 'small',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Medium text',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Large text',
              alignment: 'left',
              color: 'primary',
              size: 'large',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Extra large text',
              alignment: 'left',
              color: 'primary',
              size: 'xl',
              weight: 'normal',
            }}
          />
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Weights</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Light weight',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'light',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Normal weight',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Medium weight',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'medium',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Semibold weight',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'semibold',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content: 'Bold weight',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'bold',
            }}
          />
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Rich Content</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <TextWidget
            locale={Locale.EN}
            config={{
              content:
                '<p>Features:</p><ul><li>Responsive design</li><li>Theme-aware colors</li><li>Rich text support</li></ul>',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content:
                '<p>Getting started:</p><ol><li>Configure the widget</li><li>Add your content</li><li>Deploy and enjoy</li></ol>',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
          <TextWidget
            locale={Locale.EN}
            config={{
              content:
                '<p>Visit our <a href="https://example.com/docs" target="_blank">documentation</a> for more information.</p>',
              alignment: 'left',
              color: 'primary',
              size: 'medium',
              weight: 'normal',
            }}
          />
        </div>
      </div>
    </div>
  ),
}

export const Playground: Story = {
  args: {
    locale: Locale.EN,
    config: {
      content: 'Customize this text widget using the controls below',
      alignment: 'left',
      color: 'primary',
      size: 'medium',
      weight: 'normal',
    },
  },
}
