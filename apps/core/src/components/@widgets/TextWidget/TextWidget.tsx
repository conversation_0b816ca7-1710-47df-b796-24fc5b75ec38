import type { FC } from 'react'
import { clsx } from 'clsx'
import { sanitizeHtml } from '@/utils/sanitizeHtml'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { DynamicallyRenderedTextConfigType } from '@widgets/TextWidget/TextWidget.schema'
import styles from '@widgets/TextWidget/TextWidget.module.scss'

export interface ITextWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedTextConfigType
}

const TextWidget: FC<ITextWidgetProps> = ({ config }) => {
  if (!config?.content) {
    return null
  }

  const {
    content,
    alignment = 'left',
    color = 'primary',
    size = 'medium',
    weight = 'normal',
    fontFamily,
    lineHeight,
    className,
  } = config

  const textClasses = clsx(
    styles.text,
    styles[alignment],
    styles[color],
    styles[`${size}Size`],
    styles[`${weight}Weight`],
    className,
  )

  const inlineStyles = {
    ...(fontFamily && { fontFamily }),
    ...(lineHeight && { lineHeight }),
  }

  const sanitizedContent = sanitizeHtml(content)

  if (!sanitizedContent.trim()) {
    return null
  }

  return <div className={textClasses} style={inlineStyles} dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
}

export default TextWidget
