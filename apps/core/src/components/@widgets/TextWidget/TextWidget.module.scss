@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

// Base text styles
.text {
  margin: 0;
  font-family: $font-family-primary;

  // Text alignment options
  &.left {
    text-align: left;
  }

  &.center {
    text-align: center;
  }

  &.right {
    text-align: right;
  }

  &.justify {
    text-align: justify;
  }

  // Color variants
  &.primary {
    color: $color-primary;
  }

  &.secondary {
    color: $color-secondary;
  }

  &.tertiary {
    color: $color-tertiary;
  }

  &.success {
    color: $color-success;
  }

  &.error {
    color: $color-error;
  }

  &.surface {
    color: $color-secondary;
  }

  // Font size variants
  &.smallSize {
    font-size: $font-size-xs;
  }

  &.mediumSize {
    font-size: $font-size-sm;
  }

  &.largeSize {
    font-size: $font-size-md;
  }

  &.xlSize {
    font-size: $font-size-lg;
  }

  // Font weight variants
  &.lightWeight,
  &.normalWeight,
  &.mediumWeight {
    font-weight: $font-weight-baseline;
  }

  &.semiboldWeight,
  &.boldWeight,
  &.extraboldWeight {
    font-weight: $font-weight-emphasized;
  }

  // HTML element styles for WYSIWYG editor content
  p {
    margin: 0 0 calculate-rem(8px) 0;
    line-height: $font-line-height-normal;
    font-size: inherit;
    color: inherit;

    &:last-child {
      margin-bottom: 0;
    }

    @media (max-width: 768px) {
      margin-bottom: calculate-rem(6px);
    }
  }

  ul, ol {
    margin: calculate-rem(8px) 0 calculate-rem(16px) calculate-rem(20px);
    padding: 0;
    line-height: $font-line-height-normal;
    font-size: inherit;
    color: inherit;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    margin-bottom: calculate-rem(4px);

    &:last-child {
      margin-bottom: 0;
    }
  }

  a {
    color: inherit;
    text-decoration: underline;
    transition: color 0.2s ease;
    font-size: inherit;

    &:hover {
      color: $color-primary;
    }
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: $font-weight-emphasized;
    line-height: $font-line-height-normal;
    font-size: inherit;
    color: inherit;
    
    @media (max-width: 768px) {
      margin-bottom: calculate-rem(8px);
    }
  }
}

.link {
  color: inherit;
  text-decoration: underline;
  transition: color 0.2s ease;
  font-size: inherit;

  &:hover {
    color: $color-primary;
  }
} 