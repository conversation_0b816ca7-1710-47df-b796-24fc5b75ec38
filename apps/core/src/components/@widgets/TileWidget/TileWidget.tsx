import type { FC } from 'react'
import React from 'react'
import clsx from 'clsx'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicLink } from '@components/DynamicLink'
import { ImgIx } from '@components/ImgIx'
import { Card, CardBody, CardFooter } from '@heroui/card'
import type { DynamicallyRenderedTileConfigType } from '@widgets/TileWidget/TileWidget.schema'
import styles from '@widgets/TileWidget/TileWidget.module.scss'

export interface ITileWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedTileConfigType
}

const TileWidget: FC<ITileWidgetProps> = ({ config, locale }) => {
  return (
    <Card isPressable as={DynamicLink} href={config.href} prefetch={false} className={clsx(styles.container)}>
      <CardBody className="overflow-visible p-0">
        {!!config?.backgroundUrl && (
          <ImgIx
            src={config.backgroundUrl}
            placeholder="url"
            width={300}
            unoptimized
            alt="card-background"
            style={{
              objectFit: 'cover',
            }}
          />
        )}
      </CardBody>
      <CardFooter className="py-1 text-white absolute before:rounded-xl rounded-large bottom-1 w-[calc(100%_-_8px)] ml-1 z-10">
        {config?.title}
      </CardFooter>
    </Card>
  )
}

export default TileWidget
