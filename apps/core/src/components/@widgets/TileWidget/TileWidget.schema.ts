import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedTileConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.TILE),
  meta: z.object({
    title: z.string().optional(),
    backgroundUrl: z.string(),
    href: z.string(),
    dataReference: z.string(),
  }),
})

export type DynamicallyRenderedTileConfigType = z.infer<typeof DynamicallyRenderedTileConfigSchema>['meta']
