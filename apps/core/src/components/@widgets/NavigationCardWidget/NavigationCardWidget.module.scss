@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.navigationCard {
  width: 100%;
  max-height: calculate-rem(299px);
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  border-radius: calculate-rem(8px);
  padding: calculate-rem(16px);
  position: relative;
  gap: calculate-rem(20px);

  @media (max-width: 768px) {
    min-height: calculate-rem(116px);
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(9px);

  @media (max-width: 768px) {
    gap: calculate-rem(2px);
  }
}

.iconTitle {
  display: flex;
  align-items: center;
  gap: calculate-rem(10px);

  @media (max-width: 768px) {
    gap: calculate-rem(6px);
  }
}

.title,
.description,
.content {
  z-index: 1;
}

.title {
  max-width: 60%;
  font-size: calculate-rem(24px);
  line-height: calculate-rem(24px);
  font-weight: 800;
  color: $color-secondary;
  @include line-clamp(2);

  @media (max-width: 768px) {
    max-width: 37%;
    line-height: calculate-rem(20px);
    font-size: calculate-rem(16px);
    word-wrap: break-word;
    @include line-clamp(2);
  }
}

.description {
  font-size: calculate-rem(15px);
  color: $color-surface-900;
  margin-bottom: calculate-rem(16px);
  max-width: 40%;
  word-wrap: break-word;
  @include line-clamp(8);

  @media (max-width: 768px) {
    font-size: calculate-rem(12px);
    @include line-clamp(2);
  }
}

.image {
  width: 100%;
  object-fit: cover;
  margin-bottom: calculate-rem(16px);
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
}

.ctaButton {
  display: flex;
}
