import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/react'
import NavigationCardDynamicComponent from '@widgets/NavigationCardWidget/NavigationCardWidget'

const meta: Meta<typeof NavigationCardDynamicComponent> = {
  title: 'Widgets/NavigationCardWidget',
  component: NavigationCardDynamicComponent,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A navigation card component with optional icon, title, description, background image, and CTA button. ' +
          'Supports dynamic configuration for content and visuals.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      title: 'Casino Games',
      description: 'Play exciting casino games and win big rewards.',
      ctaLabel: 'Start Playing',
      ctaHref: '/casino',
      iconSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/casino-chip.svg',
      imageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/casino.png',
    },
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Config object containing card display properties (title, description, image, CTA, icon)',
    },
    locale: {
      control: 'text',
      description: 'Optional market string for conditional rendering or data logic',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '308px',
          minHeight: '299px',
          padding: '20px',
          borderRadius: '8px',
          position: 'relative',
          background: 'transparent',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
    locale: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All NavigationCardDynamicComponent variants showcasing different content, icons, and backgrounds.',
      },
    },
  },
  render: () => {
    const defaultIcon = 'https://luckyspins-staging4.imgix.net/sweep-rush/casino-chip.svg'
    const defaultImage = 'https://luckyspins-staging4.imgix.net/sweep-rush/casino.png'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold', color: 'black' }}>
            Content Variations
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Default</h4>
              <NavigationCardDynamicComponent
                config={{
                  title: 'Casino',
                  description: 'Dive into a world of thrilling games',
                  ctaLabel: 'Try now',
                  ctaHref: '/casino',
                  iconSrc: defaultIcon,
                  imageSrc: defaultImage,
                }}
                locale={Locale.EN}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>No Icon Variation</h4>
              <NavigationCardDynamicComponent
                config={{
                  title: 'Promotions',
                  description: 'Check out our latest promotions and offers.',
                  ctaLabel: 'View Offers',
                  ctaHref: '/promotions',
                  imageSrc: defaultImage,
                }}
                locale={Locale.EN}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Long Description Text</h4>
              <NavigationCardDynamicComponent
                config={{
                  title: 'Weekly Tournaments',
                  description:
                    'Participate in our weekly tournaments and stand a chance to win amazing prizes with your gameplay skills.',
                  ctaLabel: 'Join Now',
                  ctaHref: '/tournaments',
                  iconSrc: defaultIcon,
                  imageSrc: defaultImage,
                }}
                locale={Locale.EN}
              />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive navigation card with all properties available for customization via config object.',
      },
    },
  },
  args: {
    config: {
      title: 'Interactive Card',
      description: 'Customize all properties to see how the component renders dynamically.',
      ctaLabel: 'Learn More',
      ctaHref: '/learn-more',
      iconSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/casino-chip.svg',
      imageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/casino.png',
    },
  },
  render: args => (
    <div
      style={{
        width: '308px',
        minHeight: '299px',
        background: 'transparent',
        padding: '20px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <NavigationCardDynamicComponent {...args} />
    </div>
  ),
}
