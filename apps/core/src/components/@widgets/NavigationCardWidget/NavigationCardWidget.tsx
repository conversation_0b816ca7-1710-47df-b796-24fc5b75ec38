import type { FC } from 'react'
import Image from 'next/image'
import { Button } from '@components/Button'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicLink } from '@components/DynamicLink'
import type { DynamicallyRenderedNavigationCardConfigType } from '@widgets/NavigationCardWidget/NavigationCardWidget.schema'
import styles from '@widgets/NavigationCardWidget/NavigationCardWidget.module.scss'

export interface INavigationCardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedNavigationCardConfigType
}

const NavigationCardWidget: FC<INavigationCardWidgetProps> = ({ config, locale }) => {
  if (!config) return null

  const { title, description, imageSrc, ctaLabel, ctaHref, iconSrc, ctaActionKey } = config
  // TODO: handle ctaActionKey for action-based navigation or tracking

  return (
    <div className={styles.navigationCard}>
      <div className={styles.content}>
        <div className={styles.iconTitle}>
          {iconSrc ? <Image src={iconSrc} alt={title} width={24} height={24} /> : null}
          <h3 className={styles.title}>{title}</h3>
        </div>

        {description ? <p className={styles.description}>{description}</p> : null}
      </div>

      {imageSrc ? <Image src={imageSrc} className={styles.image} quality={100} fill alt={title} /> : null}

      {ctaLabel ? (
        <div className={styles.ctaButton}>
          <Button
            as={ctaHref ? DynamicLink : 'button'}
            {...(ctaHref ? { href: ctaHref } : {})}
            label={ctaLabel}
            color="primary"
            size="sm"
          />
        </div>
      ) : null}
    </div>
  )
}

export default NavigationCardWidget
