import type { FC } from 'react'
import React from 'react'
import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import type { DynamicallyRenderedContainerConfigType } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicallyRenderedContainer } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { DynamicallyRenderedActionsContainerConfigType } from '@widgets/ActionsContainerWidget/ActionsContainerWidget.schema'

export interface IActionsContainerWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedActionsContainerConfigType
}

const ActionsContainerWidget: FC<IActionsContainerWidgetProps> = ({ config, locale }) => {
  const dynamicContainerConfig: DynamicallyRenderedContainerConfigType = {
    component: DynamicallyRenderedContainer.CONTAINER,
    meta: config,
  }
  return (
    <div className="bg-[var(--color-secondary)]">
      <DynamicContentRenderer config={dynamicContainerConfig} />
    </div>
  )
}

export default ActionsContainerWidget
