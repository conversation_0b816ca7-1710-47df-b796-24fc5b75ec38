import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedActionsContainerConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.ACTIONS_CONTAINER),
  meta: z.object({
    items: z.array(z.any()).min(1),
  }),
})

export type DynamicallyRenderedActionsContainerConfigType = z.infer<
  typeof DynamicallyRenderedActionsContainerConfigSchema
>['meta']
