import type { FC } from 'react'
import React from 'react'
import Image from 'next/image'
import { But<PERSON> } from '@components/Button'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { InfoLabel } from '@components/InfoLabel/InfoLabel'
import { Card, CardBody } from '@heroui/card'
import type { DynamicallyRenderedBannerConfigType } from '@widgets/BannerWidget/BannerWidget.schema'

export interface IBannerWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedBannerConfigType
}

const BannerWidget: FC<IBannerWidgetProps> = ({ config, locale }) => {
  return (
    <Card>
      <div style={{ position: 'relative', height: '300px' }}>
        {!!config?.backgroundUrl && (
          <Image alt="Dynamic tile background" src={config.backgroundUrl} style={{ objectFit: 'cover' }} fill />
        )}
      </div>
      <CardBody className="p-4 text-white absolute before:rounded-xl rounded-large">
        {!!config.labelText && <InfoLabel text={config.labelText} iconUrl={config.labelIcon || ''} />}
        {!!config.headline && <p>{config.headline}</p>}
        {!!config.subline && <p>{config.subline}</p>}
        {!!(config.primaryCtas || config.secondaryCtas) && (
          <div>
            {config.primaryCtas?.map((cta, index) => (
              <Button key={index} color="primary" label={cta.label} href={cta.href} />
            ))}
            {config.secondaryCtas?.map((cta, index) => (
              <Button key={index} color="secondary" icon={cta.icon} href={cta.href} />
            ))}
          </div>
        )}
      </CardBody>
    </Card>
  )
}

export default BannerWidget
