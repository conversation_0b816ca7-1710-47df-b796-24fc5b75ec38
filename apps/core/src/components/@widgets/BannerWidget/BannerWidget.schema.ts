import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

// TODO: Move to a shared file so it can be reused across different components
const CtaSchema = z
  .object({
    label: z.string().optional(),
    icon: z.string().optional(),
    href: z.string(),
  })
  .refine(data => data.label || data.icon, {
    message: "Either 'label' or 'icon' must be provided",
  })

export const DynamicallyRenderedBannerConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.BANNER),
  meta: z.object({
    backgroundUrl: z.string(),
    labelText: z.string().optional(),
    labelIcon: z.string().optional(),
    headline: z.string().optional(),
    subline: z.string().optional(),
    primaryCtas: z.array(CtaSchema).optional(),
    secondaryCtas: z.array(CtaSchema).optional(),
  }),
})

export type DynamicallyRenderedBannerConfigType = z.infer<typeof DynamicallyRenderedBannerConfigSchema>['meta']
