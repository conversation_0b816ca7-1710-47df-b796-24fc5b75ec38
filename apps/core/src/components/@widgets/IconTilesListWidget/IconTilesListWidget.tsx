import type { FC } from 'react'
import React from 'react'
import Image from 'next/image'
import { But<PERSON> } from '@components/Button'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { DynamicallyRenderedIconTilesListConfigType } from '@widgets/IconTilesListWidget/IconTilesListWidget.schema'

export interface IIconTilesListWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedIconTilesListConfigType
}

const IconTilesListWidget: FC<IIconTilesListWidgetProps> = ({ config, locale }) => {
  return (
    <div>
      <div className="flex flex-row justify-between">
        <div className="flex flex-row items-center">
          {!!config.icon && <Image src={config.icon} width={24} height={24} alt="icon" />}
          <p>{config.title}</p>
        </div>
        {/* show/hide dynamically based on the count */}
        <div>
          <Button label={config.control_button_label} href={config.control_button_href} color="secondary" size="sm" />
          {/* add prev/next chevron buttons based on items.length and per_page */}
        </div>
      </div>
      <div className="flex flex-row justify-evenly">
        {config.items.map((item, index) => (
          <div key={index} className="flex flex-col items-center">
            <Image src={item.icon} alt={item.alt_text} width={100} height={48} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default IconTilesListWidget
