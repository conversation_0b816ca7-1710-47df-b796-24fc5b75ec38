import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedIconTilesListConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.ICONS_LIST),
  meta: z.object({
    title: z.string(),
    icon: z.string().optional(),
    per_page: z.number().min(1).optional(),
    control_button_label: z.string(),
    control_button_href: z.string(),
    items: z
      .array(
        z.object({
          alt_text: z.string(),
          icon: z.string(),
          icon_dark: z.string(),
          href: z.string().optional(),
        }),
      )
      .min(1),
  }),
})

export type DynamicallyRenderedIconTilesListConfigType = z.infer<
  typeof DynamicallyRenderedIconTilesListConfigSchema
>['meta']
