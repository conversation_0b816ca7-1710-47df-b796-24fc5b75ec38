import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedNavigationLinkConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.NAVIGATION_LINK),
  meta: z.object({
    label: z.string(),
    href: z.string(),
    icon: z.string().optional(),
    icon_dark: z.string().optional(),
    badge: z.string().or(z.number()).optional(),
  }),
})

export type DynamicallyRenderedNavigationLinkConfigType = z.infer<
  typeof DynamicallyRenderedNavigationLinkConfigSchema
>['meta']
