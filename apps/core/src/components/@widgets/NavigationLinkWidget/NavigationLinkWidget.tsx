'use client'
import React from 'react'
import type { FC } from 'react'
import { clsx } from 'clsx'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicLink } from '@components/DynamicLink'
import { SidebarSideEnum } from '@repo/constants/common/sidebar'
import { useSidebar } from '@repo/ui/shadcn/sidebar'
import type { DynamicallyRenderedNavigationLinkConfigType } from '@widgets/NavigationLinkWidget/NavigationLinkWidget.schema'
import styles from '@widgets/NavigationLinkWidget/NavigationLinkWidget.module.scss'

export interface INavigationLinkWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedNavigationLinkConfigType
}

const NavigationLinkWidget: FC<INavigationLinkWidgetProps> = ({ config, locale }) => {
  const { resolvedTheme: theme } = useTheme()
  const isDarkMode = theme === 'dark'
  const sidebarLeft = useSidebar(SidebarSideEnum.LEFT)
  const sidebarRight = useSidebar(SidebarSideEnum.RIGHT)

  if (!config) return null

  const { href, icon, icon_dark, label, badge } = config
  const iconSrc = isDarkMode ? icon_dark : icon

  return (
    <DynamicLink
      href={href}
      withOpacity
      onClick={() => {
        if (sidebarLeft.isMobile) {
          sidebarLeft.setIsOpen(false)
        }
        if (sidebarRight.isMobile) {
          sidebarRight.setIsOpen(false)
        }
      }}
      scroll={false}
      className={clsx(styles.linkItem, !iconSrc && styles.noIconPadding)}>
      <div className={styles.iconLabel}>
        {iconSrc ? <Image src={iconSrc} width={16} height={16} alt={label} suppressHydrationWarning /> : null}
        <span className={styles.label}>{label}</span>
      </div>
      {badge ? <span className={styles.badge}>{badge}</span> : null}
    </DynamicLink>
  )
}

export default NavigationLinkWidget
