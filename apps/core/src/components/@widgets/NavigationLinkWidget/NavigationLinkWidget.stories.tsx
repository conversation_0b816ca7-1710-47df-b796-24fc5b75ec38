import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/nextjs'
import { NavigationLinkWidget } from '@widgets/NavigationLinkWidget'

const mockNavigationLink = {
  label: 'All slots',
  href: '#',
  icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
  icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
}

const meta: Meta<typeof NavigationLinkWidget> = {
  title: 'Widgets/NavigationLinkWidget',
  component: NavigationLinkWidget,
  tags: ['autodocs'],
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    config: {
      label: { control: 'text', description: 'Link label' },
      href: { control: 'text', description: 'Link URL' },
      icon: { control: 'text', description: 'Icon URL (light)' },
      icon_dark: { control: 'text', description: 'Icon URL (dark)' },
      badge: { control: 'text', description: 'Badge (string or number)' },
    },
  },
  decorators: [
    Story => (
      <div style={{ maxWidth: '280px', padding: '1rem' }}>
        <Story />
      </div>
    ),
  ],
}

export default meta

type Story = StoryObj<typeof meta>

export const Variants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      <div>
        <h4 style={{ margin: '0.5rem 0' }}>Default</h4>
        <NavigationLinkWidget config={mockNavigationLink} locale={Locale.EN} />
      </div>
      <div>
        <h4 style={{ margin: '0.5rem 0' }}>With String Badge</h4>
        <NavigationLinkWidget config={{ ...mockNavigationLink, badge: 'New' }} locale={Locale.EN} />
      </div>
      <div>
        <h4 style={{ margin: '0.5rem 0' }}>With Number Badge</h4>
        <NavigationLinkWidget config={{ ...mockNavigationLink, badge: 7 }} locale={Locale.EN} />
      </div>
      <div>
        <h4 style={{ margin: '0.5rem 0' }}>Without Icons</h4>
        <NavigationLinkWidget config={{ label: 'All Slots', href: '#' }} locale={Locale.EN} />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows NavigationLinkWidget with default, string badge, number badge, and no icon.',
      },
    },
  },
}

export const Playground: Story = {
  args: {
    config: mockNavigationLink,
  },
  render: args => <NavigationLinkWidget {...args} />,
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground for NavigationLinkWidget.',
      },
    },
  },
}
