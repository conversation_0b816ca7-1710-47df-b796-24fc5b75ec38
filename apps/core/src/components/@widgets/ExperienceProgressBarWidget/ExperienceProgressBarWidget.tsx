import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { Card, CardBody } from '@heroui/card'

export const ExperienceProgressBarWidget: FC<IDynamicallyRenderedContentProps> = ({ locale }) => {
  const ecrID = 123456789 // Fetch ecrID properly
  const { level = 38, progress = 0.65 } = {} // Fetch progress for ecrID via proper endpoint

  return (
    <Card className="bg-content2" shadow="none">
      <CardBody className="p-4 w-full max-w-md">
        <h5 className="font-semibold text-lg mb-2">XP Progress</h5>
        <div className="relative h-3 rounded-full bg-content3 overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-purple-500 to-pink-500 
          transition-all animate-in slide-in-from-left-100 duration-500 ease-in-out rounded-xl"
            style={{ width: `${progress * 100}%` }}
          />
        </div>
        <div className="flex justify-between items-center mt-4">
          <div className="bg-purple-600 text-white font-bold px-3 py-1 rounded-full text-sm">{level}</div>
          <div className="bg-[#2c2c2c] text-gray-400 font-bold px-3 py-1 rounded-full text-sm">{level + 1}</div>
        </div>
      </CardBody>
    </Card>
  )
}
