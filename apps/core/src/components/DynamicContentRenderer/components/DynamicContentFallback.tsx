import type { FC } from 'react'
import { Card } from '@heroui/card'
import styles from '@components/DynamicContentRenderer/components/DynamicContentFallback.module.scss'

export const DynamicContentFallback: FC = () => {
  return (
    <Card isDisabled className={styles.container} role="presentation">
      <p className={styles.headline}>Oops!</p>
      <p className={styles.subline}>Something went sideways. We can&apos;t render this content.</p>
    </Card>
  )
}
