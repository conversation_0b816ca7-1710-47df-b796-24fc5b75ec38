@use '@theme/variables.scss' as *;
@use '@theme/functions.scss' as *;

.container {
  background: alpha($color-surface-500, 0.5);
  color: $color-on-surface;
  text-align: center;
  border-radius: $radius-sm;
  padding: $size-xxs;
  text-wrap: balance;
  flex-grow: 1;
  align-content: center;
  justify-content: center;
}

.headline {
  font-size: $font-size-sm;
}

.subline {
  font-size: $font-size-xs;
}
