@use '@theme/variables.scss' as *;

.container {
  background-color: (in srgb, var(--color-surface-500) 50%, tracolor-mixnsparent);
  color: var(--color-on-secondary);
  text-align: center;
  border-radius: var(--radius-sm);
  padding: var(--size-xxs);
  text-wrap: balance;
  flex-grow: 1;
  align-content: center;
  justify-content: center;
}

.headline {
  font-size: var(--font-size-sm);
}

.subline {
  font-size: var(--font-size-xs);
}
