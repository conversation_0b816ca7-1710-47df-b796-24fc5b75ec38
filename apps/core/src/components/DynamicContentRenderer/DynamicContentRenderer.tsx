import type { FC } from 'react'
import React from 'react'
import { DynamicContentFallback } from '@components/DynamicContentRenderer/components/DynamicContentFallback'
import DynamicContentRendererConsts from '@components/DynamicContentRenderer/DynamicContentRenderer.consts'
import type { DynamicallyRenderedContentType } from '@components/DynamicContentRenderer/DynamicContentRenderer.derived-schema'
import {
  DynamicallyRenderedContainerConfigSchema,
  DynamicallyRenderedContentBaseRequiredConfigSchema,
} from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import type { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { checkDynamicRenderEligibility } from '@components/DynamicContentRenderer/utils/checkDynamicRenderEligibility'
import { componentRegistry } from '@components/DynamicContentRenderer/utils/componentRegistry'
import type { Locale } from '@constants/locale'

interface IRenderDynamicContentRecursivelyProps {
  contentConfig: DynamicallyRenderedContentType
  locale?: Locale
}

const renderDynamicContentRecursively = async ({ contentConfig, locale }: IRenderDynamicContentRecursivelyProps) => {
  const { error: baseValidationError } = DynamicallyRenderedContentBaseRequiredConfigSchema.safeParse(contentConfig)

  if (baseValidationError) {
    console.error('[DynamicallyRenderedContent] Base validation failed', { contentConfig, error: baseValidationError })
    return <DynamicContentFallback />
  }

  const shouldRender = await checkDynamicRenderEligibility(contentConfig.hide_for)

  if (!shouldRender) {
    return null
  }

  switch (contentConfig.component) {
    case 'container': {
      const { error: containerConfigError } = DynamicallyRenderedContainerConfigSchema.safeParse(contentConfig)

      if (containerConfigError) {
        console.error('[DynamicallyRenderedContent] Invalid container configuration:', containerConfigError)
        return <DynamicContentFallback />
      }

      const layout = contentConfig.meta.layout || 'vertical'
      const items = contentConfig.meta.items

      const content = await Promise.all(
        items.map(async (item, index) => (
          <React.Fragment key={'container-' + index}>
            {await renderDynamicContentRecursively({ contentConfig: item, locale })}
          </React.Fragment>
        )),
      )

      return <div className={`flex gap-2 ${layout === 'vertical' ? 'flex-col' : 'flex-row'}`}>{content}</div>
    }

    default: {
      const Component = componentRegistry[contentConfig.component as keyof typeof componentRegistry]

      if (!Component) {
        console.error(`[DynamicallyRenderedContent] Component "${contentConfig.component}" not found in registry`)
        return <DynamicContentFallback />
      }

      const WidgetSchemaByComponent =
        DynamicContentRendererConsts.COMPONENT_TO_SCHEMA_MAP[contentConfig.component as DynamicallyRenderedWidget]
      if (!WidgetSchemaByComponent) {
        console.error(`[DynamicallyRenderedContent] Unsupported "${contentConfig.component}" component`)
        return <DynamicContentFallback />
      }
      const { error: widgetConfigError } = WidgetSchemaByComponent.safeParse(contentConfig)
      if (widgetConfigError) {
        console.error('[DynamicallyRenderedContent] Invalid widget configuration:', widgetConfigError)
        return <DynamicContentFallback />
      }
      return (
        <div className="flex gap-2 flex-col flex-grow animate-in fade-in duration-300 ease-in-out">
          <Component locale={locale} {...('meta' in contentConfig ? { config: contentConfig.meta } : {})} />
        </div>
      )
    }
  }
}

interface IDynamicallyRenderedContentProps extends Omit<IRenderDynamicContentRecursivelyProps, 'contentConfig'> {
  config: IRenderDynamicContentRecursivelyProps['contentConfig']
}

const DynamicContentRenderer: FC<IDynamicallyRenderedContentProps> = async ({ config, locale }) => {
  return await renderDynamicContentRecursively({ contentConfig: config, locale })
}

export default DynamicContentRenderer
