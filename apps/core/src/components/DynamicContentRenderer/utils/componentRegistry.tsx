import dynamic from 'next/dynamic'
import { Banner } from '@components/Banner'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { LoadingComponentSkeleton } from '@components/DynamicContentRenderer/LoadingComponenSkeleton'
import { SideMenu } from '@modules/sidemenu'
import { withEnteringAnimation } from '@repo/ui/HOC/withEnteringAnimation'

const ExperienceProgressBarWidget = dynamic(
  () =>
    import('@widgets/ExperienceProgressBarWidget/ExperienceProgressBarWidget').then(mod =>
      withEnteringAnimation(mod.ExperienceProgressBarWidget),
    ),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const ThemeToggleWidget = dynamic(
  () => import('@widgets/ThemeToggleWidget/ThemeToggleWidget').then(mod => mod.ThemeToggleWidget),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const LanguageSelectorWidget = dynamic(
  () => import('@widgets/LanguageSelectorWidget').then(mod => withEnteringAnimation(mod.LanguageSelectorWidget)),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const PromotionsWidget = dynamic(() => import('@modules/promotions').then(mod => mod.PromotionsWidget), {
  loading: () => <LoadingComponentSkeleton height={100} />,
})

const WinnersWidget = dynamic(
  () => import('@modules/winners').then(mod => withEnteringAnimation(mod.WinnersWidget)),
  {
    loading: () => <LoadingComponentSkeleton height={100} />,
  },
)

const QuickDepositWidget = dynamic(
  () => import('@widgets/QuickDepositWidget/QuickDepositWidget').then(mod => mod.QuickDepositWidget),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const Slots = dynamic(() => import('@modules/slots').then(mod => mod.Slots), {
  loading: () => <LoadingComponentSkeleton height={400} />,
})

const TileWidget = dynamic(() => import('@widgets/TileWidget').then(mod => withEnteringAnimation(mod.TileWidget)), {
  loading: () => <LoadingComponentSkeleton height={60} />,
})

const NavigationLinkWidget = dynamic(
  () => import('@widgets/NavigationLinkWidget').then(mod => withEnteringAnimation(mod.NavigationLinkWidget)),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const NavigationCardWidget = dynamic(
  () => import('@widgets/NavigationCardWidget').then(mod => withEnteringAnimation(mod.NavigationCardWidget)),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const CardWidget = dynamic(() => import('@widgets/CardWidget').then(mod => withEnteringAnimation(mod.CardWidget)), {
  loading: () => <LoadingComponentSkeleton height={100} />,
})

const LinksCardWidget = dynamic(
  () => import('@widgets/LinksCardWidget').then(mod => withEnteringAnimation(mod.LinksCardWidget)),
  {
    loading: () => <LoadingComponentSkeleton height={100} />,
  },
)

const ActionsContainerWidget = dynamic(
  () => import('@widgets/ActionsContainerWidget').then(mod => mod.ActionsContainerWidget),
  { loading: () => <LoadingComponentSkeleton height={100} /> },
)

const Chat = dynamic(() => import('@modules/chat').then(mod => mod.Chat), {
  loading: () => <LoadingComponentSkeleton height={100} />,
})

// to be aligned with a Banner + welcome offer
const BannerWidget = dynamic(() => import('@widgets/BannerWidget').then(mod => mod.BannerWidget), {
  loading: () => <LoadingComponentSkeleton height={400} />,
})

const IconTilesListWidget = dynamic(() => import('@widgets/IconTilesListWidget').then(mod => mod.IconTilesListWidget), {
  loading: () => <LoadingComponentSkeleton height={50} />,
})

const TextWidget = dynamic(() => import('@widgets/TextWidget').then(mod => withEnteringAnimation(mod.TextWidget)), {
  loading: () => <LoadingComponentSkeleton height={100} />,
})

const RewardsWidget = dynamic(() => import('@modules/rewards').then(mod => mod.RewardsWidget), {
  loading: () => <LoadingComponentSkeleton height={250} />,
})

const widgets = {
  ExperienceProgressBar: ExperienceProgressBarWidget,
  ThemeToggleExpanded: ThemeToggleWidget,
  LanguageSelector: LanguageSelectorWidget,
  Promotions: PromotionsWidget,
  Winners: WinnersWidget,
  QuickDeposit: QuickDepositWidget,
  Slots: Slots,
  Tile: TileWidget,
  NavigationLink: NavigationLinkWidget,
  NavigationCard: NavigationCardWidget,
  Card: CardWidget,
  LinksCard: LinksCardWidget,
  ActionsContainer: ActionsContainerWidget,
  Banner: Banner,
  IconTilesList: IconTilesListWidget,
  Text: TextWidget,
  Rewards: RewardsWidget,
}

const modules = {
  SideMenu: SideMenu,
  Chat: Chat,
}

export const componentRegistry: Record<string, React.ComponentType<any>> = {
  ...widgets,
  ...modules,
}

type ComponentName = keyof typeof componentRegistry

export function getComponent(componentName: string): React.ComponentType<IDynamicallyRenderedContentProps> | undefined {
  return componentRegistry[componentName as ComponentName] as
    | React.ComponentType<IDynamicallyRenderedContentProps>
    | undefined
}
