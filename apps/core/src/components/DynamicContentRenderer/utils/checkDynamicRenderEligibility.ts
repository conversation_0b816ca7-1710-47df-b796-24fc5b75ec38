import { headers } from 'next/headers'
import { authService } from '@/services/auth.service'
import {
  DynamicallyRenderedContentHideAuthSchema,
  DynamicallyRenderedContentHideDeviceSchema,
  type DynamicallyRenderedContentHideType,
} from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { AuthTypeEnum } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { detectDeviceType } from '@repo/helpers/deviceHelpers'

export const checkDynamicRenderEligibility = async (hideFor?: DynamicallyRenderedContentHideType): Promise<boolean> => {
  if (!hideFor) {
    return true
  }

  const { error: authSchemaError } = DynamicallyRenderedContentHideAuthSchema.safeParse(hideFor.auth)

  if (authSchemaError) {
    console.error('[checkDynamicRenderEligibility] Auth schema validation failed', {
      config: hideFor.auth,
      error: authSchemaError,
    })
  } else if (hideFor.auth) {
    const isAuthenticated = await authService.isAuthenticated()
    const authState = isAuthenticated ? AuthTypeEnum.AUTHENTICATED : AuthTypeEnum.UNAUTHENTICATED
    return hideFor.auth !== authState
  }

  const { error: deviceSchemaError } = DynamicallyRenderedContentHideDeviceSchema.safeParse(hideFor.devices)

  if (deviceSchemaError) {
    console.error('[checkDynamicRenderEligibility] Device schema validation failed', {
      config: hideFor.devices,
      error: deviceSchemaError,
    })
  } else if (hideFor.devices && hideFor.devices.length > 0) {
    const _headers = await headers()
    const ua = _headers.get('user-agent') || ''
    const currentDevice = detectDeviceType(ua)

    if (hideFor.devices.includes(currentDevice)) {
      return false
    }
  }

  return true
}
