import { z } from 'zod'
import { DynamicallyRenderedContainerConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedChatConfigSchema } from '@modules/chat'
import { DynamicallyRenderedRewardsConfigSchema } from '@modules/rewards'
import { DynamicallyRenderedSideMenuConfigSchema } from '@modules/sidemenu'
import { DynamicallyRenderedWinnersConfigSchema } from '@modules/winners'
import { DynamicallyRenderedActionsContainerConfigSchema } from '@widgets/ActionsContainerWidget/ActionsContainerWidget.schema'
import { DynamicallyRenderedCardConfigSchema } from '@widgets/CardWidget/CardWidget.schema'
import { DynamicallyRenderedExperienceProgressBarConfigSchema } from '@widgets/ExperienceProgressBarWidget/ExperienceProgressBarWidget.schema'
import { DynamicallyRenderedLanguageSelectorConfigSchema } from '@widgets/LanguageSelectorWidget/LanguageSelectorWidget.schema'
import { DynamicallyRenderedLinksCardConfigSchema } from '@widgets/LinksCardWidget/LinksCardWidget.schema'
import { DynamicallyRenderedNavigationLinkConfigSchema } from '@widgets/NavigationLinkWidget/NavigationLinkWidget.schema'
import { DynamicallyRenderedTextConfigSchema } from '@widgets/TextWidget/TextWidget.schema'
import { DynamicallyRenderedThemeToggleExpandedConfigSchema } from '@widgets/ThemeToggleWidget/ThemeToggleWidget.schema'
import { DynamicallyRenderedTileConfigSchema } from '@widgets/TileWidget/TileWidget.schema'

export const DynamicallyRenderedContentConfigSchema = z.discriminatedUnion('component', [
  DynamicallyRenderedContainerConfigSchema,
  DynamicallyRenderedTileConfigSchema,
  DynamicallyRenderedNavigationLinkConfigSchema,
  DynamicallyRenderedLinksCardConfigSchema,
  DynamicallyRenderedCardConfigSchema,
  DynamicallyRenderedActionsContainerConfigSchema,
  DynamicallyRenderedExperienceProgressBarConfigSchema,
  DynamicallyRenderedLanguageSelectorConfigSchema,
  DynamicallyRenderedThemeToggleExpandedConfigSchema,
  DynamicallyRenderedTextConfigSchema,
  DynamicallyRenderedSideMenuConfigSchema,
  DynamicallyRenderedChatConfigSchema,
  DynamicallyRenderedRewardsConfigSchema,
  DynamicallyRenderedWinnersConfigSchema,
])

export type DynamicallyRenderedContentType = z.infer<typeof DynamicallyRenderedContentConfigSchema>
