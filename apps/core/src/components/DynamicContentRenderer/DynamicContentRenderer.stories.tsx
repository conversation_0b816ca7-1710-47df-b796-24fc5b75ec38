import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import type { DynamicallyRenderedContentType } from '@components/DynamicContentRenderer/DynamicContentRenderer.derived-schema'
import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/nextjs'
// Import the JSON configs directly

const meta: Meta<typeof DynamicContentRenderer> = {
  title: 'DynamicContentRenderer',
  component: DynamicContentRenderer,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'The DynamicContentRenderer component renders dynamic content based on configuration objects. ' +
          'It supports various widget types and container layouts.',
      },
    },
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration object that defines the content to render',
    },
    locale: {
      control: 'select',
      options: [Locale.EN, Locale.US, Locale.CA_FR],
      description: 'Locale for internationalization',
    },
  },
}

export default meta
type Story = StoryObj<typeof DynamicContentRenderer>

const sideMenu = {
  component: 'container',
  id: 'side-menu',
  meta: {
    layout: 'vertical',
    items: [
      {
        id: 'sidemenu-social-center-container',
        component: 'container',
        meta: {
          layout: 'vertical',
          title: 'Social Center',
          items: [
            {
              id: 'sidemenu-social-center-promotions-tile',
              component: 'Tile',
              meta: {
                title: 'Promo',
                backgroundUrl: 'https://casinodays2-staging.imgix.net/sweeps-temp/promo.svg',
                href: '/promotions',
                dataReference: 'promotions',
              },
            },
            {
              id: 'sidemenu-social-center-referral-and-leaderboard-container',
              component: 'container',
              meta: {
                layout: 'horizontal',
                items: [
                  {
                    id: 'sidemenu-social-center-referral-tile',
                    component: 'Tile',
                    meta: {
                      title: 'Refer a friend',
                      href: '/referral',
                      backgroundUrl: 'https://casinodays2-staging.imgix.net/sweeps-temp/referral.svg',
                      dataReference: 'referrals',
                    },
                  },
                  {
                    id: 'sidemenu-social-center-leaderboard-tile',
                    component: 'Tile',
                    meta: {
                      title: 'Leaderboard',
                      href: '/leaderboard',
                      backgroundUrl: 'https://casinodays2-staging.imgix.net/sweeps-temp/leaderboard.svg',
                      dataReference: 'leaderboard',
                    },
                  },
                ],
              },
            },
          ],
        },
      },
      {
        id: 'sidemenu-experience-progress',
        component: 'ExperienceProgressBar',
        hide_for: {
          auth: 'authenticated',
        },
      },
      {
        id: 'sidemenu-game-category-container',
        component: 'container',
        meta: {
          layout: 'vertical',
          title: 'Games',
          items: [
            {
              id: 'sidemenu-game-category-slots-link',
              component: 'NavigationLink',
              meta: {
                label: 'All slots',
                href: '/casino#all-slots',
                icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
                icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              },
            },
            {
              id: 'sidemenu-game-category-featured-link',
              component: 'NavigationLink',
              meta: {
                label: 'Featured',
                href: '/casino#featured',
                icon: 'https://luckyspins4.imgix.net/icons/cherries-dark.svg',
                icon_dark: 'https://buustikasino.imgix.net/icons/cherries.svg',
              },
            },
            {
              id: 'sidemenu-game-category-hold-and-win-link',
              component: 'NavigationLink',
              meta: {
                label: 'Hold & Win',
                href: '/casino#hold-and-win',
                icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
                icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              },
            },
            {
              id: 'sidemenu-game-category-new-link',
              component: 'NavigationLink',
              meta: {
                label: 'New',
                href: '/live-casino#new',
                icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
                icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              },
            },
          ],
        },
      },
      // TODO: Check why this causes issues, probably need to mock bonus req
      //   {
      //     id: 'sidemenu-daily-bonus',
      //     component: 'Card',
      //     meta: {
      //       headline: 'Daily Bonus',
      //       subline: 'Make first purchase and receive free spins!',
      //       backgroundUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.png',
      //       ctaTitle: 'Claim',
      //       ctaAction: 'claim-bonus',
      //       bonusId: 1,
      //     },
      //   },
      {
        id: 'sidemenu-social-center-jackpot-and-challenges-container',
        component: 'container',
        meta: {
          layout: 'horizontal',
          items: [
            {
              id: 'sidemenu-social-center-jackpot-tile',
              component: 'Tile',
              meta: {
                title: 'Jackpot',
                href: '/casino#jackpot',
                backgroundUrl: 'https://casinodays2-staging.imgix.net/sweeps-temp/jackpot.svg',
                dataReference: 'jackpot',
              },
            },
            {
              id: 'sidemenu-social-center-challenges-tile',
              component: 'Tile',
              meta: {
                title: 'Challenge',
                href: '/challenges',
                backgroundUrl: 'https://casinodays2-staging.imgix.net/sweeps-temp/challenge.svg',
                dataReference: 'challenges',
              },
            },
          ],
        },
      },
      {
        id: 'sidemenu-social-media',
        component: 'LinksCard',
        meta: {
          headline: 'Check us out on social media',
          backgroundUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/promo.png',
          items: [
            {
              alt_text: 'Casino Facebook',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              href: 'https://www.facebook.com/casinodays/',
            },
            {
              alt_text: 'Casino Instagram',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              href: 'https://www.instagram.com/casinodays/',
            },
            {
              alt_text: 'Casino Twitter',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              href: 'https://twitter.com/casinodays',
            },
            {
              alt_text: 'Casino YouTube',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              href: 'https://www.youtube.com/@CasinoDays',
            },
          ],
        },
      },
      {
        id: 'sidemenu-misc',
        component: 'ActionsContainer',
        meta: {
          items: [
            {
              id: 'sidemenu-misc-language-selector',
              component: 'LanguageSelector',
            },
            {
              id: 'sidemenu-misc-support-link',
              component: 'NavigationLink',
              meta: {
                label: 'Support',
                href: '/support',
                icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
                icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
              },
            },
            {
              id: 'sidemenu-misc-theme-toggle',
              component: 'ThemeToggleExpanded',
              hide_for: {
                devices: ['desktop'],
              },
            },
          ],
        },
      },
    ],
  },
}

export const Sidemenu: Story = {
  args: {
    config: sideMenu as DynamicallyRenderedContentType,
    locale: Locale.EN,
  },
  decorators: [
    Story => (
      <div style={{ width: '280px' }}>
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          'Homepage configuration showing a typical layout with banners, promotional sections, game sections, ' +
          'and provider lists. Demonstrates complex nested structures and conditional rendering.',
      },
    },
  },
}

const homePage = {
  component: 'container',
  id: 'homepage',
  meta: {
    layout: 'vertical',
    items: [
      {
        id: 'homepage-winners-list',
        component: 'WinnersList',
      },
      {
        id: 'homepage-banner',
        component: 'Banner',
        meta: {
          backgroundUrl:
            'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png',
          labelText: 'Welcome bonus',
          labelIcon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
          headline: 'Welcome to Luckyone.us - your gateway to exciting rewards!',
          subline: 'Make your first purchase today and unlock exclusive free spins',
          primaryCtas: [
            {
              label: 'Register now',
              href: '/register',
            },
          ],
          secondaryCtas: [
            {
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/facebook.svg',
              href: '/register?ref=facebook',
            },
            {
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/google.svg',
              href: '/register?ref=google',
            },
          ],
        },
        hide_for: {
          auth: 'authenticated',
        },
      },
      {
        id: 'homepage-promotional-section-container',
        component: 'container',
        meta: {
          layout: 'horizontal',
          items: [
            {
              id: 'homepage-promotional-section-navigation-cards-container',
              component: 'container',
              meta: {
                layout: 'horizontal',
                items: [
                  {
                    id: 'homepage-promotional-section-navigation-cards-casino',
                    component: 'NavigationCard',
                    meta: {
                      title: 'Casino',
                      description: 'Dive into the world of thrilling games.',
                      imageSrc:
                        'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/casino-card.svg',
                      ctaLabel: 'Try now',
                      ctaHref: '/casino',
                      iconSrc:
                        'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/casino-chip.svg',
                    },
                  },
                  {
                    id: 'homepage-promotional-section-navigation-cards-promotions',
                    component: 'NavigationCard',
                    meta: {
                      title: 'Promotions',
                      description: 'Discover our latest promotions and offers.',
                      imageSrc:
                        'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/promotions-card.svg',
                      ctaLabel: 'Read more',
                      ctaHref: '/promotions',
                      iconSrc:
                        'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/megaphone.svg',
                    },
                  },
                ],
              },
            },
            {
              id: 'homepage-promotional-section-jackpot',
              component: 'Jackpot',
            },
          ],
        },
      },
      //   {
      //     id: 'homepage-slots',
      //     component: 'Slots',
      //   },
      {
        id: 'homepage-payment-info',
        component: 'InfoCard',
        meta: {
          headline: 'Payment Methods',
          subline: 'We service more than 50 payment methods, including crypto',
          items: [
            {
              alt_text: 'Visa',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
            },
            {
              alt_text: 'Mastercard',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
            },
            {
              alt_text: 'Discover',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
            },
            {
              alt_text: 'Apple Pay',
              icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
              icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
            },
          ],
          ctaTitle: 'Purchase now',
          ctaHref: '/cashier',
        },
      },
      {
        id: 'homepage-gamesection-new',
        component: 'GameSection',
        meta: {
          id: 1,
        },
      },
      {
        id: 'homepage-gamesection-bonus-buy',
        component: 'GameSection',
        meta: {
          id: 2,
        },
      },
      {
        id: 'homepage-gamesection-providers-list',
        component: 'IconTilesList',
        meta: {
          title: 'Providers',
          icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/game-controller.svg',
          per_page: 6,
          control_button_label: 'See all',
          control_button_href: '/providers',
          items: [
            {
              alt_text: 'BGaming',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/bgaming.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/bgaming.svg',
            },
            {
              alt_text: 'AE Sexy',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/ae-sexy.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/ae-sexy.svg',
            },
            {
              alt_text: 'BGaming',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/bgaming.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/bgaming.svg',
            },
            {
              alt_text: 'AE Sexy',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/ae-sexy.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/ae-sexy.svg',
            },
            {
              alt_text: 'BGaming',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/bgaming.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/bgaming.svg',
            },
            {
              alt_text: 'AE Sexy',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/ae-sexy.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-providers-icons/ae-sexy.svg',
            },
          ],
        },
      },
    ],
  },
}

export const Page: Story = {
  args: {
    config: homePage as DynamicallyRenderedContentType,
    locale: Locale.EN,
  },
  decorators: [
    Story => (
      <div style={{ width: '100%' }}>
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          'Homepage configuration showing a typical layout with banners, promotional sections, game sections, ' +
          'and provider lists. Demonstrates complex nested structures and conditional rendering.',
      },
    },
  },
}
