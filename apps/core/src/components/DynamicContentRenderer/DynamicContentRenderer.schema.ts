import { z } from 'zod'
import {
  AuthTypeEnum,
  DynamicallyRenderedContainer,
  LayoutEnum,
} from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DeviceTypeEnum } from '@repo/helpers/deviceHelpers'

const LayoutEnumZ = z.nativeEnum(LayoutEnum)

export const DynamicallyRenderedContentHideAuthSchema = z.nativeEnum(AuthTypeEnum).optional()
export const DynamicallyRenderedContentHideDeviceSchema = z.array(z.nativeEnum(DeviceTypeEnum)).min(1).optional()
export const DynamicallyRenderedContentHideSchema = z.object({
  auth: DynamicallyRenderedContentHideAuthSchema,
  devices: DynamicallyRenderedContentHideDeviceSchema,
})
export type DynamicallyRenderedContentHideType = z.infer<typeof DynamicallyRenderedContentHideSchema>

export const DynamicallyRenderedContentBaseRequiredConfigSchema = z.object({
  component: z.string(),
})

export const DynamicallyRenderedContentBaseConfigSchema = DynamicallyRenderedContentBaseRequiredConfigSchema.extend({
  id: z.string().optional(),
  hide_for: DynamicallyRenderedContentHideSchema.optional(),
})

export const DynamicallyRenderedContainerConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedContainer.CONTAINER),
  title: z.string().optional(),
  meta: z.object({
    layout: LayoutEnumZ.optional(),
    items: z.array(z.any()).min(1),
  }),
})
export type DynamicallyRenderedContainerConfigType = z.infer<typeof DynamicallyRenderedContainerConfigSchema>
