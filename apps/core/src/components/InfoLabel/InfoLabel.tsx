import type { FC } from 'react'
import { clsx } from 'clsx'
import Image from 'next/image'
import styles from '@components/InfoLabel/InfoLabel.module.scss'

interface IInfoLabelProps {
  iconUrl: string
  text: string
  customClassName?: string
}

export const InfoLabel: FC<IInfoLabelProps> = ({ iconUrl, text, customClassName }) => (
  <div className={clsx(styles.container, customClassName && customClassName)}>
    <div className={styles.iconWrapper}>
      <Image src={iconUrl} width={24} height={24} alt="icon" />
    </div>
    <div className={styles.contentWrapper}>{text}</div>
  </div>
)
