@use '@theme/variables.scss' as *;
@use '@theme/functions.scss' as *;
@use './InfoLabel.sd.module' as *;

.container {
  background: $background-color;
  display: flex;
  align-items: center;
  gap: calculate-rem(8px);
  padding: calculate-rem(4px) calculate-rem(24px) calculate-rem(4px) calculate-rem(4px);
  border-radius: calculate-rem(8px);
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $color-primary;
  border-radius: calculate-rem(8px);
  padding: calculate-rem(4px);
}

.contentWrapper {
  font-size: calculate-rem(14px);
  font-weight: 800;
  line-height: normal;
  letter-spacing: 0.14px;
}