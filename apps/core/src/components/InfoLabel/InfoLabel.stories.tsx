import { InfoLabel } from '@components/InfoLabel/InfoLabel'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof InfoLabel> = {
  title: 'InfoLabel',
  component: InfoLabel,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'InfoLabel component displays an icon with text content. Used for showing informational content ' +
          'with visual indicators like dates, statuses, or categories.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    iconUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/calendar-empty-alt.svg',
    text: 'February 17th 2025 - February 24th 2025',
    customClassName: '',
  },
  argTypes: {
    iconUrl: {
      control: 'text',
      description: 'URL of the background/icon image to display',
    },
    text: {
      control: 'text',
      description: 'Text content to display alongside the icon',
    },
    customClassName: {
      control: 'text',
      description: 'Optional custom CSS class for additional styling',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: 'fit-content',
          padding: '20px',
          borderRadius: '8px',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  // Disable all args for variants story - it showcases all InfoLabel variants
  argTypes: {
    iconUrl: { table: { disable: true } },
    text: { table: { disable: true } },
    customClassName: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All InfoLabel variants showing different icons and text content',
      },
    },
  },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      {/* Icon Variants */}
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Different Icons</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Calendar Icon</h4>
            <InfoLabel
              iconUrl="https://luckyspins-staging4.imgix.net/icons/sweeps/calendar-empty-alt.svg"
              text="February 17th 2025 - February 24th 2025"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Clock Icon</h4>
            <InfoLabel iconUrl="https://luckyspins-staging4.imgix.net/icons/clock.svg" text="Limited Time Offer" />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Location Icon</h4>
            <InfoLabel iconUrl="https://luckyspins-staging4.imgix.net/icons/location.svg" text="Available Worldwide" />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Star Icon</h4>
            <InfoLabel iconUrl="https://luckyspins-staging4.imgix.net/icons/star.svg" text="Premium Feature" />
          </div>
        </div>
      </div>

      {/* Text Content Variants */}
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Text Content Variants</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Short Text</h4>
            <InfoLabel
              iconUrl="https://luckyspins-staging4.imgix.net/icons/sweeps/calendar-empty-alt.svg"
              text="Today"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Medium Text</h4>
            <InfoLabel
              iconUrl="https://luckyspins-staging4.imgix.net/icons/sweeps/calendar-empty-alt.svg"
              text="March 15th - March 22nd"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Long Text</h4>
            <InfoLabel
              iconUrl="https://luckyspins-staging4.imgix.net/icons/sweeps/calendar-empty-alt.svg"
              text="Extended promotion period from January 1st 2025 to December 31st 2025"
            />
          </div>
        </div>
      </div>
    </div>
  ),
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive InfoLabel with all properties available for customization',
      },
    },
  },
  args: {
    iconUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/calendar-empty-alt.svg',
    text: 'Customize this text and icon!',
    customClassName: '',
  },
  render: args => (
    <div
      style={{
        borderRadius: '12px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <InfoLabel {...args} />
    </div>
  ),
}
