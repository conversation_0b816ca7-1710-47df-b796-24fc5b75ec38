/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$size-sm-radius: $radius-sm;
$size-sm-padding: $size-xs;
$size-sm-font: $typography-body-baseline-xs;
$size-md-radius: $radius-sm;
$size-md-padding: $font-size-sm;
$size-md-font: $typography-body-baseline-sm;
$default-background: $color-surface-200;
$default-icon: $color-surface-800;
$default-text: $color-surface-900;
$active-background: $color-tertiary;
$active-icon: $color-on-tertiary;
$active-text: $color-on-tertiary;