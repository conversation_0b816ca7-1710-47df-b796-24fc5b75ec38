'use client'
import React from 'react'
import ThemedImage from '@components/ThemedImage/ThemedImage'

interface ILogoProps {
  className?: string
  width?: number
  height?: number
  fill?: boolean
}

export const Logo: React.FC<ILogoProps> = ({ className, width = 140, height = 20, fill }) => {
  return (
    <ThemedImage
      lightSrc="/assets/svg/logo_light.svg"
      darkSrc="/assets/svg/logo_dark.svg"
      alt="logo"
      width={!fill ? width : undefined}
      height={!fill ? height : undefined}
      className={className}
      fill={fill}
    />
  )
}
