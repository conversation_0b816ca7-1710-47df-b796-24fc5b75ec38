@use './404Screen.sd.module' as *;
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(24px);

  @media (max-width: $breakpoint-mobile) {
    min-height: calc(100vh - calculate-rem(80px));
    padding: calculate-rem(16px);
  }
}

.content {
  max-width: calculate-rem(640px);
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calculate-rem(32px);

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(24px);
  }
}

.logo {
  height: calculate-rem(80px);
}

.illustrationContainer h1 {
  font-size: calculate-rem(220px);
}

.textContent {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
  max-width: calculate-rem(480px);
}

.title {
  font: $title-font;
  color: $title-color;
  margin: 0;

  @media (max-width: $breakpoint-mobile) {
    font: $title-mobile-font;
  }
}

.description {
  font: $description-font;
  color: $description-color;
  line-height: $description-line-height;
  margin: 0;

  @media (max-width: $breakpoint-mobile) {
    font: $description-mobile-font;
  }
}

.actions {
  display: flex;
  gap: calculate-rem(16px);
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: $breakpoint-mobile) {
    flex-direction: column;
    width: 100%;
    gap: calculate-rem(12px);
  }
}

.quickLinks {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
  align-items: center;
  padding-top: calculate-rem(24px);
  border-top: $border-separator;

  @media (max-width: $breakpoint-mobile) {
    padding-top: calculate-rem(16px);
    gap: calculate-rem(12px);
  }
}

.quickLinksTitle {
  font: $quick-links-title-font;
  color: $quick-links-title-color;
  margin: 0;
}

.linksList {
  display: flex;
  gap: calculate-rem(24px);
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(16px);
    flex-direction: column;
    align-items: center;
  }
}

.quickLink {
  font: $quick-link-font;
  color: $quick-link-color;
  text-decoration: none;
  padding: calculate-rem(8px) calculate-rem(12px);
  border-radius: $quick-link-radius;
  background: $quick-link-background;
  border: $quick-link-border;
  transition: all 0.2s ease-in-out;

  &:hover {
    color: $quick-link-hover-color;
    background: $quick-link-hover-background;
    transform: translateY(calculate-rem(-1px));
  }

  &:active {
    transform: translateY(0);
  }
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(calculate-rem(-8px));
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}
