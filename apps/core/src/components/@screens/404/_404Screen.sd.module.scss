@use '@theme/style-dictionary' as *;

$illustration-color: $color-surface-600;
$illustration-opacity: 0.8;

$title-font: $typography-headline-xl;
$title-mobile-font: $typography-headline-xl;
$title-color: $color-surface-900;

$description-font: $typography-body-baseline-md;
$description-mobile-font: $typography-body-baseline-md;
$description-color: $color-surface-700;
$description-line-height: 1.6;

$quick-links-title-font: $typography-label-md;
$quick-links-title-color: $color-surface-800;

$quick-link-font: $typography-label-sm;
$quick-link-color: $color-surface-700;
$quick-link-hover-color: $color-primary;
$quick-link-background: $color-surface-100;
$quick-link-hover-background: $color-surface-200;
$quick-link-radius: $radius-sm;
$quick-link-border: $border-width-default solid $color-surface-300;

$border-separator: $border-width-default solid $color-surface-200;
