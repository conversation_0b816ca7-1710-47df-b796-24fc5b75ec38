'use client'
import type { FC } from 'react'
import React, { useMemo } from 'react'
import { clsx } from 'clsx'
import { AnimatePresence } from 'motion/react'
import { useGetPromotionsPaginatedInfiniteQuery } from '@/store/rhinoLayer/rhinoLayer.api'
import type { RouteParams } from '@/types/nextjs'
import { Button } from '@components/Button'
import { PromoCard } from '@components/PromoCard'
import { Spinner } from '@components/Spinner'
import { PageContainer } from '@modules/page/PageContainer/PageContainer'
import { PageTitle } from '@modules/page/PageTitle/PageTitle'
import type { IGetPromotionsProps, IRlPromotionsResponse } from '@repo/types/api/rl/promotions'
import styles from '@screens/promotions/PromotionsScreen.module.scss'

interface IPromotionsScreenProps extends RouteParams {
  isLoggedIn?: boolean
  initialData?: IRlPromotionsResponse
  requestParams: IGetPromotionsProps
}

export const PromotionsScreen: FC<IPromotionsScreenProps> = ({ requestParams, initialData, locale }) => {
  const { page, ...infiniteQueryParams } = requestParams
  const { data, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage } =
    useGetPromotionsPaginatedInfiniteQuery(infiniteQueryParams)

  const allPromotions = useMemo(
    () => data?.pages.flatMap(page => page.payload || []) || initialData?.payload || [],
    [data?.pages, initialData],
  )

  const handleLoadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }

  return (
    <>
      <PageContainer>
        <PageTitle title={'Promotions - ' + locale} />
        <AnimatePresence initial={false}>{!!isLoading && <Spinner absolute />}</AnimatePresence>
        <div className="@container">
          <div
            className={clsx(
              'grid gap-3 grid-cols-1 @min-[620px]:grid-cols-2 @min-[1220px]:grid-cols-3 @min-[1720px]:grid-cols-4',
            )}>
            {allPromotions.map((item, i) => (
              <React.Fragment key={i}>
                <PromoCard promotion={item} />
              </React.Fragment>
            ))}
          </div>
        </div>
        {hasNextPage && !isLoading ? (
          <div className={styles.loadMoreContainer}>
            <Button label="Load more" onClick={handleLoadMore} isLoading={isFetchingNextPage} />
          </div>
        ) : null}
      </PageContainer>
    </>
  )
}
