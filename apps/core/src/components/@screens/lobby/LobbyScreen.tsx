import React from 'react'
import { getCasinoPageLayoutConfig, getLiveCasinoPageLayoutConfig } from '@/network/server-utils/s3/getters'
import type { RouteParams } from '@/types/nextjs'
import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import { LobbyType } from '@repo/constants/common/lobby'
import styles from '@screens/lobby/Lobby.module.scss'

type LobbyScreenProps = RouteParams & {
  type: LobbyType
}

export const LobbyScreen = async ({ locale, type }: LobbyScreenProps) => {
  let lobbyPageConfig
  if (type === LobbyType.Casino) {
    lobbyPageConfig = await getCasinoPageLayoutConfig()
  } else if (type === LobbyType.LiveCasino) {
    lobbyPageConfig = await getLiveCasinoPageLayoutConfig()
  } else {
    throw new Error(`Unsupported lobby type: ${type}`)
  }

  if (!lobbyPageConfig) {
    return (
      <div className={styles.container}>
        <div>Failed to load lobby page configuration</div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <DynamicContentRenderer config={lobbyPageConfig} locale={locale} />
    </div>
  )
}
