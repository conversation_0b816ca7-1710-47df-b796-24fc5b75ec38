/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$default-background: $color-surface-100;
$default-text: $color-surface-700;
$default-border: $border-width-default solid $color-surface-200;
$default-supporting-text: $color-surface-600;
$hover-border: $color-surface-300;
$active-text: $color-surface-1000;
$active-border: $color-surface-300;
$disabled-background: $color-surface-200;
$error-border: $color-error;
$error-supporting-text: $color-error-container;
$size-default-padding: $size-sm $size-md;
$size-default-gap: $size-xs;
$size-default-height: $size-3xl;
$size-default-font: $typography-body-baseline-md;
$size-compact-padding: $size-sm;
$size-compact-gap: $size-xs;
$size-compact-height: $size-3xl-3;
$size-compact-font: $typography-body-baseline-sm;