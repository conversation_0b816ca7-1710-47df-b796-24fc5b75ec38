const getLoaderSizeFromBtnSize = (size: string) => {
  switch (size) {
    case 'md':
      return 5
    case 'sm':
      return 4
    case 'lg':
      return 6
    default:
      return 5
  }
}

interface ILoadingIndicatorProps {
  size?: 'sm' | 'md' | 'lg'
  // consider adding a color prop if we need to customize the loader color
}

const LoadingIndicator = ({ size = 'md' }: ILoadingIndicatorProps) => {
  const _size = getLoaderSizeFromBtnSize(size)
  return (
    <svg
      className={`animate-spin h-${_size} w-${_size}`}
      fill="none"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg">
      <circle className="opacity-25 stroke-[var(--color-surface-700)]" cx="12" cy="12" r="10" strokeWidth="4" />
      <path
        className="opacity-75 fill-[var(--color-surface-700)]"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291
        A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )
}

export default LoadingIndicator
