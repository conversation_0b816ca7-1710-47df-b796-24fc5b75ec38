/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$state-unchecked-default-background: $color-surface-100;
$state-unchecked-default-border: $border-width-default solid $color-surface-400;
$state-unchecked-hover-border: $color-surface-500;
$state-unchecked-active-border: $color-surface-500;
$state-unchecked-disabled-background: $color-surface-200;
$state-unchecked-disabled-border: $color-surface-200;
$state-checked-default-background: $color-tertiary;
$state-checked-default-foreground: $color-on-tertiary;
$state-checked-default-border: $color-transparent;
$size-default-radius: $size-xs;