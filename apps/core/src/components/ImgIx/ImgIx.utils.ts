export const getImgIxBlurUrl = (imgIxUrl: string) => {
  if (typeof imgIxUrl !== 'string' || !imgIxUrl.includes('.imgix')) {
    return
  }
  try {
    const url = new URL(imgIxUrl)
    url.searchParams.delete('auto')
    url.searchParams.set('w', '15')
    url.searchParams.set('blur', '20')
    url.searchParams.set('q', '50')

    return url.toString()
  } catch (error) {
    console.error('Error creating blur URL:', error)
    return undefined
  }
}

export const detectDevicePixelRatio = () => {
  if (typeof window !== 'undefined' && window.devicePixelRatio) {
    return window.devicePixelRatio.toString()
  }
  return '2'
}
