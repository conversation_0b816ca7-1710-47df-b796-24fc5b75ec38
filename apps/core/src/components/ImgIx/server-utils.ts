import 'server-only'
import qs from 'query-string'
import { getImgIxConfig } from '@/network/server-utils/s3/getters'
import { withCache } from '@/services/Cache.service'
import { getImgIxBlurUrl } from '@components/ImgIx/ImgIx.utils'
import type { Keyable } from '@repo/types/common'

interface IResolveImgIxUrlParams {
  path: string
  middlewareImage: string | null
  middlewareImageHost: string | undefined
  imgIxParams: Keyable | undefined
}

// files types provided as it is by ImgIx
const extensionsWithNoParams = ['svg', 'gif']

export function decorateImgIxUrlWithParams(path: string, params: Keyable = {}) {
  if (extensionsWithNoParams.some(ext => path?.includes(`.${ext}`))) {
    return path
  }

  let imgIxParams
  if (params) {
    imgIxParams = {
      fit: 'crop',
      auto: 'format',
      w: !params.h ? 1081 : undefined,
      ...params,
    }
  }

  return qs.stringifyUrl({
    url: path,
    query: imgIxParams,
  })
}

const resolveImgIxUrl = ({ path, middlewareImage, middlewareImageHost, imgIxParams }: IResolveImgIxUrlParams) => {
  if (middlewareImage !== null) {
    return middlewareImage
  }
  if (path.startsWith('http')) {
    return path
  }
  return decorateImgIxUrlWithParams(`${middlewareImageHost}${path}`, imgIxParams)
}

async function getMiddlewareImgIx(url: string, params?: Keyable) {
  if (!url) {
    return null
  }
  const imgIxConfig = await getImgIxConfig()
  const decodedPath = decodeURIComponent(url)

  const matchingImageConfig = imgIxConfig?.find(iic => decodedPath.startsWith(`${iic.imagesOrigin}`))
  if (!matchingImageConfig) {
    return null
  }
  const imgIxUrl = decodedPath.replace(matchingImageConfig.imagesOrigin, matchingImageConfig.imgIxHost)
  return decorateImgIxUrlWithParams(imgIxUrl, params)
}

export const getImgIx = async (path: string, params?: Keyable) => {
  return withCache(
    `imgix:${path}:${JSON.stringify(params || {})}`,
    async () => {
      if (!path) {
        return undefined
      }

      const isGif = path.endsWith('.gif')

      if (isGif) {
        return path // imgix returns only 1st frame for gif (static image)
      }

      const imgIxParams = {
        fit: params?.w && params?.h ? 'crop' : 'ar',
        auto: 'format',
        ...params,
      }

      return await getMiddlewareImgIx(path, imgIxParams)
    },
    180,
  )
}

export const getImgIxBlurBase64 = async (imgIxUrl: string) => {
  return withCache(
    `imgix-blur-base64:${imgIxUrl}`,
    async () => {
      const blurUrl = getImgIxBlurUrl(imgIxUrl)
      if (!blurUrl) {
        return undefined
      }

      try {
        const response = await fetch(blurUrl, { next: { revalidate: 0 } })
        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer()
          const buffer = Buffer.from(arrayBuffer)
          const contentType = response.headers.get('content-type') || 'image/jpeg'
          return `data:${contentType};base64,${buffer.toString('base64')}`
        } else {
          console.log('Failed to fetch blur image:', response.status, response.statusText)
          return undefined
        }
      } catch (error) {
        console.error('Error fetching blur image:', error)
        return undefined
      }
    },
    180,
  )
}

export const getImgIxBlurBlurhash = async (imgIxUrl: string) => {
  return withCache(
    `imgix-blur-blurhash:${imgIxUrl}`,
    async () => {
      if (typeof imgIxUrl !== 'string' || !imgIxUrl.includes('imgix')) {
        return
      }
      try {
        const url = new URL(imgIxUrl)
        url.searchParams.delete('auto')
        url.searchParams.set('w', '32')
        url.searchParams.set('fm', 'blurhash')

        const response = await fetch(url.toString(), { next: { revalidate: 0 } })
        if (response.ok) {
          return response.text()
        } else {
          //  console.log('Failed to fetch blurhash image:', response.status, response.statusText)
          return undefined
        }
      } catch (error) {
        // console.error('Error fetching blurhash image:', error)
        return undefined
      }
    },
    180,
  )
}
