'use client'

import React, { useRef, useState, useEffect } from 'react'
import { debounce } from 'lodash-es'
import { useIsClient } from 'usehooks-ts'
import { Image, type ImageProps } from '@heroui/image'
import { cn } from '@repo/ui/utils/class'

interface IImgIxClientProps extends Omit<ImageProps, 'src'> {
  src: string
  autoresize?: boolean
}

const DEBOUNCE_DELAY = 1000 //ms

/**
 * @description This component is used to render images using ImgIx service on the client side.
 * with autosizing functionality.
 */
const ImgIxWithResizeClient: React.FC<IImgIxClientProps> = ({ src, autoresize = true, width, height, ...props }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const isClient = useIsClient()
  const [dimensions, setDimensions] = useState<{ width?: number; height?: number }>({
    width: width as number | undefined,
    height: height as number | undefined,
  })

  const prevDimensionsRef = useRef<{ width?: number; height?: number }>({
    width: dimensions.width,
    height: dimensions.height,
  })

  const areDimensionsDetected =
    prevDimensionsRef.current.width !== undefined && prevDimensionsRef.current.height !== undefined

  useEffect(() => {
    if (!autoresize || (width && height)) return

    const updateDimensions = () => {
      if (containerRef.current) {
        const parentWidth = containerRef.current.parentElement?.offsetWidth
        const parentHeight = containerRef.current.parentElement?.offsetHeight

        let processedWidth = parentWidth
        if (width !== undefined) {
          processedWidth = typeof width === 'number' ? width : parseInt(width as string, 10)
        }

        let processedHeight = parentHeight
        if (height !== undefined) {
          processedHeight = typeof height === 'number' ? height : parseInt(height as string, 10)
        }

        const prevWidth = prevDimensionsRef.current.width
        const prevHeight = prevDimensionsRef.current.height

        // Only update if at least one dimension increases
        if (
          prevWidth !== undefined &&
          prevHeight !== undefined &&
          processedWidth !== undefined &&
          processedHeight !== undefined
        ) {
          const widthIncreased = processedWidth > prevWidth
          const heightIncreased = processedHeight > prevHeight

          if (!widthIncreased && !heightIncreased) {
            return
          }
        }

        prevDimensionsRef.current = {
          width: processedWidth,
          height: processedHeight,
        }

        setDimensions({
          width: processedWidth,
          height: processedHeight,
        })
      }
    }

    const throttledUpdateDimensions = debounce(updateDimensions, DEBOUNCE_DELAY, { leading: false, trailing: true })

    updateDimensions() // Initial update

    const resizeObserver = new ResizeObserver(() => {
      throttledUpdateDimensions()
    })

    if (containerRef.current?.parentElement) {
      resizeObserver.observe(containerRef.current.parentElement)
    }

    return () => {
      throttledUpdateDimensions.cancel()
      resizeObserver.disconnect()
    }
  }, [autoresize, width, height])

  if (!src) {
    return null
  }

  // Parse the URL to check for existing parameters
  const parseUrl = (url: string) => {
    const [baseUrl, queryString] = url.split('?')
    const params = new URLSearchParams(queryString || '')
    return { baseUrl, params }
  }

  // Extract existing URL parts
  const { baseUrl, params: queryParams } = parseUrl(src)

  if (typeof dimensions.width === 'number') {
    queryParams.set('w', dimensions.width.toString())
  }

  if (typeof dimensions.height === 'number') {
    queryParams.set('h', dimensions.height.toString())
  }

  queryParams.set('dpr', `${isClient ? window.devicePixelRatio || 1 : 1}`)

  const paramString = queryParams.toString()
  const srcWithDimensions = paramString ? `${baseUrl}?${paramString}` : baseUrl

  return (
    <div ref={containerRef} style={{ display: 'block', width: '100%', height: '100%' }}>
      <Image
        src={srcWithDimensions}
        disableSkeleton={!!props.fallbackSrc}
        loading="lazy"
        {...props}
        classNames={{
          wrapper: cn('flex w-full h-full !max-w-full bg-cover bg-no-repeat bg-center rounded-none filter', {
            // 'blur-lg': !areDimensionsDetected && autoresize,
          }),
          img: cn('w-full object-cover z-0 rounded-none '),
        }}
      />
    </div>
  )
}

export default ImgIxWithResizeClient
