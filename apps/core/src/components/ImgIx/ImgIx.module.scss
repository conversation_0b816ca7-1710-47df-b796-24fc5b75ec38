.container {
  position: relative;
  height: 100%;
  z-index: 0;

  img {
    width: 100%;
    height: 100%;
  }
}

.blurhashContainer {
  position: absolute;
  inset: 0;
  z-index: 0;
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}

.blurUrlContainer {
  position: absolute;
  inset: 0;
  transition: opacity 0.2s ease-in-out;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.blurhash {
  width: 100%;
  height: 100%;
}

.image {
  position: relative;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.visible {
  opacity: 1;
}
