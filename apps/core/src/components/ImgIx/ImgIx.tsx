import React from 'react'
import type { ImageProps } from 'next/image'
import ImgIxClient from '@components/ImgIx/ImgIx.client'
import { detectDevicePixelRatio, getImgIxBlurUrl } from '@components/ImgIx/ImgIx.utils'
import {
  decorateImgIxUrlWithParams,
  getImgIx,
  getImgIxBlurBase64,
  getImgIxBlurBlurhash,
} from '@components/ImgIx/server-utils'

type IImgIxServerProps = Omit<ImageProps, 'placeholder'> & {
  placeholder?: 'blurhash' | 'base64' | 'url'
  fallbackText?: string
  containerClassName?: string
}

/**
 * @description This component is used to render images using ImgIx service on the server side.
 * The ImgIx is used only to get an original asset URL,
 * the resize functionality is handled by the nextjs Image component
 */
const ImgIx = async ({
  src: baseSrc,
  placeholder = 'url', // base64 renders immediately, but increases page size,
  // blurhash renders after the client hydrated, but provides smaller page size
  fallbackText,
  height = 0,
  width,
  ...props
}: IImgIxServerProps) => {
  let src = baseSrc
  let blurhash: string | undefined = undefined
  let blurBase64: string | undefined = undefined
  let blurUrl: string | undefined = undefined

  try {
    if (typeof baseSrc === 'string') {
      try {
        let imgIxSrc: string | null | undefined = undefined
        const imgIxParams: Record<string, string> = {
          w: width ? String(width) : '1920',
          dpr: detectDevicePixelRatio(),
        }
        if (baseSrc.startsWith('http') && baseSrc.includes('.imgix')) {
          imgIxSrc = decorateImgIxUrlWithParams(baseSrc, imgIxParams)
        } else {
          imgIxSrc = await getImgIx(baseSrc, imgIxParams)
        }

        if (imgIxSrc) {
          src = imgIxSrc
        }
      } catch (error) {
        console.error('Error while getting ImgIx URL:', error)
        src = baseSrc
      }
    }

    if (placeholder === 'base64') {
      blurBase64 = await getImgIxBlurBase64(src as string)
    } else if (placeholder === 'blurhash') {
      blurhash = await getImgIxBlurBlurhash(src as string)
    } else if (placeholder === 'url') {
      blurUrl = getImgIxBlurUrl(src as string)
    }
  } catch (error) {
    console.error('Unexpected error in ImgIx:', error)
    src = baseSrc
  }

  return (
    <ImgIxClient
      src={src}
      blurDataURL={blurBase64}
      placeholder={blurBase64 ? 'blur' : 'empty'}
      blurhash={blurhash}
      blurUrl={blurUrl}
      quality={95}
      width={width}
      height={height}
      fallbackText={fallbackText}
      {...props}
    />
  )
}

export default ImgIx
