import React from 'react'
import ImgIxWithResizeClient from '@components/ImgIx/ImgIxWithResize.client'
import { getImgIx } from '@components/ImgIx/server-utils'
import type { ImageProps } from '@heroui/image'

interface IImgIxProps extends ImageProps {
  thumbnailCoeff?: number
  thumbnailBlur?: number
}

/**
 * @description This component is used to render images using ImgIx service on the client side.
 * with autosizing functionality.
 */
const ImgIxWithResize = async ({ src: baseSrc, thumbnailCoeff = 0.08, thumbnailBlur = 20, ...props }: IImgIxProps) => {
  const src =
    typeof baseSrc === 'string'
      ? await getImgIx(baseSrc, {
          w: props.width || (props.height ? undefined : 30),
          h: props.height || (props.width ? undefined : 30),
        })
      : '#' // TODO provide a fallback url

  if (!src) {
    console.warn('ImgIxWithResize: src is not provided or invalid', { src, baseSrc })
    return null
  }

  const blurUrl =
    typeof baseSrc === 'string'
      ? await getImgIx(baseSrc, {
          w: (Number(props.width) || (props.height ? 0 : 300)) * thumbnailCoeff,
          h: (Number(props.height) || (props.width ? 0 : 300)) * thumbnailCoeff,
          blur: thumbnailBlur,
        })
      : undefined

  return <ImgIxWithResizeClient src={src} fallbackSrc={blurUrl} loading="lazy" {...props} />
}

export default ImgIxWithResize
