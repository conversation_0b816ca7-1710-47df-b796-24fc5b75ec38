import { z } from 'zod'

const clientEnvSchema = z.object({
  NEXT_PUBLIC_APP_NAME: z.string(),
  NEXT_PUBLIC_S3_URL: z.string().url(),
  NEXT_PUBLIC_TEST_USER_EMAIL: z.string().optional(),
  NEXT_PUBLIC_TEST_USER_PASSWORD: z.string().optional(),
  NEXT_PUBLIC_DEFAULT_LICENSE: z.string(),
  NEXT_PUBLIC_DEFAULT_LOCALE: z.string(),
  NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH: z
    .string()
    .optional()
    .transform(val => val === 'true' || val === '1'),
})

const serverEnvSchema = z
  .object({
    SUPPORTED_LOCALES: z.string().transform(val => val.split(',')),
    // VERBOSE_LOGGING: z
    //   .string()
    //   .optional()
    //   .transform(val => val === 'true' || val === '1'),
    AUTH_SECRET: z.string(),
    RL_API_KEY: z.string(),
    RL_API_URL: z.string(),
  })
  .merge(clientEnvSchema)

const vars = {
  NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
  NEXT_PUBLIC_TEST_USER_EMAIL: process.env.NEXT_PUBLIC_TEST_USER_EMAIL,
  NEXT_PUBLIC_TEST_USER_PASSWORD: process.env.NEXT_PUBLIC_TEST_USER_PASSWORD,
  NEXT_PUBLIC_DEFAULT_LICENSE: process.env.NEXT_PUBLIC_DEFAULT_LICENSE,
  NEXT_PUBLIC_DEFAULT_LOCALE: process.env.NEXT_PUBLIC_DEFAULT_LOCALE,
  NEXT_PUBLIC_S3_URL: process.env.NEXT_PUBLIC_S3_URL,
  NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH: process.env.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH,
  SUPPORTED_LOCALES: process.env.SUPPORTED_LOCALES,
  RL_API_URL: process.env.RL_API_URL,
  AUTH_SECRET: process.env.AUTH_SECRET,
  RL_API_KEY: process.env.RL_API_KEY,
}
// Parse and validate the environment variables
export const envVars = (typeof window === 'undefined' ? serverEnvSchema : clientEnvSchema).parse(vars) as z.infer<
  typeof serverEnvSchema
>
