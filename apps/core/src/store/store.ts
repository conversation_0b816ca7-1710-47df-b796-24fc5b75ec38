import { rhinolayerApi } from '@/store/rhinoLayer/rhinoLayer.api'
import { rootReducer } from '@/store/rootReducer'
import { configureStore } from '@reduxjs/toolkit'

const middlewares = [rhinolayerApi.middleware]

export const makeStore = () => {
  return configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(middlewares),
  })
}

export type AppStore = ReturnType<typeof makeStore>
export type RootState = ReturnType<AppStore['getState']>
export type AppDispatch = AppStore['dispatch']
