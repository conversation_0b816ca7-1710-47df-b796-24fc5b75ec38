'use client'
import { useRef } from 'react'
import { Provider } from 'react-redux'
import type { AppStore } from '@/store/store'
import { makeStore } from '@/store/store'

export default function ReduxStoreProvider({ children }: { children: React.ReactNode }) {
  const storeRef = useRef<AppStore | null>(null)
  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = makeStore()
    // Loading Initial Data
    // storeRef.current.dispatch(setSession(session))
  }

  return <Provider store={storeRef.current}>{children}</Provider>
}
