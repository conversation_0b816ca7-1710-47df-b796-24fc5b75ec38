import type { TypedUseSelectorHook } from 'react-redux'
import { useStore, useDispatch, useSelector } from 'react-redux'
import type { AppDispatch, RootState } from '@/store/store'
import type { Store } from '@reduxjs/toolkit'

export const useAppDispatch: () => AppDispatch = useDispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export const useAppStore: () => Store<RootState> = useStore
