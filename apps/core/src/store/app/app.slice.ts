import type { PayloadAction } from '@reduxjs/toolkit'
import { createSlice } from '@reduxjs/toolkit'

export interface IAppState {
  appStatus: any
}

const initialState: IAppState = {
  appStatus: 'LOADING',
}

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setAppStatus: (state, { payload }: PayloadAction<IAppState['appStatus']>) => {
      state.appStatus = payload
    },
  },
})

export const { setAppStatus } = appSlice.actions
export const appReducer = appSlice.reducer
