import 'server-only'
import { headers } from 'next/headers'
import type { NextRequest } from 'next/server'
import type { Session } from 'next-auth'
import { auth } from '@/auth'
import { serverConfig } from '@/config/serverConfig'
import { supportedLocaleRegex } from '@/middlewares/utils'
import { Locale } from '@constants/locale'

export async function getClientIp(req?: any) {
  const forwarded = (await headers()).get('x-forwarded-for')
  return forwarded ? forwarded.split(',')[0] : req?.connection?.remoteAddress
}

export async function detectLocaleByIp() {
  // TODO to be implemented based on the ipdata or CF header
  const ip = await getClientIp()
  return serverConfig.defaultLocale
}

export async function detectCountryByIp() {
  // TODO to be implemented based on the ipdata or CF header
  const ip = await getClientIp()
  return 'MK'
}

function detectLocaleByUserInfo(sessionInfo: Session): Locale {
  // TODO to be implemented based on the user country map or whatever
  const userInfo = sessionInfo.user
  const MAP = {
    SK: 'mk',
    MT: 'mt',
  }
  return Locale.EN // a geo data missed currently
  // return MAP[userInfo?.geo?.countryCode as 'SK'] || 'en'
}

export function extractLocaleFromUrl(url: string): string | null {
  const match = url.match(supportedLocaleRegex)
  return match && match[1] ? match[1] : null
}

export async function detectLocaleByRequest(req?: NextRequest) {
  const sessionInfo = await auth()
  const pathname = req?.nextUrl?.pathname || ''

  const isAuthenticated = !!sessionInfo?.accessToken && !!sessionInfo?.user
  if (isAuthenticated) {
    return detectLocaleByUserInfo(sessionInfo)
  }
  return extractLocaleFromUrl(pathname) || detectLocaleByIp()
}
