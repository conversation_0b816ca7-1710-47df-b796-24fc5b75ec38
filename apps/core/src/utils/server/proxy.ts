import type { NextRequest } from 'next/server'
import 'server-only'

const rateLimitStore = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100 // requests per window

export function validateRequestPath(path: string[]): boolean {
  // Prevent path traversal attacks
  const joinedPath = path.join('/')
  if (joinedPath.includes('..') || joinedPath.includes('//')) {
    return false
  }
  const pathRegex = /^[a-zA-Z0-9\-_/]+$/
  return pathRegex.test(joinedPath)
}

export function checkApiRateLimit(clientId: string): boolean {
  return true // rateLimitStore should be replaced with a real store like Redis as
  // using an in-memory Map for rate limiting will not work correctly in serverless environments
  //   const now = Date.now()
  //   const clientData = rateLimitStore.get(clientId)

  //   if (!clientData || now > clientData.resetTime) {
  //     rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
  //     return true
  //   }

  //   if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
  //     return false
  //   }

  //   clientData.count++
  //   return true
}

export function getRequestClientId(request: NextRequest): string {
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwardedFor ? forwardedFor.split(',')[0]?.trim() : realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'
  return `${ip}:${userAgent.slice(0, 50)}`
}
