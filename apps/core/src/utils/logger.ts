export class Logger {
  private prefix: string

  constructor(prefix: string = '[App]') {
    this.prefix = prefix
  }

  log(message: string, ...args: any[]): void {
    console.log(`${this.prefix} ${message}`, ...args)
  }

  error(message: string, ...args: any[]): void {
    console.error(`${this.prefix} ${message}`, ...args)
  }

  warn(message: string, ...args: any[]): void {
    console.warn(`${this.prefix} ${message}`, ...args)
  }

  info(message: string, ...args: any[]): void {
    console.info(`${this.prefix} ${message}`, ...args)
  }

  debug(message: string, ...args: any[]): void {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`${this.prefix} ${message}`, ...args)
    }
  }
}

export const logger = new Logger('[App]')

export const createLogger = (prefix: string): Logger => new Logger(prefix)
