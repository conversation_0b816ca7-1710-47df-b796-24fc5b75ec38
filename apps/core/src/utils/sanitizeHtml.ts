import DOMPurify from 'isomorphic-dompurify'

/**
 * Sanitizes HTML content to prevent XSS attacks
 * @param html - The HTML string to sanitize
 * @returns Sanitized HTML string
 */
export const sanitizeHtml = (html: string | undefined | null): string => {
  if (!html) return ''

  // Run the HTML through DOMPurify to sanitize it
  return DOMPurify.sanitize(html, {
    USE_PROFILES: { html: true },
    // Allow only these tags and attributes, adjust based on your needs
    ALLOWED_TAGS: [
      'b',
      'i',
      'em',
      'strong',
      'a',
      'p',
      'br',
      'span',
      'div',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'ul',
      'ol',
      'li',
    ],
    ALLOWED_ATTR: ['href', 'target', 'class', 'id', 'style'],
  })
}
