import { envVars } from '@/env'
import type { License, Locale, Market } from '@constants/locale'
import { LOCALES_MAP, MARKET_TO_LICENSE_MAP } from '@constants/locale'

export const getLicenseByMarket = (market: Market): License => {
  const license = MARKET_TO_LICENSE_MAP[market]
  if (!license) {
    console.warn(
      `No license found for market: ${market}. Falling back to default license: ${envVars.NEXT_PUBLIC_DEFAULT_LICENSE}`,
    )
    return envVars.NEXT_PUBLIC_DEFAULT_LICENSE as License
  }
  return license
}

export const getLicenseByLocale = (locale: Locale): License => {
  const market = getMarketByLocale(locale)
  const license = MARKET_TO_LICENSE_MAP[market]
  if (!license) {
    console.warn(
      `No license found for locale: ${locale}. Falling back to default license: ${envVars.NEXT_PUBLIC_DEFAULT_LICENSE}`,
    )
    return envVars.NEXT_PUBLIC_DEFAULT_LICENSE as License
  }
  return license
}

export const getMarketByLocale = (locale: Locale): Market => {
  const market = LOCALES_MAP[locale]?.market
  if (!market) {
    const defaultMarket = LOCALES_MAP[envVars.NEXT_PUBLIC_DEFAULT_LOCALE as Locale].market
    console.warn(`No market found for locale: ${locale}. Falling back to default market: ${defaultMarket}`)
    return defaultMarket
  }

  return market
}

export const getLanguageByLocale = (locale: Locale) => {
  const lang = LOCALES_MAP[locale]?.lang
  if (!lang) {
    const defaultLang = LOCALES_MAP[envVars.NEXT_PUBLIC_DEFAULT_LOCALE as Locale].lang
    console.warn(`No language found for locale: ${locale}. Falling back to default language: ${defaultLang}`)
    return defaultLang
  }

  return lang
}

export const getRLRequestParamsByLocale = (locale: Locale) => {
  const market = getMarketByLocale(locale).toUpperCase()
  const iso2 = getLanguageByLocale(locale).toLowerCase()

  return { market, iso2 }
}
