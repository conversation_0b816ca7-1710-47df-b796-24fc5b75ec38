'use server'

import { unstable_update } from '@/auth'
import { rlF<PERSON>ch<PERSON><PERSON> } from '@/network/server-utils/rl/RLFetchApi'
import type { IServerActionResult } from '@/types/serverActions'
import type { IRefreshTokenResponse } from '@repo/types/api/rl/auth'
import type { IRlResponseError } from '@repo/types/api/rl/index'

export const refreshTokenAction = async (refreshToken: string): Promise<IServerActionResult<IRefreshTokenResponse>> => {
  try {
    if (!refreshToken) {
      throw new Error('No refresh token found')
    }

    const response = await rlFetchApi.refreshToken(refreshToken)

    if (!response?.ok) {
      const errorData = (await response?.json()) as IRlResponseError
      console.error('Refresh token failed:', errorData)
      throw new Error('Failed to refresh token')
    }

    const data = (await response.json()).data as IRefreshTokenResponse
    if (!data || !data.accessToken) {
      throw new Error('No new accessToken received')
    }
    await unstable_update({ accessToken: data.accessToken, refreshToken: data.refreshToken })

    return {
      success: true,
      data,
    }
  } catch (error: any) {
    console.error('Error in refreshTokenAction:', error)

    return {
      success: false,
      error: error.message || 'An unknown error occurred',
    }
  }
}
