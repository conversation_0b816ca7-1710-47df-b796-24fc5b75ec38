class CryptoService {
  async encryptData(plainData: string, encryptionKey: string): Promise<string> {
    const initVector = crypto.getRandomValues(new Uint8Array(12))
    const encodedData = new TextEncoder().encode(plainData)

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      Buffer.from(encryptionKey, 'base64'),
      {
        name: 'AES-GCM',
        length: 256,
      },
      true,
      ['encrypt', 'decrypt'],
    )

    const encryptedData = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: initVector,
      },
      cryptoKey,
      encodedData,
    )

    const combinedData = new Uint8Array(initVector.length + encryptedData.byteLength)
    combinedData.set(initVector)
    combinedData.set(new Uint8Array(encryptedData), initVector.length)

    return Buffer.from(combinedData).toString('base64')
  }

  async decryptData(combinedData: string, encryptionKey: string): Promise<string | null> {
    const decodedData = Buffer.from(combinedData, 'base64')
    const initVector = decodedData.subarray(0, 12)
    const encryptedData = decodedData.subarray(12)

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      Buffer.from(encryptionKey, 'base64'),
      {
        name: 'AES-GCM',
        length: 256,
      },
      true,
      ['encrypt', 'decrypt'],
    )

    try {
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: initVector,
        },
        cryptoKey,
        encryptedData,
      )

      return new TextDecoder().decode(decryptedData)
    } catch (error) {
      throw new Error('Decryption failed: ' + error)
    }
  }
}

export const cryptoService = new CryptoService()
