import { getAuthStore } from '@modules/auth'

class RouterService {
  constructor(private locale?: string) {}

  get localePrefix(): string {
    return this.locale ? `/${this.locale}` : ''
  }

  get home(): string {
    const isAuthenticated = getAuthStore()?.getIsAuthenticated()
    return isAuthenticated ? this.dashboard : `${this.localePrefix || '/'}`
  }

  get login(): string {
    return `${this.localePrefix}/login`
  }

  get register(): string {
    return `${this.localePrefix}/register`
  }

  get dashboard(): string {
    return `${this.localePrefix}/casino`
  }

  get casino(): string {
    return `${this.localePrefix}/casino`
  }

  get liveCasino(): string {
    return `${this.localePrefix}/live-casino`
  }
}

export const getAppRouter = (locale?: string) => new RouterService(locale)
export const useAppRouter = (locale?: string) => new RouterService(locale)
