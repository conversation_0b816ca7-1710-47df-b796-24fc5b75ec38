import 'server-only'
import { createLogger } from '@/utils/logger'

interface ICacheEntry<T> {
  data: T
  timestamp: number
  revalidate?: number
}

class MemoryCache {
  private cache = new Map<string, ICacheEntry<any>>()
  private logger = createLogger('[Cache]')

  set<T>(key: string, data: T, staleTimeSeconds?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      revalidate: staleTimeSeconds ? staleTimeSeconds * 1000 : undefined,
    })
  }

  get<T>(key: string) {
    const entry = this.cache.get(key)

    if (!entry) {
      return null
    }

    return entry as ICacheEntry<T>
  }

  getWithStaleInfo<T>(key: string): { data: T | null; isStale: boolean; exists: boolean } {
    const entry = this.cache.get(key)

    if (!entry) {
      return { data: null, isStale: false, exists: false }
    }

    const now = Date.now()
    const isStale = entry.revalidate ? now - entry.timestamp > entry.revalidate : false

    return {
      data: entry.data as T,
      isStale: isStale,
      exists: true,
    }
  }

  has(key: string): boolean {
    return this.cache.has(key)
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  cleanup() {
    const now = Date.now()
    this.logger.log('Cleanup initiated')

    for (const [key, entry] of this.cache.entries()) {
      const isExpired = entry.revalidate && now - entry.timestamp > entry.revalidate

      if (isExpired || entry.timestamp === 0) {
        this.cache.delete(key)
        this.logger.log('Cleaned up entry:', key, entry.timestamp === 0 ? '(marked stale)' : '(expired)')
      }
    }
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    }
  }

  getKeys() {
    return Array.from(this.cache.keys())
  }

  getKeysStartsWith(prefix: string): string[] {
    const keys = Array.from(this.cache.keys())
    return keys.filter(key => key.startsWith(prefix))
  }

  getEntities() {
    return this.cache.entries()
  }
}

class CacheService {
  private memoryCache: MemoryCache
  private backgroundRefreshPromises = new Map<string, Promise<any>>()
  private logger = createLogger('[Cache]')
  public cacheKeySeparator = ':'

  constructor() {
    this.memoryCache = new MemoryCache()
  }

  buildCacheKey(parts: (string | number)[]): string {
    return parts
      .filter(part => part !== null && part !== undefined && part !== '')
      .map(part => String(part))
      .join(this.cacheKeySeparator)
  }

  async withCache<T>(
    key: string,
    getDataPromise: () => Promise<T>,
    staleTimeSeconds: number = 60, // Time after which data is considered stale (default: 1 minute)
  ): Promise<T> {
    const cacheResult = this.memoryCache.getWithStaleInfo<T>(key)

    // Case 1: Cache hit with fresh data
    if (cacheResult.exists && !cacheResult.isStale) {
      // this.logger.log(`FRESH HIT for key: ${key}`)
      return cacheResult.data!
    }

    // Case 2: Cache hit with stale data - return stale, refresh in background
    if (cacheResult.exists && cacheResult.isStale) {
      this.logger.log(`STALE HIT for key: ${key} - returning stale data and refreshing in background`)

      // Check if background refresh is already in progress
      if (!this.backgroundRefreshPromises.has(key)) {
        const refreshPromise = (async () => {
          try {
            this.logger.log(`Background refresh started for key: ${key}`)
            const freshData = await getDataPromise()
            this.memoryCache.set(key, freshData, staleTimeSeconds)
            this.logger.log(`Background refresh completed for key: ${key}`)
            return freshData
          } catch (error) {
            this.logger.error(`Background refresh failed for key: ${key}`, error)
            throw error
          } finally {
            this.backgroundRefreshPromises.delete(key)
          }
        })()

        this.backgroundRefreshPromises.set(key, refreshPromise)
      }

      return cacheResult.data!
    }

    // this.logger.log(`MISS for key: ${key} - fetching fresh data`)

    // Check if there's already a fetch in progress
    if (this.backgroundRefreshPromises.has(key)) {
      // this.logger.log(`Waiting for existing fetch for key: ${key}`)
      return await this.backgroundRefreshPromises.get(key)!
    }

    const fetchPromise = (async () => {
      try {
        const data = await getDataPromise()
        this.memoryCache.set(key, data, staleTimeSeconds)
        return data
      } finally {
        this.backgroundRefreshPromises.delete(key)
      }
    })()

    this.backgroundRefreshPromises.set(key, fetchPromise)
    return await fetchPromise
  }

  invalidate(key: string | string[]): boolean | boolean[] {
    if (Array.isArray(key)) {
      return key.map(k => this.invalidateKey(k))
    }

    return this.invalidateKey(key)
  }

  private invalidateKey(key: string): boolean {
    this.logger.log(`INVALIDATE for key: ${key} - marking as stale`)
    const entry = this.memoryCache.get(key)

    if (!entry) {
      return false
    }

    entry.timestamp = 0

    return true
  }

  clearAll(): void {
    this.logger.log('CLEAR ALL')
    this.memoryCache.clear()
    this.backgroundRefreshPromises.clear()
  }

  getStats() {
    return this.memoryCache.getStats()
  }

  getKeys() {
    return this.memoryCache.getKeys()
  }

  getKeysStartsWith(prefix: string): string[] {
    return this.memoryCache.getKeysStartsWith(prefix)
  }

  getDetailedStats(): {
    totalEntries: number
    freshEntries: number
    staleEntries: number
    entries: Array<{
      key: string
      age: number
      isStale: boolean
      isFresh: boolean
    }>
  } {
    const stats = this.memoryCache.getStats()
    const entries = []
    let freshCount = 0
    let staleCount = 0

    for (const key of stats.keys) {
      const result = this.memoryCache.getWithStaleInfo(key)
      if (result.exists) {
        const entry = {
          key,
          age: Date.now() - (this.memoryCache.get(key)?.timestamp || 0),
          isStale: result.isStale,
          isFresh: !result.isStale,
        }
        entries.push(entry)

        if (result.isStale) {
          staleCount++
        } else {
          freshCount++
        }
      }
    }

    return {
      totalEntries: stats.size,
      freshEntries: freshCount,
      staleEntries: staleCount,
      entries,
    }
  }

  cleanup(): void {
    this.memoryCache.cleanup()
  }
}

const cacheService = new CacheService()

export { cacheService }

/**
 * Utility function for caching data with stale-while-revalidate strategy
 */
export async function withCache<T>(
  key: string,
  getDataPromise: () => Promise<T>,
  staleTimeSeconds: number = 60,
): Promise<T> {
  return cacheService.withCache(key, getDataPromise, staleTimeSeconds)
}
