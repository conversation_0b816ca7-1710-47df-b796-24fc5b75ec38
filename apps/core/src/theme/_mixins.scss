@use '@theme/variables.scss' as *;

@mixin line-clamp($number-of-lines) {
  display: -webkit-box;
  -webkit-line-clamp: $number-of-lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin custom-scrollbar(
  $thumb-color: $color-surface-600,
  $thumb-hover-color: $color-surface-700,
  $thickness: 4px,
  $radius: 2px
) {
  &::-webkit-scrollbar {
    width: calculate-rem($thickness);
    height: calculate-rem($thickness);
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calculate-rem($radius);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $thumb-hover-color;
  }
}
