# CSS Units Usage Guide for Rhino Next Project

## General Principles

When developing a user interface, it's important to choose the right units of measurement to ensure adaptability, accessibility, and design consistency. This guide will help determine when to use `rem` and when to use `px` in the Rhino Next project.

## When to Use REM

### 1. Font Sizes

```scss
.title {
  font-size: 2rem; // good: scales with user settings
  font-size: calculate-rem(16px); // good: same as above
}

.description {
  font-size: 16px; // bad: doesn't scale
}
```

### 2. Padding and Margin

```scss
.card {
  padding: calculate-rem(16px); // good: scales proportionally
  margin-bottom: calculate-rem(16px); // good: scales proportionally
}

.card {
  padding: 24px; // bad: doesn't scale
}
```

### 3. Width and Height of Containers

```scss
.container {
  max-width: calculate-rem(300px); // good: adapts to font settings
  height: calculate-rem(200px); // good: scales proportionally
}
```

### 4. Element Positioning

```scss
.tooltip {
  top: calculate-rem(16px); // good: scales with content
  left: calculate-rem(16px); // good: scales with content
}
```

## When to Use PX

### 1. Borders

```scss
.card {
  border: 1px solid grey; // good: crisp lines
  border-top: 2px solid black; // good: visible border
}

.card {
  border: 0.0625rem solid grey; // bad: may become blurry when scaled
}
```

### 2. Box-shadows

```scss
.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); // good: crisp shadows
}

.card {
  box-shadow: 0 calculate-rem(6px) calculate-rem(6px) rgba(0, 0, 0, 0.2); // usually not necessary
}
```

### 3. Small Graphical Details

```scss
.divider {
  height: 1px; // good: thin line
}

.pixel-perfect {
  transform: translateY(1px); // good: precise positioning
}
```

## Special Cases

### 1. Media Queries

It's better to use `em` for media queries:

```scss
@media (min-width: 40em) {
  // good: relative to current browser settings
  .container {
    display: flex;
  }
}
```

### 2. Line-height

For line spacing, it's better to use unitless values:

```scss
.text {
  line-height: 1.5; // good: proportional to font size
  line-height: 24px; // bad: doesn't scale with font size
}
```

### 3. Hardware Pixels and High Resolution (Retina)

For very thin lines (1px) on high DPI screens, you can use special approaches:

```scss
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hairline {
    border-width: 0.5px; // for Retina displays
  }
}
```

## Practical Recommendations for the Team

1. **Always use rem for font sizes**. This improves accessibility and allows content to scale according to user settings.

2. **Use rem for most UI component sizes**. This maintains consistency across different font settings.

3. **Use px for borders, shadows, and very small details**. For such elements, precision is more important than scalability.

4. **Use variables from \_variables.scss** for design consistency.

5. **Test your interface at different font sizes** (by changing browser settings).

## Setting the Root Size in the Project

By default, in most browsers, 1rem = 16px, but this can be changed by users. In our project:

```scss
html {
  // We do not change the root font size directly for accessibility
  // The base size is 1rem = 16px in most browsers by default
}
```

## Additional Files with Examples

1. **sizing-guidelines.scss** - contains basic variables and functions for working with sizes
2. **sizing-examples.scss** - shows examples of applying recommendations to various types of components
