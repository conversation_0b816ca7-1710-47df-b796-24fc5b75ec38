@use './sizing-guidelines.scss' as sizing;
@use '@theme/variables.scss' as *;

/**
 * Examples of using rem and px in Rhino Next project components
 *
 * This file demonstrates best practices for using rem and px
 * in various components, according to the project's design system.
 */

/* Game card example (GameCard) */
.gameCard {
  /* Use rem for main component sizes */
  width: 15rem; /* 240px at base font size 16px */
  height: 20rem; /* 320px */
  margin: 1rem; /* 16px */
  padding: 1.25rem; /* 20px */

  /* Use rem for border-radius according to the design system */
  border-radius: $radius-md; /* use variable from variables.scss */

  /* Use px for borders */
  border: 1px solid $color-primary; /* Use px for borders */

  /* Shadows described in px */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Shadows described in px */

  /* Font sizes in rem */
  .gameTitle {
    font-size: 1.25rem; /* 20px */
    margin-bottom: 0.5rem; /* 8px */
    line-height: 1.2; /* relative value (unitless) */
  }

  .gameDescription {
    font-size: 0.875rem; /* 14px */
    color: $color-surface-900; /* 14px color */
  }
}

/* Button example (Button) */
.button {
  /* Padding in rem */
  padding: 0.75rem 1.5rem; /* 12px 24px */
  margin: 0.5rem; /* 8px */

  /* Font sizes in rem */
  font-size: 1rem; /* 16px */

  /* Border in px */
  border: 2px solid transparent; /* 16px */

  /* Border-radius in rem for consistency with other elements */
  border-radius: 0.5rem; /* 8px */

  /* Transitions usually in seconds (s) or milliseconds (ms) */
  transition: all 0.2s ease; /* 8px */

  &:hover {
    /* Shadow offset in px */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Shadow offset in px */
  }

  /* Button sizes */
  &.small {
    padding: 0.5rem 1rem; /* 8px 16px */
    font-size: 0.875rem; /* 14px */
  }

  &.large {
    padding: 1rem 2rem; /* 16px 32px */
    font-size: 1.125rem; /* 18px */
  }
}

/* Header example (Header) */
.header {
  /* Height in rem for scalability */
  height: 4rem; /* 64px */
  padding: 0 1.5rem; /* 0 24px */

  /* px unit for aligning the bottom border */
  border-bottom: 1px solid $color-primary; /* px unit for aligning the bottom border */

  /* For navigation items use rem */
  .navItem {
    padding: 0.5rem 1rem; /* 8px 16px */
    margin: 0 0.25rem; /* 0 4px */
    font-size: 1rem; /* 16px */

    &:hover {
      /* Even for small interface elements, rem is preferable */
      border-bottom: 0.125rem solid $brand; /* Even for small interface elements, rem is preferable */
    }
  }
}

/* Grid Layout example (Grid Layout) */
.gridLayout {
  display: grid;
  /* Gaps between cells in rem */
  gap: 1.5rem; /* 24px */

  /* Minimum cell size in rem */
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr)); /* min 288px */

  /* Padding in rem */
  padding: 2rem; /* 32px */
}

/* Modal window example */
.modal {
  /* Outer dimensions in rem for scalability */
  width: 30rem; /* 480px */
  max-width: 90vw; /* 90% of the viewport width */

  /* Inner padding in rem */
  padding: 1.5rem; /* 24px */

  /* Border in px */
  border: 1px solid $color-primary; /* Border in px */

  /* Radius in rem according to the design system */
  border-radius: $radius-md; /* Radius in rem according to the design system */

  /* Shadow in px */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15); /* Shadow in px */

  .modalHeader {
    font-size: 1.5rem; /* 24px */
    margin-bottom: 1rem; /* 16px */
  }

  .modalFooter {
    margin-top: 1.25rem; /* 20px */
    padding-top: 1rem; /* 16px */
    /* Border in px */
    border-top: 1px solid $color-primary; /* Border in px */
  }
}

/* Example for responsive design using media queries */
@media (max-width: 48em) {
  /* 768px */
  .gameCard {
    width: 100%;
    margin: 0.75rem 0; /* 12px 0 */
  }

  .button {
    padding: 0.625rem 1.25rem; /* 10px 20px */
  }

  .header {
    height: 3.5rem; /* 56px */
    padding: 0 1rem; /* 0 16px */
  }
}

/**
 * This file shows how to use rem and px in the project.
 * General rule:
 * - rem for sizes, paddings, and fonts
 * - px for borders and small details
 */
