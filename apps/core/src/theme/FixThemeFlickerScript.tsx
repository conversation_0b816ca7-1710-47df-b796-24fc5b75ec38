import React from 'react'

// Detect dark mode preference and set the class before Tailwind/heroui loads.
// This avoids a flash of unstyled content (FOUC) when the page loads.
// reference https://github.com/pacocoursey/next-themes/issues/343
// consider to remove once the issue is resolved in next-themes
export const FixThemeFlickerScript = () => {
  return (
    <script id="theme-detection">
      {`(function() {
            try {
              var theme = localStorage.getItem('theme');
              var systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
              if (theme === 'dark' || (!theme && systemDark)) {
                document.documentElement.classList.add('dark');
              }
            } catch (e) {}
          })();`}
    </script>
  )
}
