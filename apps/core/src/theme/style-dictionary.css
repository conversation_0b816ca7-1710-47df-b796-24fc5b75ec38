/**
 * Do not edit directly, this file was auto-generated.
 */

:root {
  --radius-none: 0rem;
  --radius-xxs: 0.25rem;
  --radius-xs: 0.375rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 1000rem;
  --font-family-primary: Roboto;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-0xl: 1.375rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 3rem;
  --font-line-height-tight: 1;
  --font-line-height-normal: 1.2;
  --font-weight-baseline: 500;
  --font-weight-emphasized: 800;
  --size-xxs: 0.25rem;
  --size-xxs-1: 0.375rem;
  --size-xs: 0.5rem;
  --size-xs-1: 0.625rem;
  --size-sm: 0.75rem;
  --size-md: 1rem;
  --size-md-1: 1.125rem;
  --size-lg: 1.25rem;
  --size-xl: 1.5rem;
  --size-2xl: 2rem;
  --size-2xl-1: 2.25rem;
  --size-3xl-2: 2.5rem;
  --size-3xl-3: 2.75rem;
  --size-3xl: 3rem;
  --size-4xl: 4rem;
  --size-5xl: 5rem;
  --border-width-none: 0px;
  --border-width-default: 1px;
  --border-width-heavy: 2px;
  --icon-size-xxs: 0.875rem;
  --icon-size-xs: 1rem;
  --icon-size-sm: 1.25rem;
  --icon-size-md: 1.5rem;
  --color-primary: #1f1f1f;
  --color-primary-border: #1f1f1f;
  --color-primary-focus: #262626;
  --color-on-primary: #f8f8f8;
  --color-secondary: #8e8e8e;
  --color-secondary-border: #8e8e8e;
  --color-secondary-focus: #9b9b9b;
  --color-on-secondary: #1f1f1f;
  --color-tertiary: #b6b6b6;
  --color-tertiary-border: #b6b6b6;
  --color-tertiary-focus: #c5c5c5;
  --color-on-tertiary: #1f1f1f;
  --color-error: #c73636;
  --color-error-container: #ff9898;
  --color-success: #82d02f;
  --color-on-success: #ffffff;
  --color-background: #fefefe;
  --color-transparent: rgba(255, 255, 255, 0);
  --color-surface-100: #e6e6e6;
  --color-surface-200: #cfcfcf;
  --color-surface-300: #b7b7b7;
  --color-surface-400: #9f9f9f;
  --color-surface-500: #888888;
  --color-surface-600: #707070;
  --color-surface-700: #585858;
  --color-surface-800: #404040;
  --color-surface-900: #292929;
  --color-surface-1000: #111111;
  --font-family-secondary: var(--font-family-primary);
  --typography-label-xs: var(--font-weight-emphasized) var(--font-size-xs)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-label-sm: var(--font-weight-emphasized) var(--font-size-sm)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-label-md: var(--font-weight-emphasized) var(--font-size-md)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-body-baseline-xs: var(--font-weight-baseline) var(--font-size-xs)/var(--font-line-height-normal) var(--font-family-primary);
  --typography-body-baseline-sm: var(--font-weight-baseline) var(--font-size-sm)/var(--font-line-height-normal) var(--font-family-primary);
  --typography-body-baseline-md: var(--font-weight-baseline) var(--font-size-md)/var(--font-line-height-normal) var(--font-family-primary);
  --typography-headline-xs: var(--font-weight-emphasized) var(--font-size-lg)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-sm: var(--font-weight-emphasized) var(--font-size-0xl)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-md: var(--font-weight-emphasized) var(--font-size-2xl)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-lg: var(--font-weight-emphasized) var(--font-size-3xl)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-xl: var(--font-weight-emphasized) var(--font-size-4xl)/var(--font-line-height-tight) var(--font-family-primary);
  --elevation-level-0: 0 0 0 0 var(--color-transparent);
  --elevation-level-1: 0 4px 4px 0 rgba(230, 230, 230, 0.2), 0 2px 16px 0 rgba(207, 207, 207, 0.1);
  --color-fade: linear-gradient(180deg, rgba( var(--color-background) ,0) 0%, var(--color-background) 100%);
  --color-scrim: rgba( var(--color-background) ,0.6);
}

[data-theme='dark'], .dark {
  --color-primary: #f8f8f8;
  --color-primary-border: #f8f8f8;
  --color-primary-focus: #ffffff;
  --color-on-primary: #1f1f1f;
  --color-secondary: #9c9c9c;
  --color-secondary-focus: #a4a4a4;
  --color-secondary-border: #969696;
  --color-on-secondary: #ffffff;
  --color-tertiary: #616161;
  --color-tertiary-border: #616161;
  --color-tertiary-focus: #717171;
  --color-on-tertiary: #ffffff;
  --color-error: #c73636;
  --color-error-container: #ff9898;
  --color-success: #82d02f;
  --color-on-success: #ffffff;
  --color-background: #1f1f1f;
  --color-transparent: rgba(0, 0, 0, 0);
  --color-surface-100: #292929;
  --color-surface-200: #414141;
  --color-surface-300: #585858;
  --color-surface-400: #707070;
  --color-surface-500: #888888;
  --color-surface-600: #a0a0a0;
  --color-surface-700: #b8b8b8;
  --color-surface-800: #cfcfcf;
  --color-surface-900: #e7e7e7;
  --color-surface-1000: #ffffff;
  --color-fade: linear-gradient(180deg, rgba( var(--color-background) ,0) 0%, var(--color-background) 100%);
  --color-scrim: rgba( var(--color-background) ,0.6);
}
