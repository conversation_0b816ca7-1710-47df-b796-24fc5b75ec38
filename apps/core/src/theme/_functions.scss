@use 'sass:math';

@function calculate-rem($pixels, $base-font-size: 16px) {
  @if math.is-unitless($pixels) {
    $pixels: $pixels * 1px;
  }

  @if math.is-unitless($base-font-size) {
    $base-font-size: $base-font-size * 1px;
  }

  @return math.div($pixels, $base-font-size) * 1rem;
}

// Apply alpha transparency to a CSS custom property color
// @param $color-var - The CSS custom property (e.g., '--color-surface-500')
// @param $alpha - The alpha value between 0 and 1 (e.g., 0.5 for 50% opacity)
// @return CSS rgba function with the custom property
@function alpha($color-var, $alpha: 1) {
  @return rgba(from $color-var r g b / #{$alpha});
}
