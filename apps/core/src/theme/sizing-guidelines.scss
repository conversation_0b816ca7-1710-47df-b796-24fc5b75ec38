/**
 * Guidelines for using rem and px units
 * in the Rhino Next project
 */

/* Root font size (usually 16px in most browsers) */
/* This variable helps to understand the correspondence between rem and px */
$root-font-size: 16px;

/**
 * RECOMMENDED PRACTICES FOR USING REM AND PX
 * 
 * 1. USE REM FOR:
 *    - Font sizes
 *    - Spacing (margin/padding)
 *    - Container sizes
 *    - Line heights
 *    - Element positioning
 */
$font-sizes: (
  xs: 0.75rem, /* 12px */
  sm: 0.875rem, /* 14px */
  base: 1rem, /* 16px */
  lg: 1.125rem, /* 18px */
  xl: 1.25rem, /* 20px */
  2xl: 1.5rem, /* 24px */
  3xl: 1.875rem, /* 30px */
  4xl: 2.25rem, /* 36px */
  5xl: 3rem /* 48px */
);

$spacing: (
  0: 0,
  1: 0.25rem, /* 4px */
  2: 0.5rem, /* 8px */
  3: 0.75rem, /* 12px */
  4: 1rem, /* 16px */
  5: 1.25rem, /* 20px */
  6: 1.5rem, /* 24px */
  8: 2rem, /* 32px */
  10: 2.5rem, /* 40px */
  12: 3rem, /* 48px */
  16: 4rem, /* 64px */
  20: 5rem, /* 80px */
  24: 6rem, /* 96px */
  32: 8rem /* 128px */
);

/**
 * 2. USE PX FOR:
 *    - Borders
 *    - Shadows
 *    - Very small elements (1-2px)
 *    - Precise positioning
 */
$border-widths: (
  thin: 1px,
  default: 2px,
  thick: 3px
);

$shadow-sizes: (
  sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05),
  default: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
  0 1px 2px 0 rgba(0, 0, 0, 0.06),
  md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06),
  lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05),
  xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04)
);

/**
 * 3. SPECIAL CASES:
 */

/* For media queries, it is recommended to use em */
/* em is based on the parent element, but for media queries - on the browser's base size */
$breakpoints: (
  sm: 40em, /* ~640px */
  md: 48em, /* ~768px */
  lg: 64em, /* ~1024px */
  xl: 80em, /* ~1280px */
  2xl: 96em /* ~1536px */
);

/* For border-radius, it is sometimes convenient to use rem for consistency with other elements */
/* But for very small values, px can be used */
$border-radius: (
  sm: 0.125rem, /* 2px */
  default: 0.25rem, /* 4px */
  md: 0.375rem, /* 6px */
  lg: 0.5rem, /* 8px */
  xl: 0.75rem, /* 12px */
  2xl: 1rem, /* 16px */
  full: 9999px /* For creating completely round elements */
);

/**
 * 4. CONVERTER FUNCTIONS:
 */

/* Conversion from px to rem for convenience */
@function px-to-rem($px) {
  @return ($px / $root-font-size) * 1rem;
}

/* Conversion from rem to px to understand real sizes */
@function rem-to-px($rem) {
  @return ($rem * $root-font-size) / 1rem;
}

/**
 * 5. USAGE EXAMPLES:
 */

/* Example styles for the card component */
.exampleCard {
  /* Use rem for outer sizes and paddings */
  width: 18rem; /* 288px */
  padding: map-get($spacing, 4); /* 16px */
  margin-bottom: map-get($spacing, 6); /* 24px */

  /* Use px for borders and shadows */
  border: 1px solid #dddddd;
  box-shadow: map-get($shadow-sizes, md);

  /* Use rem for border-radius for consistency with the design */
  border-radius: map-get($border-radius, lg);

  /* Use rem for text */
  font-size: map-get($font-sizes, base);
  line-height: 1.5;
}

.exampleButton {
  /* Use rem for sizes and paddings */
  padding: 0.5rem 1rem;
  margin: 0.25rem;
  font-size: map-get($font-sizes, sm);

  /* Use px for borders */
  border: 1px solid transparent;

  /* Use rem for radius */
  border-radius: map-get($border-radius, default);
}

/**
 * 6. OUTPUT:
 * 
 * - rem is better for flexibility and scalability
 * - px is better for fixed elements and small details
 * - A mixed approach is most practical in modern web development
 */
