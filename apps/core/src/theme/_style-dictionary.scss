/**
 * Do not edit directly, this file was auto-generated.
 */

$radius-none: var(--radius-none);
$radius-xxs: var(--radius-xxs);
$radius-xs: var(--radius-xs);
$radius-sm: var(--radius-sm);
$radius-md: var(--radius-md);
$radius-lg: var(--radius-lg);
$radius-full: var(--radius-full);
$font-family-primary: var(--font-family-primary);
$font-size-xs: var(--font-size-xs);
$font-size-sm: var(--font-size-sm);
$font-size-md: var(--font-size-md);
$font-size-lg: var(--font-size-lg);
$font-size-xl: var(--font-size-xl);
$font-size-0xl: var(--font-size-0xl);
$font-size-2xl: var(--font-size-2xl);
$font-size-3xl: var(--font-size-3xl);
$font-size-4xl: var(--font-size-4xl);
$font-line-height-tight: var(--font-line-height-tight);
$font-line-height-normal: var(--font-line-height-normal);
$font-weight-baseline: var(--font-weight-baseline);
$font-weight-emphasized: var(--font-weight-emphasized);
$size-xxs: var(--size-xxs);
$size-xxs-1: var(--size-xxs-1);
$size-xs: var(--size-xs);
$size-xs-1: var(--size-xs-1);
$size-sm: var(--size-sm);
$size-md: var(--size-md);
$size-md-1: var(--size-md-1);
$size-lg: var(--size-lg);
$size-xl: var(--size-xl);
$size-2xl: var(--size-2xl);
$size-2xl-1: var(--size-2xl-1);
$size-3xl-2: var(--size-3xl-2);
$size-3xl-3: var(--size-3xl-3);
$size-3xl: var(--size-3xl);
$size-4xl: var(--size-4xl);
$size-5xl: var(--size-5xl);
$border-width-none: var(--border-width-none);
$border-width-default: var(--border-width-default);
$border-width-heavy: var(--border-width-heavy);
$icon-size-xxs: var(--icon-size-xxs);
$icon-size-xs: var(--icon-size-xs);
$icon-size-sm: var(--icon-size-sm);
$icon-size-md: var(--icon-size-md);
$color-primary: var(--color-primary);
$color-primary-border: var(--color-primary-border);
$color-primary-focus: var(--color-primary-focus);
$color-on-primary: var(--color-on-primary);
$color-secondary: var(--color-secondary);
$color-secondary-border: var(--color-secondary-border);
$color-secondary-focus: var(--color-secondary-focus);
$color-on-secondary: var(--color-on-secondary);
$color-tertiary: var(--color-tertiary);
$color-tertiary-border: var(--color-tertiary-border);
$color-tertiary-focus: var(--color-tertiary-focus);
$color-on-tertiary: var(--color-on-tertiary);
$color-error: var(--color-error);
$color-error-container: var(--color-error-container);
$color-success: var(--color-success);
$color-on-success: var(--color-on-success);
$color-background: var(--color-background);
$color-transparent: var(--color-transparent);
$color-surface-100: var(--color-surface-100);
$color-surface-200: var(--color-surface-200);
$color-surface-300: var(--color-surface-300);
$color-surface-400: var(--color-surface-400);
$color-surface-500: var(--color-surface-500);
$color-surface-600: var(--color-surface-600);
$color-surface-700: var(--color-surface-700);
$color-surface-800: var(--color-surface-800);
$color-surface-900: var(--color-surface-900);
$color-surface-1000: var(--color-surface-1000);
$font-family-secondary: var(--font-family-secondary);
$typography-label-xs: var(--typography-label-xs);
$typography-label-sm: var(--typography-label-sm);
$typography-label-md: var(--typography-label-md);
$typography-body-baseline-xs: var(--typography-body-baseline-xs);
$typography-body-baseline-sm: var(--typography-body-baseline-sm);
$typography-body-baseline-md: var(--typography-body-baseline-md);
$typography-headline-xs: var(--typography-headline-xs);
$typography-headline-sm: var(--typography-headline-sm);
$typography-headline-md: var(--typography-headline-md);
$typography-headline-lg: var(--typography-headline-lg);
$typography-headline-xl: var(--typography-headline-xl);
$elevation-level-0: var(--elevation-level-0);
$elevation-level-1: var(--elevation-level-1);
$color-fade: var(--color-fade);
$color-scrim: var(--color-scrim);
