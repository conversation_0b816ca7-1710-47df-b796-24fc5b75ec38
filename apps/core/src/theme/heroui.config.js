// This file was auto-generated by generate-heroui-config.mjs
// Do not edit directly - run "yarn generate-heroui-config" to regenerate

export default {
  themes: {
    light: {
      colors: {
        primary: {
          50: '#929292',
          100: '#858585',
          200: '#6b6b6b',
          300: '#525252',
          400: '#383838',
          500: '#1f1f1f',
          600: '#0b0b0b',
          700: '#000000',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#1f1f1f',
          foreground: '#f8f8f8',
        },
        secondary: {
          50: '#ffffff',
          100: '#f4f4f4',
          200: '#dadada',
          300: '#c1c1c1',
          400: '#a7a7a7',
          500: '#8e8e8e',
          600: '#7a7a7a',
          700: '#656565',
          800: '#515151',
          900: '#3c3c3c',
          DEFAULT: '#8e8e8e',
          foreground: '#1f1f1f',
        },
        success: {
          50: '#f2faea',
          100: '#e6f6d5',
          200: '#cdecac',
          300: '#b4e382',
          400: '#9bd959',
          500: '#82d02f',
          600: '#6daf27',
          700: '#588d20',
          800: '#446c18',
          900: '#2f4b11',
          DEFAULT: '#82d02f',
          foreground: '#ffffff',
        },
        danger: {
          50: '#f9e9e9',
          100: '#f4d5d5',
          200: '#e9adad',
          300: '#de8585',
          400: '#d35d5d',
          500: '#c73636',
          600: '#a72d2d',
          700: '#872525',
          800: '#671c1c',
          900: '#471313',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        warning: {
          50: '#f9e9e9',
          100: '#f4d5d5',
          200: '#e9adad',
          300: '#de8585',
          400: '#d35d5d',
          500: '#c73636',
          600: '#a72d2d',
          700: '#872525',
          800: '#671c1c',
          900: '#471313',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        background: '#fefefe',
        foreground: '#404040',
        focus: '#262626',
        overlay: '#888888',
        default: {
          50: '#fbfbfb',
          100: '#eeeeee',
          200: '#d4d4d4',
          300: '#bbbbbb',
          400: '#a1a1a1',
          500: '#888888',
          600: '#747474',
          700: '#5f5f5f',
          800: '#4b4b4b',
          900: '#363636',
          DEFAULT: '#888888',
          foreground: '#111111',
        },
        content1: {
          DEFAULT: '#e6e6e6',
          foreground: '#292929',
        },
        content2: {
          DEFAULT: '#cfcfcf',
          foreground: '#404040',
        },
        content3: {
          DEFAULT: '#b7b7b7',
          foreground: '#585858',
        },
        content4: {
          DEFAULT: '#b6b6b6',
          foreground: '#1f1f1f',
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: '#a6a6a6',
          100: '#bbbbbb',
          200: '#cfcfcf',
          300: '#e4e4e4',
          400: '#f8f8f8',
          500: '#ffffff',
          600: '#ffffff',
          700: '#ffffff',
          800: '#ffffff',
          900: '#ffffff',
          DEFAULT: '#ffffff',
          foreground: '#1f1f1f',
        },
        secondary: {
          50: '#4a4a4a',
          100: '#5f5f5f',
          200: '#737373',
          300: '#888888',
          400: '#9c9c9c',
          500: '#b5b5b5',
          600: '#cfcfcf',
          700: '#e8e8e8',
          800: '#ffffff',
          900: '#ffffff',
          DEFAULT: '#b5b5b5',
          foreground: '#ffffff',
        },
        success: {
          50: '#2f4b11',
          100: '#446c18',
          200: '#588d20',
          300: '#6daf27',
          400: '#82d02f',
          500: '#9bd959',
          600: '#b4e382',
          700: '#cdecac',
          800: '#e6f6d5',
          900: '#f2faea',
          DEFAULT: '#9bd959',
          foreground: '#ffffff',
        },
        danger: {
          50: '#471313',
          100: '#671c1c',
          200: '#872525',
          300: '#a72d2d',
          400: '#c73636',
          500: '#d35d5d',
          600: '#de8585',
          700: '#e9adad',
          800: '#f4d5d5',
          900: '#f9e9e9',
          DEFAULT: '#d35d5d',
          foreground: '#000000',
        },
        warning: {
          50: '#471313',
          100: '#671c1c',
          200: '#872525',
          300: '#a72d2d',
          400: '#c73636',
          500: '#d35d5d',
          600: '#de8585',
          700: '#e9adad',
          800: '#f4d5d5',
          900: '#f9e9e9',
          DEFAULT: '#d35d5d',
          foreground: '#000000',
        },
        background: '#1f1f1f',
        foreground: '#cfcfcf',
        focus: '#ffffff',
        overlay: '#888888',
        default: {
          50: '#363636',
          100: '#4b4b4b',
          200: '#5f5f5f',
          300: '#747474',
          400: '#888888',
          500: '#a1a1a1',
          600: '#bbbbbb',
          700: '#d4d4d4',
          800: '#eeeeee',
          900: '#fbfbfb',
          DEFAULT: '#a1a1a1',
          foreground: '#ffffff',
        },
        content1: {
          DEFAULT: '#292929',
          foreground: '#e7e7e7',
        },
        content2: {
          DEFAULT: '#414141',
          foreground: '#cfcfcf',
        },
        content3: {
          DEFAULT: '#585858',
          foreground: '#b8b8b8',
        },
        content4: {
          DEFAULT: '#616161',
          foreground: '#ffffff',
        },
      },
    },
  },
  layout: {
    radius: {
      small: '0.5rem',
      medium: '0.75rem',
      large: '1rem',
    },
    borderWidth: {
      small: '1px',
      medium: '1px',
      large: '1px',
    },
  },
}
