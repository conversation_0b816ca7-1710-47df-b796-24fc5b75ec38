import { HttpResponse, http } from 'msw'
import banners from '@/mock/server/handlers/api/rhinoent-middleware/banners.json'

export const rlHandlers = [
  http.get('*/api/rhinoent-middleware/banners', async ({ request, params }) => {
    return HttpResponse.json(banners)
  }),
  http.get('*/api/rhinoent-middleware/promotions', async ({ request, params }) => {
    return HttpResponse.json(require('./promotions.json'))
  }),
]
