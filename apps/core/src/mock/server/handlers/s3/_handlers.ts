import { HttpResponse, http } from 'msw'
import games from '@/mock/server/handlers/s3/games/index.json'

export const s3Handlers = [
  http.get('*/games/index.json', () => {
    return HttpResponse.json(games, {
      headers: {
        'Content-Type': 'application/json',
        'Last-Modified': 'Wed, 20 Dec 2023 12:44:39 GMT',
        Etag: '"2b7f2032b5073b7481e001f2d8e362d9"',
      },
    })
  }),
  http.head('*/games/index.json', () => {
    return HttpResponse.json('', {
      headers: {
        'Content-Type': 'application/json',
        'Last-Modified': 'Wed, 20 Dec 2023 12:44:39 GMT',
        Etag: '"2b7f2032b5073b7481e001f2d8e362d9"',
      },
    })
  }),
]
