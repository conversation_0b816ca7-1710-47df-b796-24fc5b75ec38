import { setupServer } from 'msw/node'
import { handlers } from '@/mock/server/handlers'

export const server = setupServer(...handlers)

export const startMSWServer = () => {
  console.log('Starting MSW server...')
  server.listen({ onUnhandledRequest: 'bypass' })
}

export const stopMSWServer = () => {
  console.log('Stop MSW server...')
  server.close()
}

if (process.env.NODE_ENV === 'test') {
  server.events.on('request:start', ({ request }) => {
    console.info('[MSW] Mock Outgoing request:', request.method, request.url, new Date().toISOString())
  })

  server.events.on('request:unhandled', ({ request }) => {
    console.info('[MSW] Unhandled request:', request.method, request.url)
  })

  server.events.on('response:bypass', async ({ request }) => {
    console.info('[MSW] bypass request:', request.method, request.url)
  })
}

export default { startMSWServer, stopMSWServer }
