import React from 'react'

interface IChevronDownProps {
  size?: number
  color?: string
  strokeWidth?: number
  className?: string
}

export const ChevronDownIcon: React.FC<IChevronDownProps> = ({
  size = 16,
  color = 'currentColor',
  strokeWidth = 2,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 16 16"
    fill="none"
    className={className}
    {...props}>
    <path
      d="M4 6L7.29289 9.29289C7.68342 9.68342 8.31658 9.68342 8.70711 9.29289L12 6"
      stroke={color}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
