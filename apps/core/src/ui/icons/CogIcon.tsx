import React from 'react'

interface ICogIconProps {
  size?: number
  color?: string
  className?: string
}

export const CogIcon: React.FC<ICogIconProps> = ({ size = 20, color = 'currentColor', className }) => (
  <svg
    width={size}
    height={size}
    className={className}
    fill={color}
    viewBox="0 0 14 12"
    xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.2413 4.884L10.9557 1.1336C10.5293 0.4344 9.73091 0 8.87251 0H5.12931C4.27011 0 3.47171 0.4344 3.04611 1.1336L0.759712 4.884C0.550112 5.228 0.445312 5.6144 0.445312 6C0.445312 6.3856 0.550112 6.772 0.759712 7.116L3.04531 10.8664C3.47171 11.5656 4.27011 12 5.12851 12H8.87171C9.73091 12 10.5293 11.5656 10.9549 10.8664L13.2413 7.116C13.6597 6.4272 13.6605 5.5728 13.2413 4.884ZM7.00051 8.4C5.67491 8.4 4.60051 7.3256 4.60051 6C4.60051 4.6744 5.67491 3.6 7.00051 3.6C8.32611 3.6 9.40051 4.6744 9.40051 6C9.40051 7.3256 8.32611 8.4 7.00051 8.4Z"
      fill="#68686C"
    />
  </svg>
)
