import { useContext } from 'react'
import { LocaleContext } from '@/context/locale/LocaleContext'
import { getMarketByLocale } from '@/utils/locale'

export const useLocale = () => {
  const context = useContext(LocaleContext)
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider')
  }
  return context
}

export const useMarket = () => {
  const locale = useLocale()
  const market = getMarketByLocale(locale)
  return market
}
