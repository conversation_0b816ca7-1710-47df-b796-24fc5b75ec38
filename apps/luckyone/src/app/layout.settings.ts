import { Funnel_Sans, Outfit } from 'next/font/google'

export const SecondaryFont = Funnel_Sans({
  subsets: ['latin'],
})

export const OutfitFont = Outfit({
  subsets: ['latin'],
})

// const Gilroy = localFont({
//   src: [
//     {
//       path: '../../assets/fonts/<PERSON>roy/<PERSON>roy-UltraLight.ttf',
//       weight: '200',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/<PERSON>roy/Gilroy-Light.ttf',
//       weight: '300',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/<PERSON>roy/Gilroy-Regular.ttf',
//       weight: '400',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/<PERSON><PERSON>/<PERSON><PERSON>-RegularItalic.ttf',
//       weight: '400',
//       style: 'italic',
//     },
//     {
//       path: '../../assets/fonts/<PERSON><PERSON>/<PERSON><PERSON>-Medium.ttf',
//       weight: '500',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/<PERSON><PERSON>/<PERSON><PERSON>-SemiBold.ttf',
//       weight: '600',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/Gilroy/Gilroy-Bold.ttf',
//       weight: '700',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/Gilroy/Gilroy-ExtraBold.ttf',
//       weight: '800',
//       style: 'normal',
//     },
//     {
//       path: '../../assets/fonts/Gilroy/Gilroy-Black.ttf',
//       weight: '900',
//       style: 'normal',
//     },
//   ],
// })

export { OutfitFont as PrimaryFont }
