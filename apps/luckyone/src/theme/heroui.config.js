// This file was auto-generated by generate-heroui-config.mjs
// Do not edit directly - run "yarn generate-heroui-config" to regenerate

export default {
  themes: {
    light: {
      colors: {
        primary: {
          50: '#fff7e5',
          100: '#ffeecc',
          200: '#ffdd99',
          300: '#ffcc66',
          400: '#ffbb33',
          500: '#ffaa00',
          600: '#d68f00',
          700: '#ad7400',
          800: '#855800',
          900: '#5c3d00',
          DEFAULT: '#ffaa00',
          foreground: '#08082a',
        },
        secondary: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#ffffff',
          500: '#ffffff',
          600: '#ebebeb',
          700: '#d6d6d6',
          800: '#c2c2c2',
          900: '#adadad',
          DEFAULT: '#ffffff',
          foreground: '#1e1c38',
        },
        success: {
          50: '#f2faea',
          100: '#e6f6d5',
          200: '#cdecac',
          300: '#b4e382',
          400: '#9bd959',
          500: '#82d02f',
          600: '#6daf27',
          700: '#588d20',
          800: '#446c18',
          900: '#2f4b11',
          DEFAULT: '#82d02f',
          foreground: '#ffffff',
        },
        danger: {
          50: '#f9e9e9',
          100: '#f4d5d5',
          200: '#e9adad',
          300: '#de8585',
          400: '#d35d5d',
          500: '#c73636',
          600: '#a72d2d',
          700: '#872525',
          800: '#671c1c',
          900: '#471313',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        warning: {
          50: '#f9e9e9',
          100: '#f4d5d5',
          200: '#e9adad',
          300: '#de8585',
          400: '#d35d5d',
          500: '#c73636',
          600: '#a72d2d',
          700: '#872525',
          800: '#671c1c',
          900: '#471313',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        background: '#e8e7f3',
        foreground: '#52506b',
        focus: '#e1a123',
        overlay: '#9997b1',
        default: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#efeef3',
          300: '#d2d1dd',
          400: '#b6b4c7',
          500: '#9997b1',
          600: '#82809f',
          700: '#6c6a8d',
          800: '#5a5875',
          900: '#48475e',
          DEFAULT: '#9997b1',
          foreground: '#1e1c38',
        },
        content1: {
          DEFAULT: '#fafaff',
          foreground: '#2f2e42',
        },
        content2: {
          DEFAULT: '#d7d6e4',
          foreground: '#52506b',
        },
        content3: {
          DEFAULT: '#cdcbde',
          foreground: '#75738e',
        },
        content4: {
          DEFAULT: '#741CC2',
          foreground: '#ffffff',
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: '#806200',
          100: '#a98100',
          200: '#d1a100',
          300: '#fac000',
          400: '#ffcc24',
          500: '#ffd857',
          600: '#ffe48a',
          700: '#fff0bd',
          800: '#fffbf0',
          900: '#ffffff',
          DEFAULT: '#ffd857',
          foreground: '#08082a',
        },
        secondary: {
          50: '#000000',
          100: '#000000',
          200: '#020202',
          300: '#151517',
          400: '#29292c',
          500: '#424246',
          600: '#5a5a61',
          700: '#73737b',
          800: '#8d8d94',
          900: '#9a9aa1',
          DEFAULT: '#424246',
          foreground: '#ffffff',
        },
        success: {
          50: '#2f4b11',
          100: '#446c18',
          200: '#588d20',
          300: '#6daf27',
          400: '#82d02f',
          500: '#9bd959',
          600: '#b4e382',
          700: '#cdecac',
          800: '#e6f6d5',
          900: '#f2faea',
          DEFAULT: '#9bd959',
          foreground: '#ffffff',
        },
        danger: {
          50: '#471313',
          100: '#671c1c',
          200: '#872525',
          300: '#a72d2d',
          400: '#c73636',
          500: '#d35d5d',
          600: '#de8585',
          700: '#e9adad',
          800: '#f4d5d5',
          900: '#f9e9e9',
          DEFAULT: '#d35d5d',
          foreground: '#000000',
        },
        warning: {
          50: '#471313',
          100: '#671c1c',
          200: '#872525',
          300: '#a72d2d',
          400: '#c73636',
          500: '#d35d5d',
          600: '#de8585',
          700: '#e9adad',
          800: '#f4d5d5',
          900: '#f9e9e9',
          DEFAULT: '#d35d5d',
          foreground: '#000000',
        },
        background: '#111113',
        foreground: '#939397',
        focus: '#ffc400',
        overlay: '#3b3b3e',
        default: {
          50: '#000000',
          100: '#000000',
          200: '#131314',
          300: '#272729',
          400: '#3b3b3e',
          500: '#545458',
          600: '#6d6d72',
          700: '#86868c',
          800: '#a0a0a5',
          900: '#adadb1',
          DEFAULT: '#545458',
          foreground: '#ffffff',
        },
        content1: {
          DEFAULT: '#161618',
          foreground: '#c0c0c9',
        },
        content2: {
          DEFAULT: '#1d1d1f',
          foreground: '#939397',
        },
        content3: {
          DEFAULT: '#28282a',
          foreground: '#68686c',
        },
        content4: {
          DEFAULT: '#741CC2',
          foreground: '#ffffff',
        },
      },
    },
  },
  layout: {
    radius: {
      small: '0.5rem',
      medium: '0.75rem',
      large: '1rem',
    },
    borderWidth: {
      small: '1px',
      medium: '1px',
      large: '1px',
    },
  },
}
