/**
 * Do not edit directly, this file was auto-generated.
 */

:root {
  --radius-none: 0rem;
  --radius-xxs: 0.25rem;
  --radius-xs: 0.375rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 1000rem;
  --font-family-primary: Outfit;
  --font-family-secondary: 'Funnel Sans';
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-0xl: 1.375rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 3rem;
  --font-line-height-tight: 1;
  --font-line-height-normal: 1.2;
  --font-weight-baseline: 500;
  --font-weight-emphasized: 800;
  --size-xxs: 0.25rem;
  --size-xxs-1: 0.375rem;
  --size-xs: 0.5rem;
  --size-xs-1: 0.625rem;
  --size-sm: 0.75rem;
  --size-md: 1rem;
  --size-md-1: 1.125rem;
  --size-lg: 1.25rem;
  --size-xl: 1.5rem;
  --size-2xl: 2rem;
  --size-2xl-1: 2.25rem;
  --size-3xl-2: 2.5rem;
  --size-3xl-3: 2.75rem;
  --size-3xl: 3rem;
  --size-4xl: 4rem;
  --size-5xl: 5rem;
  --border-width-none: 0px;
  --border-width-default: 1px;
  --border-width-heavy: 2px;
  --icon-size-xxs: 0.875rem;
  --icon-size-xs: 1rem;
  --icon-size-sm: 1.25rem;
  --icon-size-md: 1.5rem;
  --color-primary: #ffaa00;
  --color-primary-border: #ffaa00;
  --color-primary-focus: #e1a123;
  --color-on-primary: #08082a;
  --color-secondary: #ffffff;
  --color-secondary-border: #d7d6e4;
  --color-secondary-focus: #ffffff;
  --color-tertiary: linear-gradient(180deg, #741CC2 0%, #B02FD8 100%);
  --color-tertiary-border: linear-gradient(180deg, #9736E2 0%, #D153FF 100%);
  --color-tertiary-focus: linear-gradient(180deg, #741CC2 0%, #B02FD8 100%);
  --color-on-tertiary: #ffffff;
  --color-error: #c73636;
  --color-error-container: #ff9898;
  --color-success: #82d02f;
  --color-on-success: #ffffff;
  --color-background: #e8e7f3;
  --color-transparent: rgba(255, 255, 255, 0);
  --color-surface-100: #fafaff;
  --color-surface-200: #d7d6e4;
  --color-surface-300: #cdcbde;
  --color-surface-400: #bab8d1;
  --color-surface-500: #9997b1;
  --color-surface-600: #8a88a1;
  --color-surface-700: #75738e;
  --color-surface-800: #52506b;
  --color-surface-900: #2f2e42;
  --color-surface-1000: #1e1c38;
  --typography-label-xs: var(--font-weight-emphasized) var(--font-size-xs)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-label-sm: var(--font-weight-emphasized) var(--font-size-sm)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-label-md: var(--font-weight-emphasized) var(--font-size-md)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-body-baseline-xs: var(--font-weight-baseline) var(--font-size-xs)/var(--font-line-height-normal) var(--font-family-secondary);
  --typography-body-baseline-sm: var(--font-weight-baseline) var(--font-size-sm)/var(--font-line-height-normal) var(--font-family-secondary);
  --typography-body-baseline-md: var(--font-weight-baseline) var(--font-size-md)/var(--font-line-height-normal) var(--font-family-secondary);
  --typography-headline-xs: var(--font-weight-emphasized) var(--font-size-lg)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-sm: var(--font-weight-emphasized) var(--font-size-0xl)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-md: var(--font-weight-emphasized) var(--font-size-2xl)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-lg: var(--font-weight-emphasized) var(--font-size-3xl)/var(--font-line-height-tight) var(--font-family-primary);
  --typography-headline-xl: var(--font-weight-emphasized) var(--font-size-4xl)/var(--font-line-height-tight) var(--font-family-primary);
  --elevation-level-0: 0 0 0 0 var(--color-transparent);
  --elevation-level-1: 0 4px 4px 0 rgba(250, 250, 255, 0.2), 0 2px 16px 0 rgba(215, 214, 228, 0.1);
  --color-on-secondary: var(--color-surface-1000);
  --color-fade: linear-gradient(180deg, rgba( var(--color-background) ,0) 0%, var(--color-background) 100%);
  --color-scrim: rgba( var(--color-background) ,0.6);
}

[data-theme='dark'], .dark {
  --color-primary: #ffcc24;
  --color-primary-border: #ffcc24;
  --color-primary-focus: #ffc400;
  --color-on-primary: #08082a;
  --color-secondary: linear-gradient(180deg, #29292C 0%, #353539 100%);
  --color-secondary-focus: linear-gradient(180deg, #29292C 40%, #C19D29 100%);
  --color-secondary-border: linear-gradient(180deg, #333337 0%, #404045 100%);
  --color-on-secondary: #ffffff;
  --color-tertiary: linear-gradient(180deg, #741CC2 0%, #B02FD8 100%);
  --color-tertiary-border: linear-gradient(180deg, #9736E2 0%, #D153FF 100%);
  --color-tertiary-focus: linear-gradient(180deg, #9736E2 0%, #D153FF 100%);
  --color-on-tertiary: #ffffff;
  --color-error: #c73636;
  --color-error-container: #ff9898;
  --color-success: #82d02f;
  --color-on-success: #ffffff;
  --color-background: #111113;
  --color-transparent: rgba(0, 0, 0, 0);
  --color-surface-100: #161618;
  --color-surface-200: #1d1d1f;
  --color-surface-300: #28282a;
  --color-surface-400: #323235;
  --color-surface-500: #3b3b3e;
  --color-surface-600: #464649;
  --color-surface-700: #68686c;
  --color-surface-800: #939397;
  --color-surface-900: #c0c0c9;
  --color-surface-1000: #ffffff;
  --color-fade: linear-gradient(180deg, rgba( var(--color-background) ,0) 0%, var(--color-background) 100%);
  --color-scrim: rgba( var(--color-background) ,0.6);
}
