/* Custom font definitions for Storybook */
@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('../assets/fonts/<PERSON><PERSON>/<PERSON><PERSON>-UltraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('../assets/fonts/<PERSON>roy/<PERSON>roy-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>roy';
  src: url('../assets/fonts/<PERSON><PERSON>/<PERSON>roy-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>roy';
  src: url('../assets/fonts/<PERSON>roy/Gilroy-RegularItalic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('../assets/fonts/<PERSON><PERSON>/<PERSON>roy-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('../assets/fonts/<PERSON>roy/<PERSON>roy-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('../assets/fonts/Gilroy/Gilroy-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('../assets/fonts/Gilroy/Gilroy-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('../assets/fonts/Gilroy/Gilroy-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Storybook-specific font classes */
.storybookGilroyPrimary {
  font-family: 'Gilroy', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}
