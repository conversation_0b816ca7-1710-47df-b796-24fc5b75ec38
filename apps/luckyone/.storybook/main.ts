import path from 'path'
import type { WebpackConfiguration } from '@storybook/core-webpack'
import type { StorybookConfig } from '@storybook/nextjs'
import baseConfig from '../../../.storybook/main'

const config: StorybookConfig = {
  ...baseConfig,
  stories: [
    '../../core/src/components/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../../core/src/modules/**/*.stories.@(js|jsx|ts|tsx|mdx)',
  ],
  features: {
    experimentalRSC: true,
  },
  webpackFinal: async (config: WebpackConfiguration) => {
    config.resolve = config.resolve || {}
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@widgets': path.resolve(__dirname, '../../core/src/components/@widgets'),
      '@components': path.resolve(__dirname, '../../core/src/components'),
      '@constants': path.resolve(__dirname, '../../core/src/constants'),
      '@modules': path.resolve(__dirname, '../../core/src/modules'),
      '@theme': path.resolve(__dirname, '../src/theme'),
      '@app': path.resolve(__dirname, '../src/app'),
      'server-only': path.resolve(__dirname, '../../../.storybook/mocks/server-only.ts'),
      '@components/ImgIx/server-utils': path.resolve(__dirname, '../../../.storybook/mocks/imgix.ts'),
      '@/': path.resolve(__dirname, '../../core/src/'),
    }
    return config
  },
}

export default config
