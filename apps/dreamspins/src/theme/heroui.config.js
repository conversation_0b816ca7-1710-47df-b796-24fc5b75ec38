// This file was auto-generated by generate-heroui-config.mjs
// Do not edit directly - run "yarn generate-heroui-config" to regenerate

export default {
  themes: {
    light: {
      colors: {
        primary: {
          50: '#e5f7ff',
          100: '#ccf0ff',
          200: '#99e0ff',
          300: '#66d1ff',
          400: '#33c1ff',
          500: '#00b2ff',
          600: '#0096d6',
          700: '#0079ad',
          800: '#005d85',
          900: '#00405c',
          DEFAULT: '#00b2ff',
          foreground: '#0a1a2a',
        },
        secondary: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#ffffff',
          500: '#f6f9ff',
          600: '#cddeff',
          700: '#a4c3ff',
          800: '#7ca7ff',
          900: '#538cff',
          DEFAULT: '#f6f9ff',
          foreground: '#1e2a38',
        },
        success: {
          50: '#eafaf2',
          100: '#d5f6e6',
          200: '#aceccd',
          300: '#82e3b4',
          400: '#59d99b',
          500: '#2fd082',
          600: '#27af6d',
          700: '#208d58',
          800: '#186c44',
          900: '#114b2f',
          DEFAULT: '#2fd082',
          foreground: '#ffffff',
        },
        danger: {
          50: '#fcf4f4',
          100: '#f8dfdf',
          200: '#eeb6b6',
          300: '#e48d8d',
          400: '#db6363',
          500: '#d13a3a',
          600: '#b72b2b',
          700: '#962323',
          800: '#751b1b',
          900: '#541414',
          DEFAULT: '#d13a3a',
          foreground: '#000000',
        },
        warning: {
          50: '#fcf4f4',
          100: '#f8dfdf',
          200: '#eeb6b6',
          300: '#e48d8d',
          400: '#db6363',
          500: '#d13a3a',
          600: '#b72b2b',
          700: '#962323',
          800: '#751b1b',
          900: '#541414',
          DEFAULT: '#d13a3a',
          foreground: '#000000',
        },
        background: '#e7f3f8',
        foreground: '#507b96',
        focus: '#0099e1',
        overlay: '#97b1c9',
        default: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#fcfdfd',
          300: '#dae3ec',
          400: '#b9cada',
          500: '#97b1c9',
          600: '#7c9dbb',
          700: '#6189ad',
          800: '#4e7497',
          900: '#41607c',
          DEFAULT: '#97b1c9',
          foreground: '#1c2e38',
        },
        content1: {
          DEFAULT: '#f7faff',
          foreground: '#2f4e6b',
        },
        content2: {
          DEFAULT: '#d6e4f7',
          foreground: '#507b96',
        },
        content3: {
          DEFAULT: '#cbe0f7',
          foreground: '#738eaa',
        },
        content4: {
          DEFAULT: '#1CB2C2',
          foreground: '#ffffff',
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: '#006280',
          100: '#0081a9',
          200: '#00a1d1',
          300: '#00c0fa',
          400: '#24ccff',
          500: '#57d8ff',
          600: '#8ae4ff',
          700: '#bdf0ff',
          800: '#f0fbff',
          900: '#ffffff',
          DEFAULT: '#57d8ff',
          foreground: '#0a1a2a',
        },
        secondary: {
          50: '#000000',
          100: '#000000',
          200: '#000101',
          300: '#0d161d',
          400: '#1a2c39',
          500: '#2a475c',
          600: '#3a627f',
          700: '#4a7da2',
          800: '#6696b9',
          900: '#77a2c1',
          DEFAULT: '#2a475c',
          foreground: '#ffffff',
        },
        success: {
          50: '#114b2f',
          100: '#186c44',
          200: '#208d58',
          300: '#27af6d',
          400: '#2fd082',
          500: '#59d99b',
          600: '#82e3b4',
          700: '#aceccd',
          800: '#d5f6e6',
          900: '#eafaf2',
          DEFAULT: '#59d99b',
          foreground: '#ffffff',
        },
        danger: {
          50: '#541414',
          100: '#751b1b',
          200: '#962323',
          300: '#b72b2b',
          400: '#d13a3a',
          500: '#db6363',
          600: '#e48d8d',
          700: '#eeb6b6',
          800: '#f8dfdf',
          900: '#fcf4f4',
          DEFAULT: '#db6363',
          foreground: '#000000',
        },
        warning: {
          50: '#541414',
          100: '#751b1b',
          200: '#962323',
          300: '#b72b2b',
          400: '#d13a3a',
          500: '#db6363',
          600: '#e48d8d',
          700: '#eeb6b6',
          800: '#f8dfdf',
          900: '#fcf4f4',
          DEFAULT: '#db6363',
          foreground: '#000000',
        },
        background: '#13232b',
        foreground: '#93a9b7',
        focus: '#00b2e1',
        overlay: '#3b4e5e',
        default: {
          50: '#000000',
          100: '#0c1013',
          200: '#1c242c',
          300: '#2b3945',
          400: '#3b4e5e',
          500: '#4f687d',
          600: '#62829d',
          700: '#829bb0',
          800: '#a1b4c4',
          900: '#b1c0ce',
          DEFAULT: '#4f687d',
          foreground: '#f0f8ff',
        },
        content1: {
          DEFAULT: '#181f23',
          foreground: '#c0d0e9',
        },
        content2: {
          DEFAULT: '#1d2d3f',
          foreground: '#93a9b7',
        },
        content3: {
          DEFAULT: '#28324a',
          foreground: '#687a8c',
        },
        content4: {
          DEFAULT: '#1CB2C2',
          foreground: '#ffffff',
        },
      },
    },
  },
  layout: {
    radius: {
      small: '0.5rem',
      medium: '0.75rem',
      large: '1rem',
    },
    borderWidth: {
      small: '1px',
      medium: '1px',
      large: '1px',
    },
  },
}
