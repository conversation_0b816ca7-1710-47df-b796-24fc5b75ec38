// This file is auto-generated from style-dictionary.css
// Do not edit directly. Update the CSS source and regenerate if needed.

export const styleDictionaryVars = {
  '--radius-none': '0rem',
  '--radius-xxs': '0.25rem',
  '--radius-xs': '0.375rem',
  '--radius-sm': '0.5rem',
  '--radius-md': '0.75rem',
  '--radius-lg': '1rem',
  '--radius-full': '1000rem',
  '--font-family-primary': 'Funnel Sans',
  '--font-family-secondary': 'Roboto',
  '--font-size-xs': '0.75rem',
  '--font-size-sm': '0.875rem',
  '--font-size-md': '1rem',
  '--font-size-lg': '1.125rem',
  '--font-size-xl': '1.25rem',
  '--font-size-0xl': '1.375rem',
  '--font-size-2xl': '1.5rem',
  '--font-size-3xl': '2rem',
  '--font-size-4xl': '3rem',
  '--font-line-height-tight': '1',
  '--font-line-height-normal': '1.2',
  '--font-weight-baseline': '500',
  '--font-weight-emphasized': '800',
  '--size-xxs': '0.25rem',
  '--size-xxs-1': '0.375rem',
  '--size-xs': '0.5rem',
  '--size-xs-1': '0.625rem',
  '--size-sm': '0.75rem',
  '--size-md': '1rem',
  '--size-md-1': '1.125rem',
  '--size-lg': '1.25rem',
  '--size-xl': '1.5rem',
  '--size-2xl': '2rem',
  '--size-2xl-1': '2.25rem',
  '--size-3xl-2': '2.5rem',
  '--size-3xl-3': '2.75rem',
  '--size-3xl': '3rem',
  '--size-4xl': '4rem',
  '--size-5xl': '5rem',
  '--border-width-none': '0px',
  '--border-width-default': '1px',
  '--border-width-heavy': '2px',
  '--icon-size-xxs': '0.875rem',
  '--icon-size-xs': '1rem',
  '--icon-size-sm': '1.25rem',
  '--icon-size-md': '1.5rem',
  '--color-primary': '#00b2ff',
  '--color-primary-border': '#00b2ff',
  '--color-primary-focus': '#0099e1',
  '--color-on-primary': '#0a1a2a',
  '--color-secondary': '#f6f9ff',
  '--color-secondary-border': '#b3d6f7',
  '--color-secondary-focus': '#f6f9ff',
  '--color-on-secondary': '#1e2a38',
  '--color-tertiary': 'linear-gradient(180deg, #1CB2C2 0%, #2FD8B0 100%)',
  '--color-tertiary-border': 'linear-gradient(180deg, #36E2C9 0%, #53FFD1 100%)',
  '--color-tertiary-focus': 'linear-gradient(180deg, #1CB2C2 0%, #2FD8B0 100%)',
  '--color-on-tertiary': '#ffffff',
  '--color-error': '#d13a3a',
  '--color-error-container': '#ffd6d6',
  '--color-success': '#2fd082',
  '--color-on-success': '#ffffff',
  '--color-background': '#e7f3f8',
  '--color-transparent': 'rgba(255, 255, 255, 0)',
  '--color-surface-100': '#f7faff',
  '--color-surface-200': '#d6e4f7',
  '--color-surface-300': '#cbe0f7',
  '--color-surface-400': '#b8d1eb',
  '--color-surface-500': '#97b1c9',
  '--color-surface-600': '#88a1ba',
  '--color-surface-700': '#738eaa',
  '--color-surface-800': '#507b96',
  '--color-surface-900': '#2f4e6b',
  '--color-surface-1000': '#1c2e38',
  '--typography-label-xs': 'var(--font-weight-emphasized) var(--font-size-xs)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-label-sm': 'var(--font-weight-emphasized) var(--font-size-sm)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-label-md': 'var(--font-weight-emphasized) var(--font-size-md)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-body-baseline-xs': 'var(--font-weight-baseline) var(--font-size-xs)/var(--font-line-height-normal) var(--font-family-secondary)',
  '--typography-body-baseline-sm': 'var(--font-weight-baseline) var(--font-size-sm)/var(--font-line-height-normal) var(--font-family-secondary)',
  '--typography-body-baseline-md': 'var(--font-weight-baseline) var(--font-size-md)/var(--font-line-height-normal) var(--font-family-secondary)',
  '--typography-headline-xs': 'var(--font-weight-emphasized) var(--font-size-lg)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-headline-sm': 'var(--font-weight-emphasized) var(--font-size-0xl)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-headline-md': 'var(--font-weight-emphasized) var(--font-size-2xl)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-headline-lg': 'var(--font-weight-emphasized) var(--font-size-3xl)/var(--font-line-height-tight) var(--font-family-primary)',
  '--typography-headline-xl': 'var(--font-weight-emphasized) var(--font-size-4xl)/var(--font-line-height-tight) var(--font-family-primary)',
  '--elevation-level-0': '0 0 0 0 var(--color-transparent)',
  '--elevation-level-1': '0 4px 4px 0 rgba(247, 250, 255, 0.2), 0 2px 16px 0 rgba(214, 228, 247, 0.1)',
  '--color-fade': 'linear-gradient(180deg, rgba( var(--color-background) ,0) 0%, var(--color-background) 100%)',
  '--color-scrim': 'rgba( var(--color-background) ,0.6)'
}

export const styleDictionaryVarsDark = {
  ...styleDictionaryVars,
  '--color-primary': '#24ccff',
  '--color-primary-border': '#24ccff',
  '--color-primary-focus': '#00b2e1',
  '--color-on-primary': '#0a1a2a',
  '--color-secondary': 'linear-gradient(180deg, #1A2C39 0%, #1B3539 100%)',
  '--color-secondary-focus': 'linear-gradient(180deg, #1A2C39 40%, #1B3539 100%)',
  '--color-secondary-border': 'linear-gradient(180deg, #1B3337 0%, #1B4045 100%)',
  '--color-on-secondary': '#ffffff',
  '--color-tertiary': 'linear-gradient(180deg, #1CB2C2 0%, #2FD8B0 100%)',
  '--color-tertiary-border': 'linear-gradient(180deg, #36E2C9 0%, #53FFD1 100%)',
  '--color-tertiary-focus': 'linear-gradient(180deg, #36E2C9 0%, #53FFD1 100%)',
  '--color-on-tertiary': '#ffffff',
  '--color-error': '#d13a3a',
  '--color-error-container': '#ffd6d6',
  '--color-success': '#2fd082',
  '--color-on-success': '#ffffff',
  '--color-background': '#13232b',
  '--color-transparent': 'rgba(0, 0, 0, 0)',
  '--color-surface-100': '#181f23',
  '--color-surface-200': '#1d2d3f',
  '--color-surface-300': '#28324a',
  '--color-surface-400': '#323a45',
  '--color-surface-500': '#3b4e5e',
  '--color-surface-600': '#465a69',
  '--color-surface-700': '#687a8c',
  '--color-surface-800': '#93a9b7',
  '--color-surface-900': '#c0d0e9',
  '--color-surface-1000': '#f0f8ff',
  '--color-fade': 'linear-gradient(180deg, rgba( var(--color-background) ,0) 0%, var(--color-background) 100%)',
  '--color-scrim': 'rgba( var(--color-background) ,0.6)'
}
