{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
    "baseUrl": "./src",
    "plugins": [
      {
        "name": "next"
      },
      {
        "name": "typescript-plugin-css-modules",
        "options": {
          "classnameTransform": "camelCaseOnly"
        }
      }
    ],
    "paths": {
      "@core/*": [
        "../../core/src/*"
      ],
      "@components/*": [
        "./components/*",
        "../../core/src/components/*"
      ],
      "@constants/*": [
        "./constants/*",
      ],
      "@modules/*": [
        "./modules/*",
        "../../core/src/modules/*"
      ],
      "@screens/*": [
        "./components/@screens/*",
        "../../core/src/components/@screens/*"
      ],
      "@modals/*": [
        "./components/@modals/*",
        "../../core/src/components/@modals/*"
      ],
      "@widgets/*": [
        "./components/@widgets/*",
        "../../core/src/components/@widgets/*"
      ],
      "@app/*": [
        "./app/*",
        "../../core/src/app/*"
      ],
      "@theme/*": [
        "./theme/*",
        "../../core/src/theme/*"
      ],
      "@/*": [
        "./*",
        "../../core/src/*"
      ],
    },
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    "next.config.js",
    ".next/types/**/*.ts",
    "../core/src/**/*.ts",
    "../core/src/**/*.tsx",
    ".next/types/**/*.ts",
  ],
  "exclude": [
    "node_modules",
    "../core/src/**/*.test.*",
    "../core/src/**/*.stories.*",
    "../core/src/**/*.test.*",
  ]
}