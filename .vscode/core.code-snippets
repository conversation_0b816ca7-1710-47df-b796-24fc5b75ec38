{
  "Use theme variables": {
    "prefix": "use-theme-vars",
    "body": ["@use '@theme/variables' as *;"],
    "description": "Load theme variables in the current file",
  },
  "Forward theme variables": {
    "prefix": "fwd-theme-vars",
    "body": ["@forward '@theme/variables';"],
    "description": "Expose theme variables in to any file that imports the exposing file",
  },
  "Reexport * from core": {
    "prefix": "reexp-from-core",
    "body": ["export * from '@core/${TM_FILEPATH/^.*apps\\/[^\\/]*\\/src\\/(.*)\\.[^\\/]+$/$1/}'"],
    "description": "Reexport * from the relative core file",
  },
  "Reexport default from core": {
    "prefix": "reexp-def-from-core",
    "body": ["export { default } from '@core/${TM_FILEPATH/^.*apps\\/[^\\/]*\\/src\\/(.*)\\.[^\\/]+$/$1/}'"],
    "description": "Reexport default from the relative core file",
  },
  "Reexport all from core": {
    "prefix": "reexp-all-from-core",
    "body": [
      "export * from '@core/${TM_FILEPATH/^.*apps\\/[^\\/]*\\/src\\/(.*)\\.[^\\/]+$/$1/}'",
      "export { default } from '@core/${TM_FILEPATH/^.*apps\\/[^\\/]*\\/src\\/(.*)\\.[^\\/]+$/$1/}'",
    ],
    "description": "Reexport default from the relative core file",
  },
  "import 'server-only'": {
    "prefix": "iso",
    "body": ["import 'server-only'"],
    "description": "import 'server-only'",
  },
}
