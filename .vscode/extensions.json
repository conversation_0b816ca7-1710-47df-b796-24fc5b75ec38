{"recommendations": ["stylelint.vscode-stylelint", "vunguyentuan.vscode-css-variables", "bradlc.vscode-tailwindcss", "viijay-kr.react-ts-css", "clinyong.vscode-css-modules", "htmllessonsru.red-group-dynamic-next", "dbaeumer.vscode-eslint", "christian-kohler.path-intellisense", "esbenp.prettier-vscode", "mrmlnc.vscode-scss", "gruntfuggly.todo-tree", "christian-kohler.npm-intellisense", "syler.sass-indented", "ctf0.duplicated-code-new"]}