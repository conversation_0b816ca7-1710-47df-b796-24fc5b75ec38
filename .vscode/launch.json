{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run Dev Core",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
            "runtimeArgs": [
                "--inspect"
            ],
            "skipFiles": [
                "<node_internals>/**"
            ],
            "cwd": "${workspaceFolder}/apps/core",
            "serverReadyAction": {
                "action": "openExternally",
                "killOnServerStop": true,
                "pattern": "- Local:.+(https?://.+)",
                "uriFormat": "%s",
            }
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn dev",
            "name": "Run Dev",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn dev:staging",
            "name": "Run Staging",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn dev:staging:core",
            "name": "Run Staging Core",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn dev:luckyone",
            "name": "Run Dev LuckyOne",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn dev:dreamspins",
            "name": "Run Dev Dreamspins",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn dev:msw",
            "name": "Run Dev with MSW",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn api:start",
            "name": "Start Mock API",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn build",
            "name": "Run Build",
        },
        {
            "type": "node-terminal",
            "request": "launch",
            "command": "yarn start:core",
            "name": "Start Core Production",
        },
    ]
}