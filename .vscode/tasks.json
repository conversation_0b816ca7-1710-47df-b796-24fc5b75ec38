{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Reexport core styles",
            "type": "shell",
            "command": "node",
            "args": [
                "./scripts/reexport-core-styles.mjs",
                "--sourceFile=${file}",
                "--targetProject=${input:targetProject}"
            ],
            "problemMatcher": [],
            "presentation": {
                "echo": true,
                "reveal": "always"
            }
        },
        {
            "label": "Reexport core file",
            "type": "shell",
            "command": "node",
            "args": [
                "./scripts/reexport-core-file.mjs",
                "--sourceFile=${file}"
            ],
            "problemMatcher": [],
            "presentation": {
                "echo": true,
                "reveal": "always"
            }
        },
        {
            "label": "Create Component",
            "type": "shell",
            "command": "node",
            "args": [
                "./scripts/create-component.mjs"
            ],
            "problemMatcher": [],
            "group": "build",
            "presentation": {
                "reveal": "always"
            }
        }
    ],
    "inputs": [
        {
            "id": "targetProject",
            "type": "pickString",
            "options": [
                "luckyone",
                "dreamspins"
            ], // NOTE: Add new project directory whenever a new project is created
            "description": "Enter target project name"
        }
    ]
}