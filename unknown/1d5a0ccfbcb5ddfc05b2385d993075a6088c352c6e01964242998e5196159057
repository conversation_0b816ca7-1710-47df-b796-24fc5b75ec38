import React from 'react'
import Link from 'next/link'
import { getAppRouter } from '@/services/router.service'
import { Button } from '@components/Button'
import { Logo } from '@components/Logo/Logo'
import { PageContainer } from '@modules/page'
import styles from '@screens/404/404Screen.module.scss'

export const NotFoundScreen = () => {
  const appRouter = getAppRouter()

  return (
    <PageContainer>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.logo}>
            <Logo width={340} height={80} />
          </div>
          <div className={styles.illustrationContainer}>
            <h1>404</h1>
          </div>

          {/* Text Content */}
          <div className={styles.textContent}>
            <h1 className={styles.title}>Page Not Found</h1>
            <p className={styles.description}>
              Sorry, we couldn&apos;t find the page you&apos;re looking for. The page might have been removed, had its
              name changed, or is temporarily unavailable.
            </p>
          </div>

          {/* Action Buttons */}
          <div className={styles.actions}>
            <Button as={Link} href={appRouter.home} color="primary" size="lg" label="Go to Homepage" />
            <Button as={Link} href={appRouter.casino} color="secondary" size="lg" label="Browse Casino" />
          </div>

          {/* Quick Links */}
          <div className={styles.quickLinks}>
            <p className={styles.quickLinksTitle}>Quick Links:</p>
            <div className={styles.linksList}>
              <Link href={appRouter.casino} className={styles.quickLink}>
                Casino Games
              </Link>
              <Link href={appRouter.liveCasino} className={styles.quickLink}>
                Live Casino
              </Link>
              <Link href="/promotions" className={styles.quickLink}>
                Promotions
              </Link>
              <Link href="/contact" className={styles.quickLink}>
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  )
}
