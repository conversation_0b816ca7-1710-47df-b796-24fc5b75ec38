@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container header {
  max-width: unset;
  height: $header-height;
  overflow-x: auto;
  padding: 0;
}

.navbarBrand {
  width: var(--sidebar-width);
  flex: 0;
  flex-basis: unset;
  height: inherit;
  padding: calculate-rem(5px) 0 calculate-rem(5px) calculate-rem(8px);

  @media (max-width: $breakpoint-mobile) {
    width: 100%;
    padding: calculate-rem(6px) calculate-rem(5px);
  }
}

.navbarBrandInner {
  padding: 0 calculate-rem(10px);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-radius: calculate-rem(8px);
  background: linear-gradient(110deg, $color-primary, $color-background);
  height: 100%;
  width: 100%;
}

.navbarContent {
  padding: 0 calculate-rem(15px);
}

.nav {
  justify-content: space-between;
  align-items: center;
  padding: 0 calculate-rem(20px);
  height: 100%;
}
.navLink {
  padding: 0 calculate-rem(20px);
}

.logoLink {
  width: calculate-rem(140px);
  position: relative;
  height: 100%;
}
