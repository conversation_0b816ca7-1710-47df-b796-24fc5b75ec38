import { CustomWorkspaceOption, Workspace, WORKSPACE_OPTIONS_TITLES } from './workspace-configs'

export const WORKSPACE_TYPE = {
  name: 'Workspace',
  defaultValue: CustomWorkspaceOption.All,
  toolbar: {
    dynamicTitle: true,
    items: [
      { value: CustomWorkspaceOption.All, title: '🌎 ' + WORKSPACE_OPTIONS_TITLES[CustomWorkspaceOption.All] },
      { value: Workspace.Core, title: '⬜ ' + WORKSPACE_OPTIONS_TITLES[Workspace.Core] },
      { value: Workspace.LuckyOne, title: '🟡 ' + WORKSPACE_OPTIONS_TITLES[Workspace.LuckyOne] },
      { value: Workspace.Dreamspins, title: '🟦 ' + WORKSPACE_OPTIONS_TITLES[Workspace.Dreamspins] },
    ],
  },
}

export const THEME_TYPE = {
  name: 'Theme',
  defaultValue: 'light',
  toolbar: {
    dynamicTitle: true,
    items: [
      { value: 'light', title: '🌙 ' + 'light' },
      { value: 'dark', title: '☀️ ' + 'dark' },
    ],
  },
}
