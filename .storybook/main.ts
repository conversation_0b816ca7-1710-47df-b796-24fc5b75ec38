import type { StorybookConfig } from '@storybook/nextjs'
import type { WebpackConfiguration } from '@storybook/core-webpack'
import path, { join, dirname } from 'path'

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, 'package.json')))
}
const config: StorybookConfig = {
  addons: [getAbsolutePath('@storybook/addon-onboarding')],
  framework: {
    name: getAbsolutePath('@storybook/nextjs'),
    options: {},
  },
  stories: [
    '../apps/core/src/components/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../apps/core/src/modules/**/*.stories.@(js|jsx|ts|tsx|mdx)',
  ],
  features: {
    experimentalRSC: true,
  },
  webpackFinal: async (config: WebpackConfiguration) => {
    config.resolve = config.resolve || {}

    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@widgets': path.resolve(__dirname, '../apps/core/src/components/@widgets'),
      '@components': path.resolve(__dirname, '../apps/core/src/components'),
      '@constants': path.resolve(__dirname, '../apps/core/src/constants'),
      '@modules': path.resolve(__dirname, '../apps/core/src/modules'),
      '@theme': path.resolve(__dirname, '../apps/core/src/theme'),
      '@app': path.resolve(__dirname, '../apps/core/src/app'),
      'server-only': path.resolve(__dirname, './mocks/server-only.ts'),
      'next/navigation': path.resolve(__dirname, './mocks/next-navigation.ts'),
      '@/': path.resolve(__dirname, '../apps/core/src/'),
    }
    return config
  },
}
export default config
