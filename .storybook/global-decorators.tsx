import type { Decorator } from '@storybook/nextjs'
import ReduxStoreProvider from '@/store/ReduxStoreProvider'
import { SidebarProvider } from '@repo/ui/shadcn/sidebar'
import { LocaleProvider } from '@/context/locale/LocaleProvider'

export const PROVIDERS_DECORATOR: Decorator = Story => {
  return (
    <ReduxStoreProvider>
      <LocaleProvider locale="en">
        <SidebarProvider>
          <Story />
        </SidebarProvider>
      </LocaleProvider>
    </ReduxStoreProvider>
  )
}
