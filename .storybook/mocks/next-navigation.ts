// Mock for Next.js useRouter hook for Storybook
export const useRouter = () => ({
  push: (url: string) => {
    console.log('🚀 Router.push called with:', url)
  },
  replace: (url: string) => {
    console.log('🔄 Router.replace called with:', url)
  },
  back: () => {
    console.log('⬅️ Router.back called')
  },
  forward: () => {
    console.log('➡️ Router.forward called')
  },
  refresh: () => {
    console.log('🔃 Router.refresh called')
  },
  prefetch: (url: string) => {
    console.log('⚡ Router.prefetch called with:', url)
  },
  pathname: '/mocked-pathname',
  query: {},
  asPath: '/mocked-pathname',
  route: '/mocked-pathname',
  isReady: true,
  events: {
    on: (event: string, handler: () => void) => {
      console.log('📡 Router.events.on called with event:', event)
    },
    off: (event: string, handler: () => void) => {
      console.log('📡 Router.events.off called with event:', event)
    },
    emit: (event: string) => {
      console.log('📡 Router.events.emit called with event:', event)
    },
  },
})

export const usePathname = () => {
  console.log('📍 usePathname called, returning: /mocked-pathname')
  return '/mocked-pathname'
}

export const useSearchParams = () => {
  console.log('🔍 useSearchParams called')
  return new URLSearchParams()
}

export const useParams = () => {
  console.log('📋 useParams called')
  return {}
}

export default {
  useRouter,
  usePathname,
  useSearchParams,
  useParams,
}
