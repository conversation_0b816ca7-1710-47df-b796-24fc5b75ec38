import '@theme/style-dictionary.css'
import '@app/globals.css'
import '@app/globals.scss'
import '@theme/_variables.scss'
import type { Preview, Decorator } from '@storybook/nextjs'
import '../apps/luckyone/.storybook/fonts.css' // Import LuckyOne local fonts
import { CustomWorkspaceOption, Workspace, WORKSPACE_OPTIONS_TITLES } from './workspace-configs'
// NOTE: Add new project directory whenever a new project is created
import {
  styleDictionaryVars as coreStyleDictionaryVars,
  styleDictionaryVarsDark as coreStyleDictionaryVarsDark,
} from './../apps/core/src/theme/style-dictionary'
import {
  styleDictionaryVars as luckyoneStyleDictionaryVars,
  styleDictionaryVarsDark as luckyoneStyleDictionaryVarsDark,
} from './../apps/luckyone/src/theme/style-dictionary'
import {
  styleDictionaryVars as dreamspinsStyleDictionaryVars,
  styleDictionaryVarsDark as dreamspinsStyleDictionaryVarsDark,
} from './../apps/dreamspins/src/theme/style-dictionary'
import {
  PrimaryFont as CorePrimaryFont,
  SecondaryFont as CoreSecondaryFont,
} from './../apps/core/src/app/layout.settings'
import {
  PrimaryFont as LuckyOnePrimaryFont,
  SecondaryFont as LuckyOneSecondaryFont,
} from './../apps/luckyone/src/app/layout.settings.storybook'
import {
  PrimaryFont as DreamspinsPrimaryFont,
  SecondaryFont as DreamspinsSecondaryFont,
} from './../apps/dreamspins/src/app/layout.settings'
import { WORKSPACE_TYPE, THEME_TYPE } from './global-types'
import { PROVIDERS_DECORATOR } from './global-decorators'

// Helper function to get style dictionary variables based on workspace and theme
const getStyleDictionaryVars = (workspace: string, theme: string) => {
  const isLight = theme === 'light'

  switch (workspace) {
    case 'core':
    case Workspace.Core:
      return isLight ? coreStyleDictionaryVars : coreStyleDictionaryVarsDark
    case 'luckyone':
    case Workspace.LuckyOne:
      return isLight ? luckyoneStyleDictionaryVars : luckyoneStyleDictionaryVarsDark
    case 'dreamspins':
    case Workspace.Dreamspins:
      return isLight ? dreamspinsStyleDictionaryVars : dreamspinsStyleDictionaryVarsDark
    default:
      return isLight ? coreStyleDictionaryVars : coreStyleDictionaryVarsDark
  }
}

export const globalTypes = {
  workspace: WORKSPACE_TYPE,
  theme: THEME_TYPE,
}

const getFontClasses = (workspace: string) => {
  switch (workspace) {
    case Workspace.LuckyOne:
      return `${LuckyOnePrimaryFont.className} ${LuckyOneSecondaryFont.className}`
    case Workspace.Dreamspins:
      return `${DreamspinsPrimaryFont.className} ${DreamspinsSecondaryFont.className}`
    case Workspace.Core:
    default:
      return `${CorePrimaryFont.className} ${CoreSecondaryFont.className}`
  }
}

export const decorators: Decorator[] = [
  (Story, context) => {
    const theme = context.globals.theme
    document.documentElement.classList.remove('dark', 'light')
    document.documentElement.classList.add(theme)

    const workspace = context.globals.workspace

    if (workspace === CustomWorkspaceOption.All) {
      return (
        <div>
          {[Workspace.Core, Workspace.LuckyOne, Workspace.Dreamspins].map(ws => (
            <div className={getFontClasses(ws)} style={getStyleDictionaryVars(ws, theme) as React.CSSProperties}>
              <h2>{ws}</h2>
              <Story />
            </div>
          ))}
        </div>
      )
    }

    return (
      <div
        className={getFontClasses(workspace)}
        style={getStyleDictionaryVars(workspace, theme) as React.CSSProperties}>
        <Story />
      </div>
    )
  },
  PROVIDERS_DECORATOR,
]

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
}

export default preview
