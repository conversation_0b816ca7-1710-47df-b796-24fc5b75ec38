export const jiraTicketRegex = /^REN-[0-9]+ (Feat|Fix|Release): /s // e.g. would match "REN-123 Feat: Add new feature"
export const branchToPrefixRegex = /^(\w+)\/((REN-[\d]+)|(\d+\.\d+\.\d+))/ // e.g. would match "feature/REN-123" or "release/1.2.3"
export const mergeIntoPatternRegex = /^Merge branch/ // e.g. Merge branch 'master' into release/2.16.0

export const jiraTicketPattern = jiraTicketRegex.source
