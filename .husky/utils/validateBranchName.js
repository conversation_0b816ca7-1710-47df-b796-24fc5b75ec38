import chalk from 'chalk'

const branchNamePattern = new RegExp('((feat|fix|hotfix|docs|style|refactor|test)/REN-[0-9]|release/\\d+.\\d+.\\d+)')

function prettyPrint(name, errors) {
  const errorMsg = errors.map(e => `\n  ⭕  ${chalk.yellow(e)}`).join('')
  return chalk.gray(`Branch name "${chalk.blue.bold(name)}":${errorMsg}`)
}

function validateBranch(name) {
  const errors = []

  if (!branchNamePattern.test(name) && !/^Merge /.test(name)) {
    const message = "Your branch doesn't match the requirements:".concat(
      '\n\t👉 feat/REN-000-branch-name (when introducing a new feature)',
      '\n\t👉 fix/REN-000-branch-name (when fixing a bug)',
      '\n\t👉 release/0.0.0 (when doing a release)',
      '\n\t👉 docs/REN-000-branch-name (when writing docs)',
      '\n\t👉 style/REN-000-branch-name (when improving UI/Cosmetic)',
      '\n\t👉 refactor/REN-000-branch-name (when refactoring code)',
      '\n\t👉 test/REN-000-branch-name (when adding unit tests)',
      '\n\t👉 chore/REN-000-branch-name (when improving performance)',
    )
    errors.push(message)
  }

  return errors.length ? [prettyPrint(name, errors)] : null
}

export function validateBranchName(name) {
  console.log('Validating branch name...')
  return validateBranch(name)
}
