import chalk from 'chalk'
import fs from 'fs'
import { jiraTicketPattern, branchToPrefixRegex, mergeIntoPatternRegex } from './jiraTicketRegex.js'

const prefixRegex = new RegExp(`${jiraTicketPattern}`) // Matches Jira ticket + First letter, i.e "CD-29 A"
const prefixReplacement = new RegExp(`${jiraTicketPattern}`)

function prettyPrint(message, errors) {
  const errorMsg = errors.map(e => `\n  ⭕  ${chalk.yellow(e)}`).join('')
  return chalk.gray(`Commit message "${chalk.blue.bold(message)}":${errorMsg}`)
}

function standardizeCommitMessage(msg, branchName, originalCommitMsg) {
  const branchNameParts = branchName.match(branchToPrefixRegex)
  const type = branchNameParts[1].charAt(0).toUpperCase() + branchNameParts[1].slice(1) // Feat | Release | Fix
  const ticket = branchNameParts[2] // REN-000 | 0.0.0

  if (type !== 'Release') {
    msg = `${ticket} ${type}: ` + msg
    const gitParamsPath = process.env.HUSKY_GIT_PARAMS || '.git/COMMIT_EDITMSG'
    fs.writeFileSync(gitParamsPath, msg, { encoding: 'utf-8' })
    return msg
  }

  return originalCommitMsg
}

function validateMessage(msg, branchName) {
  const errors = []

  let msgText = msg.replace(prefixReplacement, '') // strip out REN-000 Feat: then will be appended standard
  msgText = msgText.charAt(0).toUpperCase() + msgText.slice(1)

  msg = standardizeCommitMessage(msgText, branchName, msg)

  if (!prefixRegex.test(msg) && !mergeIntoPatternRegex.test(msg)) {
    errors.push('Must start with a JIRA ticket prefix template [REN-000 (Feat|Fix|Release):] or "Merge branch ..."')
  }

  /* if (msgText.length < 20) {
    errors.push('is too short, please be descriptive');
  }*/

  if (msgText.length > 150 && !msg.includes('(cherry picked from commit')) {
    errors.push('is too long. Use the body to explain what and why vs. how.')
  }

  if (/(^\s)|(\s$)/.test(msg)) {
    errors.push('must not contain leading or trailing spaces')
  }

  if (msg.includes('  ')) {
    errors.push('must not contain multiple spaces')
  }

  const lastChar = msg.slice(-1)
  if ([':', ';'].indexOf(lastChar) !== -1) {
    errors.push(`ends with '${lastChar}'. This is unnecessary`)
  }

  if (!/^[A-Z0-9\[]/.test(msgText)) {
    errors.push('Commit message must start with a capital letter or digit')
  }

  console.log(chalk.gray(`Commit message "${chalk.blue.bold(msg)}"`))

  return errors.length ? prettyPrint(msg, errors) : errors
}

export function validateCommitMessages(messages, branchName) {
  console.log(`Validating ${messages.length} commit message(s)...`)

  const errors = messages.reduce((accErrors, message) => accErrors.concat(validateMessage(message, branchName)), [])

  if (errors.length) {
    errors.push(
      `Please read the commit message guidelines:\n${chalk.blue.bold('👉 http://chris.beams.io/posts/git-commit/ 👈')}`,
    )
  }

  return errors
}
