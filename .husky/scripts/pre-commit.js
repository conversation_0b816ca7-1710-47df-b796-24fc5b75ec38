import chalk from 'chalk'
import { readFileSync } from 'fs'
import { validateCommitMessages } from '../utils/validateCommitMessages.js'
import { getCurrentBranchName } from '../utils/getCurrentBranchName.js'
import { validateBranchName } from '../utils/validateBranchName.js'

const message = readFileSync(process.env.HUSKY_GIT_PARAMS || '.git/COMMIT_EDITMSG', 'utf8')
  .split('\n')
  .filter(line => !line.startsWith('#') && line.length !== 0) // Remove comments from commit
  .shift()

let errors =
  validateBranchName(getCurrentBranchName()) || validateCommitMessages([message], getCurrentBranchName()) || []

if (errors.length) {
  errors.forEach(error => {
    console.log(error)
  })
  console.log(chalk.red.bold('❌ Commit aborted! Please fix the above errors.'))
  process.exit(1)
}

console.log(chalk.green('✅ Commit validation passed!'))
