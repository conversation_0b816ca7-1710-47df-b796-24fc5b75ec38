export type GetAppConfigGlobalResponse = {
  hallOfFame: {
    cardThumbnail: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
    nativeScreens: string[]
    notificationDuration: number
    notificationBackground: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
  }
  seoOrigins: {
    no: {
      domain: string
      debugSW: boolean
      isMultiLanguage: boolean
    }
    default: {
      domain: string
      isMultiLanguage: boolean
    }
    en_DIABLED: {
      domain: string
      uninstallSW: boolean
      isMultiLanguage: boolean
    }
    in_DISABLED: {
      domain: string
      debugSW: boolean
      isMultiLanguage: boolean
    }
  }
  imgIxConfig: Array<{
    imgIxHost: string
    imagesOrigin: string
  }>
  walletConfig: {
    bonus_fallback_image: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
  }
  dashboardCards: {
    bonus: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    raffle: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    cashback: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    promotion: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    splash_game: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
  }
  allowedLoginTags: string[]
  dynamicModalConfig: {
    dynamicModalPaths: {
      privacyPolicy: string
      termsAndConditions: string
    }
    titleTranslationKeys: {
      privacyPolicy: string
      termsAndConditions: string
    }
  }
  softCountryBlockMessage: {
    backgroundImage: string
  }
}

export type GetAppConfigResponse = {
  currency: {
    separators: {
      decimalSeparator: string
      thousandSeparator: string
    }
  }
  header_logos: string[]
  logos_config: {
    footerLogo: {
      name: string
      imgUrl: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    headerLogo: {
      name: string
      imgUrl: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    defaultFooterLogo: {
      name: string
      imgUrl: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    defaultHeaderLogo: {
      name: string
      imgUrl: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
  }
  smart_banner: {
    content: {
      ios: {
        title: string
        subTitle: string
        buttonLabel: string
      }
      android: {
        title: string
        subTitle: string
        buttonLabel: string
      }
    }
    brandIcon: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
    conditions: {
      maximumLoginsTarget: number
      minimumLoginsTarget: number
    }
    appDownloadLink: string
  }
  industry_list: Array<{
    label: string
    value: string
  }>
  limits_config: {
    showNewFlow: boolean
    limitOptions: {
      options: Array<{
        label: string
        value: string
      }>
      defaultOption: string
      lossLimitPeriods: {
        periodOptions: Array<{
          label: string
          value: string
        }>
      }
      wagerLimitPeriods: {
        defaultOption: string
        periodOptions: Array<{
          label: string
          value: string
        }>
      }
      depositLimitPeriods: {
        defaultOption: string
        periodOptions: Array<{
          label: string
          value: string
        }>
      }
      lossLimitDefaultAmounts: number[]
      wagerLimitDefaultAmounts: number[]
      depositLimitDefaultAmounts: number[]
    }
    pauseOptions: {
      options: Array<{
        label: string
        value: string
      }>
      defaultOption: string
      timeOutPeriods: {
        defaultOption: string
        periodOptions: Array<{
          unit: string
          label: string
          value: string
        }>
      }
      selfExclusionPeriods: {
        defaultOption: string
        periodOptions: Array<{
          unit: string
          label: string
          value: string
        }>
      }
    }
    minWagerLimit: number
    defaultAmounts: number[]
    minDepositLimit: number
    rgTimeoutPeriods: Array<{
      unit: string
      duration: string
    }>
    myAccountTimeoutPeriods: Array<{
      unit: string
      duration: string
    }>
    areExistingLimitsAvailable: boolean
  }
  wallet_config: {
    bonus_fallback_image: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
  }
  register_popup: {
    steps: Array<{
      interval: number
      intervalBefore: number
    }>
    imagePath: string
    excludedPaths: string[]
  }
  cashback_config: {
    card_background: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
  }
  dashboard_cards: {
    bonus: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    raffle: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    cashback: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    promotion: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
    splash_game: {
      card_background_image: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      card_thumbnail_fallback: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
    }
  }
  no_index_routes: string[]
  brite_pnp_config: {
    amountButtons: number[]
    defaultAmount: number
    depositRoundUp: {
      _49: number
      _99: number
      _999: number
      default: number
    }
  }
  occupations_list: {
    'Other Occupations': Array<{
      label: string
      value: string
    }>
    'Services Occupations': Array<{
      label: string
      value: string
    }>
    'Transportation Occupations': Array<{
      label: string
      value: string
    }>
    'Healthcare Support Occupations': Array<{
      label: string
      value: string
    }>
    'Other Professional Occupations': Array<{
      label: string
      value: string
    }>
    'Architecture and Engineering Occupations': Array<{
      label: string
      value: string
    }>
    'Education, Training, and Library Occupations': Array<{
      label: string
      value: string
    }>
    'Office and Administrative Support Occupations': Array<{
      label: string
      value: string
    }>
    'Healthcare Practitioners and Technical Occupations': Array<{
      label: string
      value: string
    }>
    'Agriculture, Maintenance, Repair, and Skilled Crafts': Array<{
      label: string
      value: string
    }>
    'Business, Executive, Management, and Financial Occupations': Array<{
      label: string
      value: string
    }>
  }
  general_tmb_badge: {
    tag: string
    watermark: string
  }
  live_spins_config: {
    liveSpinsSlug: string
    casinoCategoryId: number
    liveSpinsSdkConfig: {
      tenant: string
      language: string
      serverConfig: {
        ui: string
        api: string
      }
    }
    liveSpinsCategoryId: number
    liveCasinoCategoryId: number
  }
  pause_ticker_item: number
  registration_form: {
    registerStepOne: {
      id: string
      name: string
      fields: Array<{
        id: string
        name: string
        label: string
        fields: Array<{
          id: string
          name: string
          type: string
          label: string
          component: string
          placeholder: string
          validations: Array<{
            type: string
            params: number[]
          }>
          validationType: string
        }>
        classes: string
        component: string
      }>
      component: string
    }
    registerStepTwo: {
      id: string
      name: string
      fields: Array<{
        id: string
        name: string
        label: string
        fields: Array<{
          id: string
          name: string
          type: string
          label: string
          component: string
          optionsProp: string
          placeholder: string
          validations: Array<{
            type: string
            params: string[]
          }>
          validationType: string
        }>
        classes: string
        component: string
        validations: Array<{
          type: string
          params: any[]
        }>
        validationType: string
      }>
      component: string
    }
  }
  user_details_form: {
    fields: Array<{
      id: string
      name: string
      label: string
      fields: Array<{
        id: string
        name: string
        type: string
        label: string
        component: string
        placeholder: string
        validations: Array<{
          type: string
          params: string[]
        }>
        validationType: string
      }>
      classes: string
      component: string
    }>
  }
  lobby_games_config: {
    tags: {
      popularSearchTagId: number
      popularLiveSearchTagId: number
    }
    categories: Array<{
      categoryId: string
      gtmEventName: string
    }>
    lobbySections: {
      popularLiveGamesSectionId: number
    }
  }
  user_details_check: {
    count: string
    timeInterval: string
  }
  app_download_config: {
    icon: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
    limit: string
    button: {
      icon: {
        alt: string
        src: string
        width: string
        device: string
        height: string
        target: string
      }
      link: string
      type: string
      label: string
      fullWidth: boolean
    }
  }
  providers_tmb_badge: {
    tvbet: string
    mgs_mga: string
    hub88_ebet: string
    daily_drops: string
    hub88_ezugi: string
    mgs_liveg24: string
    pragmaticplay: string
    mgs_betgamestv: string
    mgs_microgaming: string
    hub88_asiagaming: string
    hub88_evolutiongaming: string
    hub88_lottoinstantwin: string
    hub88_superspadegames: string
  }
  traffic_attribution: {
    enabled: boolean
  }
  available_date_range: number
  bonus_image_fallback: {
    alt: string
    src: string
    width: string
    device: string
    height: string
    target: string
  }
  popular_game_studios: {
    0: {
      key: string
      name: string
    }
    1: {
      key: string
      name: string
    }
    2: {
      key: string
      name: string
    }
  }
  responsible_gambling: {
    pauses: {
      timeout: {
        options: Array<{
          unit: string
          value: number
        }>
        isDefault: boolean
      }
      selfExclusion: {
        options: Array<{
          unit: string
          value: number
          isDefault: boolean
        }>
        isDefault: boolean
      }
    }
    settings: {
      loss: {
        periods: {
          daily: {
            options: number[]
          }
          weekly: {
            options: number[]
            isDefault: boolean
          }
          monthly: {
            options: number[]
          }
        }
      }
      wager: {
        periods: {
          daily: {
            options: number[]
          }
          weekly: {
            options: number[]
            isDefault: boolean
          }
          monthly: {
            options: number[]
          }
        }
      }
      deposit: {
        periods: {
          daily: {
            options: number[]
          }
          weekly: {
            options: number[]
            isDefault: boolean
          }
          monthly: {
            options: number[]
            isDefault: boolean
          }
        }
        isDefault: boolean
      }
    }
    showNewFlow: boolean
    arePausesEnabled: boolean
    areSettingsEnabled: boolean
    areExistingLimitsAvailable: boolean
  }
  all_time_highs_config: {
    maxItems: number
    winMultiplier: string
    cardBackgroundImage: {
      alt: string
      src: string
      width: string
      device: string
      height: string
      target: string
    }
    placeholderCardLink: string
  }
  dashboard_games_config: {
    categories: Array<{
      categoryId: string
      gtmEventName: string
    }>
  }
  live_spins_category_id: number
  navigation_menu_mobile: {
    gamePageMenuIcons: string[]
    loggedOutMenuIcons: string[]
    navMenuWithButtons: {
      loggedOut: Array<{
        item: string
        isButton: boolean
        buttonType: string
      }>
    }
  }
  search_categories_tabs: {
    allGames: {
      show: boolean
    }
    features: {
      show: boolean
      excludedTags: number[]
    }
    liveGames: {
      show: boolean
    }
    gameStudios: {
      show: boolean
    }
  }
  show_search_categories: boolean
  market_modal_background: string
  cashier_disabled_by_tags: {
    tags: string[]
    enabled: boolean
  }
  brite_affiliate_pnp_config: {
    655278: {
      minimumAmount: number
    }
  }
  is_enabled_welcome_page_v2: boolean
  show_crash_games_menu_item: boolean
  cashier_currency_separators: {
    decimalSeparator: string
    thousandSeparator: string
  }
  limits_notification_time_out: number
  is_game_jackpot_ticker_enabled: boolean
  last_played_games_category_row: number
  lobby_low_balance_notification: {
    depositTargets: {
      depositSize: number
      showLessDeposits: number
      showMoreDeposits: number
    }
    averageDeposits: {
      eur: {
        promptOnAmount: Array<{
          promptOn: number
          averageDepositMax: number
          averageDepositMin: number
        }>
        promptOnPercentage: Array<{
          roundUpTo: number
          averageDepositMin: number
          promptOnPercentage: number
          averageDepositTarget: number
        }>
      }
      usd: {
        promptOnAmount: Array<{
          promptOn: number
          averageDepositMax: number
          averageDepositMin: number
        }>
        promptOnPercentage: Array<{
          roundUpTo: number
          averageDepositMin: number
          promptOnPercentage: number
          averageDepositTarget: number
        }>
      }
      default: {
        promptOnAmount: Array<{
          promptOn: number
          averageDepositMax: number
          averageDepositMin: number
        }>
        promptOnPercentage: Array<{
          roundUpTo: number
          averageDepositMin: number
          promptOnPercentage: number
          averageDepositTarget: number
        }>
      }
    }
    notificationDuration: number
  }
  post_register_offer_modal_config: {
    showPostRegisterOfferModal: boolean
  }
}
