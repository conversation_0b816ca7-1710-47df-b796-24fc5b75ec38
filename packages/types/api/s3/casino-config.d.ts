export type GetCasinoConfigResponse = {
  default: boolean
  showAllGames: boolean
  showLastPlayedGames: boolean
  id: number
  gameCategories: Array<{
    id: number
    name: string
  }>
  lastPlayedGamesCategories: Array<{
    id: number
    name: string
  }>
  menuItems: Array<{
    id: number
    name: string
    localisation: {
      name: string
    }
  }>
  sections: Array<{
    id: number
    name: string
    sectionCriteria: string
    criteriaIds: number[]
    isDisplayedInMenu: boolean
    desktopGamesLimit: number
    desktopRowCount: number
    desktopThumbnailLayout: string
    mobileGamesLimit: number
    mobileRowCount: number
    mobileThumbnailLayout: string
    inheritSortFromRoot: boolean
    showWatermark: boolean
    order: number
    localizedName: string
    games: number[]
  }>
}
