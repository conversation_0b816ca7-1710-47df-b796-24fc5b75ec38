export type Keyable<T = any> = {
  [key: string]: T
}

export type ReverseMap<T> = T[keyof T]

export type PartialRecord<K extends string | number | symbol, T> = {
  [P in K]?: T
}

export type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`
}[keyof ObjectType & (string | number)]

/**
 * Make some properties in T required
 */
export type Require<T, K extends keyof T> = T & { [P in K]-?: T[P] }
