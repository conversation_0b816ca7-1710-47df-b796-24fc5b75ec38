export const RHINOLAYER_COMPONENT_BLOCKS = {
  /** @deprecated used by deprecated StaticPage only */
  ROOT: 'StaticPageRoot',
  SEO_ROOT: 'SeoRoot',
  PROMOTION_ROOT: 'PromotionRoot',
  GAME_THUMBNAILS: 'GameThumbnails',
  FLOATING_BANNER: 'FloatingBanner',
  SEO_NAVIGATION: 'navigation',
  SEO_NAVIGATION_COMPONENT: 'navigationcomponent',
  FOOTER_CERTIFICATES: 'certificates',
  FOOTER_LINKS: 'links_section',
  FOOTER_SOCIAL_MEDIA: 'social_media',
  FOOTER_DISCLAIMER: 'disclaimer',
  FOOTER_TOP_SECTION: 'top_section',
  FOOTER_USEFUL_LINKS: 'useful_links',
  FOOTER_GENERAL_LINKS: 'general_links',
  FOOTER_PAYMENT_PROVIDERS: 'payment_providers',
  FOOTER_GAME_PROVIDERS: 'game_providers',
  FOOTER_LOGO_SECTION: 'review_site_logo',
  /** @deprecated used by deprecated StaticPage only */
  EDITOR_CONTAINER: 'EditorContainer',
  /** @deprecated used by deprecated StaticPage only */
  CONTAINER: 'Container',
  TEXT: 'Text',
  IMAGE: 'ImageBlock',
  TABLE: 'TableBlock',
  CTA: 'CTA',
  REFERENCE: 'Reference',
  REFERENCE_COMPONENT: 'ReferenceComponent',
  SHORTCUTS: 'SHORTCUTS',
  LAST_PLAYED: 'LAST_PLAYED',
  PROMOTIONS: 'PROMOTION',
  GAMES: 'GAMES',
  INSTANT_BONUS: 'INSTANT_BONUS',
  REWARD: 'REWARD',
  RAFFLES: 'RAFFLES',
  PRIZE: 'PRIZE',
  NOTIFICATIONS_AREA: 'NOTIFICATIONS_AREA',
  ALL_TIME_HIGHS: 'ALL_TIME_HIGHS',
}
/**
 * @deprecated
 * Components used as Reference components in the StaticPages
 */
export const REF_COMPONENTS_MAPPER = {
  PROMOTION_TERMS: 'TermsAndConditions',
  TERMS_PLAIN: 'TermsAndConditionsPlain',
  FLOATING_BAR: 'FloatingBar',
  BANNER_COMPONENT: 'BannerComponent',
  GAME_THUMBNAILS: 'GameThumbnails',
  SLOTS_SCROLL_TO_LIST: 'SlotsScrollToList',
  SEO_GAMES_LIST: 'SEOGamesList',
  RG_TIMEOUT_SETTINGS: 'RGTimeoutSettings',
  RG_SELF_EXCLUSION: 'RGSelfExclusion',
  SEO_GAME_HEADER: 'SEOGameHeader',
  PAYMENT_METHODS: 'PaymentMethods',
  SEO_OFFER_SECTION: 'SEOOfferSection',
  ACTIVE_RAFFLES: 'RaffleComponent',
}
/**
 * Components used as Reference components in the SeoPages
 */
export const REF_COMPONENTS_SEOPAGE_MAPPER = {
  TERMS_AND_CONDITIONS: 'TermsAndConditions',
  BANNER_COMPONENT: 'BannerComponent',
  GAME_THUMBNAILS: 'GameThumbnails',
  SLOTS_SCROLL_TO_LIST: 'SlotsScrollToList',
  SEO_GAMES_LIST: 'SEOGamesList',
  RG_TIMEOUT_SETTINGS: 'RGTimeoutSettings',
  RG_SELF_EXCLUSION: 'RGSelfExclusion',
  SEO_GAME_HEADER: 'SEOGameHeader',
  PAYMENT_METHODS: 'PaymentMethods',
  SEO_OFFER_SECTION: 'SEOOfferSection',
  ACTIVE_RAFFLES: 'RaffleComponent',
}
/**
 * Const for targeting values
 */
export const RHINOLAYER_TARGET_VALUES = {
  LOGGED_IN: 'logged-in',
  LOGGED_OUT: 'logged-out',
  WEB_ONLY: 'web-only',
  BOTH: 'both',
  ALL: 'all',
}
export const RL_RESPONSE_STATUS = {
  SUCCESS: 'Success',
  ERROR: 'Error',
}
export const RL_LOG_LEVEL = {
  SILENT: 'silent',
  VERBOSE: 'verbose',
}
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  TERTIARY: 'tertiary',
}
export const BUTTON_MODES = {
  NORMAL: 'normal',
  FULL: 'full',
}
export const RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS = {
  VIP_SECTION: 'vip_section',
  INFO_SECTION: 'info_section',
  CTA_INFO_SECTION: 'cta_info_section',
  HOW_TO_SECTION: 'how_to_section',
  BANNER_SECTION: 'banner_section',
  PROMOTION_BANNER_SECTION: 'promotion_banner_section',
  BANNER_SECTION_BOTTOM: 'banner_section_bottom',
  BACCARAT_SECTION: 'baccarat_section',
  OFFERS_SECTION: 'offers_section',
  BANNER_SECOND: 'banner_second',
  BANNER_THIRD: 'banner_third',
  TABLE_OF_CONTENT: 'table_of_content',
  WHY_US: 'why_us',
  WELCOME_PACKAGE: 'welcome_package',
  EXCITING_EVERY_DAY: 'exciting_every_day',
  PAYMENT_SECTION: 'payment_section',
  HOW_TO_DEPOSIT: 'how_to_deposit',
  REGISTER_SECTION: 'register_section',
  GAMES_SECTION: 'games_section',
  LIVE_GAMES_SECTION: 'live_games_section',
  GAME_PROVIDERS: 'game_providers',
  BIG_WINNERS: 'big_winners',
  PACHINKO_SECTION: 'pachinko_section',
  PAYMENT_SECTION_METHODS: 'payment_section_methods',
  TESTIMONIAL_LANDER: 'testimonial_lander',
  TESTIMONIALS: 'testimonials',
  ONE_AND_ONLY_SECTION: 'one_and_only_section',
  HEADER_SECTION: 'header_section',
  GAME_PREVIEW: 'game_preview',
  VIDEO_SECTION: 'video_section',
  FAQ_SECTION: 'faq_section',
  SEO_SECTION: 'seo_section',
  SUPPORT_SECTION_COMPONENT: 'support_section_component',
  LEARN_HOW_TO_PLAY_SECTION_COMPONENT: 'learn_how_to_play_section_component',
  LANDER_PROMOTION_SECTION_COMPONENT: 'lander_promotion_section_component',
  LANDER_SEARCH_WRAPPER_SECTION: 'lander_search_section',
  BOOSTED_OFFERS_SECTION: 'boosted_offers_section',
  GAMES_SECTION_COMPONENT: 'games_section_component',
  BEGINNER_GUIDE: 'beginner_guide',
  APP_SECTION: 'app_section',
}
export const RHINOLAYER_PAYMENT_PROVIDERS = {
  PAYMENT_IQ: 'PIQ',
  PRAGMATIC: 'PRAGMATIC',
  PAYAGE: 'PAYAGE',
}
export const RHINOLAYER_SSE_EVENT = {
  OPEN: 'open',
  MESSAGE: 'message',
  ERROR: 'error',
  CLOSE: 'close',
  EXCEPTION: 'exception',
}
export const RHINOLAYER_SSE_UPDATES = {
  CURRENCY: 'currency',
}
export const RHINOLAYER_SSE_ENDPOINTS = {
  JackpotGames: '/jackpots',
}

export const RHINOLAYER_TARGET_KEY = 'authentication'
export const RHINOLAYER_DISPLAY_TAG_KEY = 'display'
export const RHINOLAYER_DISPLAY_TAG_VALUES = {
  WEB_ONLY: 'web-only',
  APP_ONLY: 'app-only',
  BOTH: 'both',
} as const

export enum DashboardResourceTypes {
  Shortcut = 'SHORTCUTS',
  Dynamic = 'DYNAMIC',
  LastPlayed = 'LAST_PLAYED',
  Reward = 'REWARD',
  Instant_Bonus = 'INSTANT_BONUS',
  GAMES = 'GAMES',
  Promotions = 'PROMOTION',
  Raffles = 'RAFFLES',
  Prize = 'PRIZE',
  AllTimeHighs = 'ALL_TIME_HIGHS',
  NotificationsArea = 'NOTIFICATIONS_AREA',
  HallOfFame = 'HALL_OF_FAME',
}

export enum DashboardNotificationComponents {
  PendingWithdrawals = 'PENDING_WITHDRAWALS_COMPONENT',
  Limits = 'LIMITS_COMPONENT',
  KYC = 'KYC_COMPONENT',
  CommunicationPreferences = 'SUBSCRIPTIONS_COMPONENT',
}

export enum RlNotificationUpdateEvents {
  PendingWithdrawalsUpdateEvent = 'PendingWithdrawalsUpdateEvent',
  RgLimitsUpdateEvent = 'RgLimitsUpdateEvent',
  KycVerificationUpdateEvent = 'KycVerificationUpdateEvent',
  CommsPreferencesUpdateEvent = 'CommsPreferencesUpdateEvent',
}
