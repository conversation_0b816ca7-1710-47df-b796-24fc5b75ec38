import { TimeUnitLabels } from '@repo/constants/common/sidebar'
import { calculateTimeDifference, formatTimeValueWithLeadingZero, getTimeUnitLabel } from './timeHelpers'

const TEST_START_TIME = new Date('2025-07-03T12:00:00Z')

describe('timeHelpers', () => {
  describe('calculateTimeDifference', () => {
    const mockCurrentTime = new Date('2025-07-03T12:00:00Z').getTime()

    beforeEach(() => {
      jest.spyOn(Date, 'now').mockReturnValue(mockCurrentTime)
      const OriginalDate = Date
      global.Date = jest.fn((dateString?: string | number | Date) => {
        if (dateString !== undefined) {
          return new OriginalDate(dateString as string | number)
        }
        return new OriginalDate(mockCurrentTime)
      }) as any
      global.Date.now = jest.fn(() => mockCurrentTime)
    })

    afterEach(() => {
      jest.restoreAllMocks()
    })

    it('should return all zeros when end time is in the past', () => {
      const pastDate = new Date('2025-07-02T12:00:00Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: pastDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      })
    })

    it('should calculate time correctly for future date with seconds only', () => {
      const futureDate = new Date('2025-07-03T12:00:30Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 30,
      })
    })

    it('should calculate time correctly for future date with minutes and seconds', () => {
      const futureDate = new Date('2025-07-03T12:05:45Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 0,
        minutes: 5,
        seconds: 45,
      })
    })

    it('should calculate time correctly for future date with hours, minutes and seconds', () => {
      const futureDate = new Date('2025-07-03T15:30:15Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 3,
        minutes: 30,
        seconds: 15,
      })
    })

    it('should calculate time correctly for future date with days, hours, minutes and seconds', () => {
      const futureDate = new Date('2025-07-05T14:30:45Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 2,
        hours: 2,
        minutes: 30,
        seconds: 45,
      })
    })

    it('should calculate time correctly for future date with months', () => {
      const futureDate = new Date('2025-09-15T12:00:00Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result.years).toBe(0)
      expect(result.months).toBeGreaterThanOrEqual(2)
      expect(result.days).toBeGreaterThanOrEqual(0)
    })

    it('should calculate time correctly for future date with years', () => {
      const futureDate = new Date('2027-07-03T12:00:00Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result.years).toBeGreaterThanOrEqual(1)
      expect(result.years).toBeLessThanOrEqual(2)
      expect(result.months).toBeGreaterThanOrEqual(0)
      expect(result.days).toBeGreaterThanOrEqual(0)
      expect(result.hours).toBe(0)
      expect(result.minutes).toBe(0)
      expect(result.seconds).toBe(0)
    })

    it('should calculate time correctly for complex future date with years, months, and days', () => {
      const futureDate = new Date('2026-10-18T18:30:45Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result.years).toBeGreaterThanOrEqual(1)
      expect(result.months).toBeGreaterThanOrEqual(0)
      expect(result.days).toBeGreaterThanOrEqual(0)
      expect(result.hours).toBe(6)
      expect(result.minutes).toBe(30)
      expect(result.seconds).toBe(45)
    })

    it('should handle exact time boundaries correctly', () => {
      const exactlyOneHour = new Date('2025-07-03T13:00:00Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: exactlyOneHour })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 1,
        minutes: 0,
        seconds: 0,
      })
    })

    it('should return consistent structure regardless of input', () => {
      const futureDate = new Date('2025-07-03T12:00:01Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: futureDate })

      expect(result).toHaveProperty('years')
      expect(result).toHaveProperty('months')
      expect(result).toHaveProperty('days')
      expect(result).toHaveProperty('hours')
      expect(result).toHaveProperty('minutes')
      expect(result).toHaveProperty('seconds')

      Object.values(result).forEach(value => {
        expect(typeof value).toBe('number')
        expect(value).toBeGreaterThanOrEqual(0)
      })
    })

    it('should return zeros for invalid Date objects', () => {
      const invalidDate = new Date('invalid-date-string')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: invalidDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      })
    })

    it('should throw if input is null', () => {
      expect(() => calculateTimeDifference({ startTime: null as any, endTime: new Date() })).toThrow()
    })

    it('should throw if input is undefined', () => {
      expect(() => calculateTimeDifference({ startTime: undefined as any, endTime: new Date() })).toThrow()
    })

    it('should throw if input is a string', () => {
      expect(() => calculateTimeDifference({ startTime: '2025-07-03T12:00:00Z' as any, endTime: new Date() })).toThrow()
    })

    it('should throw if input is a number', () => {
      expect(() => calculateTimeDifference({ startTime: 1234567890 as any, endTime: new Date() })).toThrow()
    })

    it('should throw if input is an empty object', () => {
      expect(() => calculateTimeDifference({ startTime: {} as any, endTime: new Date() })).toThrow()
    })

    it('should throw if input is an array', () => {
      expect(() => calculateTimeDifference({ startTime: [] as any, endTime: new Date() })).toThrow()
    })

    it('should handle very far future dates correctly', () => {
      const veryFarFuture = new Date('2100-01-01T00:00:00Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: veryFarFuture })

      expect(result.years).toBeGreaterThan(70)
      expect(typeof result.years).toBe('number')
      expect(result.months).toBeGreaterThanOrEqual(0)
      expect(result.days).toBeGreaterThanOrEqual(0)
      expect(result.hours).toBeGreaterThanOrEqual(0)
      expect(result.minutes).toBeGreaterThanOrEqual(0)
      expect(result.seconds).toBeGreaterThanOrEqual(0)
    })

    it('should handle dates very close to current time (milliseconds difference)', () => {
      const veryCloseDate = new Date(mockCurrentTime + 500) // 500ms in the future
      const result = calculateTimeDifference({ startTime: new Date(), endTime: veryCloseDate })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      })
    })

    it('should handle leap year calculations correctly', () => {
      const leapYearMockTime = new Date('2024-02-28T12:00:00Z').getTime()
      jest.spyOn(Date, 'now').mockReturnValue(leapYearMockTime)

      const OriginalDate = Date
      global.Date = jest.fn((dateString?: string | number | Date) => {
        if (dateString !== undefined) {
          return new OriginalDate(dateString as string | number)
        }
        return new OriginalDate(leapYearMockTime)
      }) as any
      global.Date.now = jest.fn(() => leapYearMockTime)

      const futureDate = new Date('2024-03-01T12:00:00Z')
      const result = calculateTimeDifference({ startTime: new Date('2024-02-28T12:00:00Z'), endTime: futureDate })

      expect(result.years).toBe(0)
      expect(result.months).toBe(0)
      expect(result.days).toBe(2)
      expect(result.hours).toBe(0)
      expect(result.minutes).toBe(0)
      expect(result.seconds).toBe(0)
    })

    it('should handle timezone differences correctly (UTC dates)', () => {
      const utcDate = new Date('2025-07-03T12:00:30Z')
      const result = calculateTimeDifference({ startTime: TEST_START_TIME, endTime: utcDate })

      expect(result.seconds).toBe(30)
      expect(result.minutes).toBe(0)
      expect(result.hours).toBe(0)
    })

    it('should return all zeros for dates exactly equal to current time', () => {
      const exactCurrentTime = new Date(mockCurrentTime)
      const result = calculateTimeDifference({ startTime: exactCurrentTime, endTime: exactCurrentTime })

      expect(result).toEqual({
        years: 0,
        months: 0,
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      })
    })
  })

  describe('formatTimeValueWithLeadingZero', () => {
    it('should add leading zero for single digit numbers', () => {
      expect(formatTimeValueWithLeadingZero(5)).toBe('05')
      expect(formatTimeValueWithLeadingZero(0)).toBe('00')
      expect(formatTimeValueWithLeadingZero(9)).toBe('09')
    })

    it('should not modify double digit numbers', () => {
      expect(formatTimeValueWithLeadingZero(10)).toBe('10')
      expect(formatTimeValueWithLeadingZero(23)).toBe('23')
      expect(formatTimeValueWithLeadingZero(99)).toBe('99')
    })

    it('should handle triple digit numbers correctly', () => {
      expect(formatTimeValueWithLeadingZero(100)).toBe('100')
      expect(formatTimeValueWithLeadingZero(999)).toBe('999')
    })

    it('should handle negative numbers correctly', () => {
      expect(formatTimeValueWithLeadingZero(-5)).toBe('-5')
      expect(formatTimeValueWithLeadingZero(-10)).toBe('-10')
    })

    it('should handle zero correctly', () => {
      expect(formatTimeValueWithLeadingZero(0)).toBe('00')
    })

    it('should handle decimal numbers by converting to string', () => {
      expect(formatTimeValueWithLeadingZero(5.5)).toBe('5.5')
      expect(formatTimeValueWithLeadingZero(0.1)).toBe('0.1')
    })

    it('should handle very large numbers correctly', () => {
      expect(formatTimeValueWithLeadingZero(1000000)).toBe('1000000')
      expect(formatTimeValueWithLeadingZero(999999)).toBe('999999')
    })

    it('should handle null input by throwing an error', () => {
      expect(() => formatTimeValueWithLeadingZero(null as any)).toThrow()
    })

    it('should handle undefined input by throwing an error', () => {
      expect(() => formatTimeValueWithLeadingZero(undefined as any)).toThrow()
    })

    it('should handle string input by converting to string (no error)', () => {
      expect(formatTimeValueWithLeadingZero('5' as any)).toBe('05')
      expect(formatTimeValueWithLeadingZero('10' as any)).toBe('10')
      expect(formatTimeValueWithLeadingZero('hello' as any)).toBe('hello')
    })

    it('should handle NaN input correctly', () => {
      expect(formatTimeValueWithLeadingZero(NaN)).toBe('NaN')
    })

    it('should handle Infinity correctly', () => {
      expect(formatTimeValueWithLeadingZero(Infinity)).toBe('Infinity')
      expect(formatTimeValueWithLeadingZero(-Infinity)).toBe('-Infinity')
    })
  })

  describe('getTimeUnitLabel', () => {
    it('should return correct labels for all time units', () => {
      expect(getTimeUnitLabel('years')).toBe(TimeUnitLabels.YEARS)
      expect(getTimeUnitLabel('months')).toBe(TimeUnitLabels.MONTHS)
      expect(getTimeUnitLabel('days')).toBe(TimeUnitLabels.DAYS)
      expect(getTimeUnitLabel('hours')).toBe(TimeUnitLabels.HOURS)
      expect(getTimeUnitLabel('minutes')).toBe(TimeUnitLabels.MINUTES)
      expect(getTimeUnitLabel('seconds')).toBe(TimeUnitLabels.SECONDS)
    })

    it('should return empty string for unknown unit', () => {
      expect(getTimeUnitLabel('unknown')).toBe('')
      expect(getTimeUnitLabel('milliseconds')).toBe('')
      expect(getTimeUnitLabel('')).toBe('')
    })

    it('should be case sensitive', () => {
      expect(getTimeUnitLabel('DAYS')).toBe('')
      expect(getTimeUnitLabel('Days')).toBe('')
      expect(getTimeUnitLabel('HOURS')).toBe('')
    })

    it('should return empty string for null input', () => {
      expect(getTimeUnitLabel(null as any)).toBe('')
    })

    it('should return empty string for undefined input', () => {
      expect(getTimeUnitLabel(undefined as any)).toBe('')
    })

    it('should return empty string for number input', () => {
      expect(getTimeUnitLabel(123 as any)).toBe('')
    })

    it('should return empty string for boolean input', () => {
      expect(getTimeUnitLabel(true as any)).toBe('')
      expect(getTimeUnitLabel(false as any)).toBe('')
    })

    it('should return empty string for object input', () => {
      expect(getTimeUnitLabel({} as any)).toBe('')
      expect(getTimeUnitLabel({ unit: 'days' } as any)).toBe('')
    })

    it('should return empty string for array input', () => {
      expect(getTimeUnitLabel(['days'] as any)).toBe('')
    })

    it('should handle strings with extra whitespace', () => {
      expect(getTimeUnitLabel(' days ')).toBe('')
      expect(getTimeUnitLabel('days ')).toBe('')
      expect(getTimeUnitLabel(' days')).toBe('')
    })

    it('should handle similar but incorrect unit names', () => {
      expect(getTimeUnitLabel('day')).toBe('')
      expect(getTimeUnitLabel('hour')).toBe('')
      expect(getTimeUnitLabel('minute')).toBe('')
      expect(getTimeUnitLabel('second')).toBe('')
      expect(getTimeUnitLabel('year')).toBe('')
      expect(getTimeUnitLabel('month')).toBe('')
    })

    it('should handle units with different pluralization', () => {
      expect(getTimeUnitLabel('days')).toBe(TimeUnitLabels.DAYS)
      expect(getTimeUnitLabel('hours')).toBe(TimeUnitLabels.HOURS)
      expect(getTimeUnitLabel('minutes')).toBe(TimeUnitLabels.MINUTES)
      expect(getTimeUnitLabel('seconds')).toBe(TimeUnitLabels.SECONDS)
      expect(getTimeUnitLabel('years')).toBe(TimeUnitLabels.YEARS)
      expect(getTimeUnitLabel('months')).toBe(TimeUnitLabels.MONTHS)
    })

    it('should handle special characters and symbols', () => {
      expect(getTimeUnitLabel('days!')).toBe('')
      expect(getTimeUnitLabel('days@')).toBe('')
      expect(getTimeUnitLabel('days#')).toBe('')
      expect(getTimeUnitLabel('days$')).toBe('')
    })
  })
})
