import { Keyable } from '@repo/types/common'

/**
 * @description Checks whether the URL starts with a protocol
 */
export const isUrlAbsolute = (url: string) => {
  const absoluteUrlRegex = /^[a-z][a-z0-9+.-]*:/
  return absoluteUrlRegex.test(url)
}

/**
 *  @description Get the value of a header
 * @param headers
 * @param headerName
 * @returns string | undefined
 */

export const getHeaderValue = (headers: Record<string, string>, headerName: string) => {
  const normalizedHeaderName = headerName.toLowerCase()
  for (const key in headers) {
    if (headers.hasOwnProperty(key) && key.toLowerCase() === normalizedHeaderName) {
      return headers[key]
    }
  }
  return undefined
}

export const createSseUrl = (baseUrl: string, path: string, params: Keyable = {}) => {
  const queryString = Object.keys(params)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')

  return `${baseUrl}${path}${queryString ? '?' + queryString : ''}`
}

/**
 * @description Removes the trailing slash from a URL if it exists
 * @param url - The URL to process
 * @returns The URL without a trailing slash
 */
export const removeTrailSlash = (url: string): string => {
  return url.replace(/\/$/, '')
}

/**
 *
 * @description Normalizes a single path or combines multiple path parts and normalizes the resulting path.
 * @example https://domain.com/asdf///sf\sdfg\\\asdf//asdf////asdf/ => https://domain.com/asdf/sf/sdfg/asdf/asdf/asdf
 */
export const normalizePath = (...paths: string[]) => {
  const combinedPath = (paths.length === 1 ? paths[0] : paths.join('/')) || ''
  let normalized = combinedPath.replace(/\\/g, '/')

  const parts = normalized.split('/')
  const result = parts.reduce((acc, part, index) => {
    if (index > 0 && acc[acc.length - 1]?.endsWith(':') && part === '') {
      acc.push('')
      return acc
    }

    if (index > 0 && acc[acc.length - 1] !== ':' && part === '') {
      return acc // Skip consecutive slashes not preceded by a colon
    }
    acc.push(part)
    return acc
  }, [] as string[])

  const joined = result.join('/')

  if (normalized.startsWith('/') && result.every(part => part === '')) {
    return '/'
  }

  return joined.length > 1 ? joined.replace(/\/$/, '') : joined
}
