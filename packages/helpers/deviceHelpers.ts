export enum DeviceTypeEnum {
  DESKTOP = 'desktop',
  TABLET = 'tablet',
  MOBILE = 'mobile',
}

const MOBILE_USER_AGENT_PATTERNS = [
  /Android.*Mobile/i,
  /iPhone/i,
  /iPod/i,
  /BlackBerry/i,
  /Windows Phone/i,
  /Opera Mini/i,
  /IEMobile/i,
  /Mobile.*Firefox/i,
  /Mobile Safari/i,
]

const TABLET_USER_AGENT_PATTERNS = [
  /iPad/i,
  /Android(?!.*Mobile)/i,
  /Tablet/i,
  /PlayBook/i,
  /Kindle/i,
  /Silk/i,
  /GT-P\d{4}/i,
  /SCH-I\d{3}/i,
  /KFAPWI/i,
  /KFARWI/i,
  /KFASWI/i,
  /KFFOWI/i,
  /KFGIWI/i,
  /KFMEWI/i,
  /KFOT/i,
  /KFTT/i,
  /KFTHWI/i,
  /KFTHWA/i,
  /KFAUWI/i,
  /KFSOWI/i,
  /KFJWA/i,
  /KFJWI/i,
  /KFMAWI/i,
  /KFMUWI/i,
  /KFSAWA/i,
  /KFSAWI/i,
  /KFASWI/i,
  /KFFOWI/i,
  /KFGIWI/i,
  /KFMEWI/i,
]

export const detectDeviceType = (userAgent?: string): DeviceTypeEnum => {
  const ua =
    userAgent ||
    (typeof window !== 'undefined' ? window.navigator.userAgent : '') ||
    (typeof navigator !== 'undefined' ? navigator.userAgent : '')

  if (!ua) {
    return DeviceTypeEnum.DESKTOP
  }

  if (MOBILE_USER_AGENT_PATTERNS.some(pattern => pattern.test(ua))) {
    return DeviceTypeEnum.MOBILE
  }

  if (TABLET_USER_AGENT_PATTERNS.some(pattern => pattern.test(ua))) {
    return DeviceTypeEnum.TABLET
  }

  return DeviceTypeEnum.DESKTOP
}
