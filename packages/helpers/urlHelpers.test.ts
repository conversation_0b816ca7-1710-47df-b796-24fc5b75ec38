import { isUrlAbsolute, getHeaderValue, createSseUrl, removeTrailSlash, normalizePath } from './urlHelpers'

describe('urlHelpers', () => {
  describe('isUrlAbsolute', () => {
    it('should return true for URLs with http protocol', () => {
      expect(isUrlAbsolute('http://example.com')).toBe(true)
      expect(isUrlAbsolute('http://localhost:3000')).toBe(true)
    })

    it('should return true for URLs with https protocol', () => {
      expect(isUrlAbsolute('https://example.com')).toBe(true)
      expect(isUrlAbsolute('https://secure.example.com/path')).toBe(true)
    })

    it('should return true for other valid protocols', () => {
      expect(isUrlAbsolute('ftp://files.example.com')).toBe(true)
      expect(isUrlAbsolute('mailto:<EMAIL>')).toBe(true)
      expect(isUrlAbsolute('file:///path/to/file')).toBe(true)
      expect(isUrlAbsolute('ws://websocket.example.com')).toBe(true)
      expect(isUrlAbsolute('wss://secure-websocket.example.com')).toBe(true)
    })

    it('should return false for relative URLs', () => {
      expect(isUrlAbsolute('/relative/path')).toBe(false)
      expect(isUrlAbsolute('./relative/path')).toBe(false)
      expect(isUrlAbsolute('../relative/path')).toBe(false)
      expect(isUrlAbsolute('relative/path')).toBe(false)
    })

    it('should return false for URLs without protocol', () => {
      expect(isUrlAbsolute('example.com')).toBe(false)
      expect(isUrlAbsolute('www.example.com')).toBe(false)
      expect(isUrlAbsolute('subdomain.example.com/path')).toBe(false)
    })

    it('should return false for invalid protocol formats', () => {
      expect(isUrlAbsolute('123://invalid.com')).toBe(false)
      expect(isUrlAbsolute('://invalid.com')).toBe(false)
      expect(isUrlAbsolute('http://')).toBe(true) // This is actually valid according to the regex
    })

    it('should return false for empty string', () => {
      expect(isUrlAbsolute('')).toBe(false)
    })
  })

  describe('getHeaderValue', () => {
    const mockHeaders = {
      'Content-Type': 'application/json',
      Authorization: 'Bearer token123',
      'X-Custom-Header': 'custom-value',
      'UPPERCASE-HEADER': 'uppercase-value',
    }

    it('should return header value for exact case match', () => {
      expect(getHeaderValue(mockHeaders, 'Content-Type')).toBe('application/json')
      expect(getHeaderValue(mockHeaders, 'Authorization')).toBe('Bearer token123')
    })

    it('should return header value for case-insensitive match', () => {
      expect(getHeaderValue(mockHeaders, 'content-type')).toBe('application/json')
      expect(getHeaderValue(mockHeaders, 'CONTENT-TYPE')).toBe('application/json')
      expect(getHeaderValue(mockHeaders, 'authorization')).toBe('Bearer token123')
      expect(getHeaderValue(mockHeaders, 'AUTHORIZATION')).toBe('Bearer token123')
    })

    it('should return header value for mixed case headers', () => {
      expect(getHeaderValue(mockHeaders, 'x-custom-header')).toBe('custom-value')
      expect(getHeaderValue(mockHeaders, 'X-CUSTOM-HEADER')).toBe('custom-value')
      expect(getHeaderValue(mockHeaders, 'uppercase-header')).toBe('uppercase-value')
      expect(getHeaderValue(mockHeaders, 'UPPERCASE-HEADER')).toBe('uppercase-value')
    })

    it('should return undefined for non-existent headers', () => {
      expect(getHeaderValue(mockHeaders, 'Non-Existent')).toBeUndefined()
      expect(getHeaderValue(mockHeaders, 'missing-header')).toBeUndefined()
    })

    it('should return undefined for empty header name', () => {
      expect(getHeaderValue(mockHeaders, '')).toBeUndefined()
    })

    it('should work with empty headers object', () => {
      expect(getHeaderValue({}, 'Content-Type')).toBeUndefined()
    })

    it('should handle headers with empty values', () => {
      const headersWithEmpty = { 'Empty-Header': '' }
      expect(getHeaderValue(headersWithEmpty, 'Empty-Header')).toBe('')
    })
  })

  describe('createSseUrl', () => {
    it('should create URL without parameters', () => {
      const result = createSseUrl('https://api.example.com', '/events')
      expect(result).toBe('https://api.example.com/events')
    })

    it('should create URL with single parameter', () => {
      const result = createSseUrl('https://api.example.com', '/events', { userId: '123' })
      expect(result).toBe('https://api.example.com/events?userId=123')
    })

    it('should create URL with multiple parameters', () => {
      const result = createSseUrl('https://api.example.com', '/events', {
        userId: '123',
        channel: 'general',
        limit: '50',
      })
      expect(result).toBe('https://api.example.com/events?userId=123&channel=general&limit=50')
    })

    it('should encode special characters in parameter values', () => {
      const result = createSseUrl('https://api.example.com', '/events', {
        query: 'hello world',
        special: 'foo&bar=baz',
      })
      expect(result).toBe('https://api.example.com/events?query=hello%20world&special=foo%26bar%3Dbaz')
    })

    it('should handle empty parameter object', () => {
      const result = createSseUrl('https://api.example.com', '/events', {})
      expect(result).toBe('https://api.example.com/events')
    })

    it('should handle undefined parameters', () => {
      const result = createSseUrl('https://api.example.com', '/events')
      expect(result).toBe('https://api.example.com/events')
    })

    it('should handle numeric and boolean parameter values', () => {
      const result = createSseUrl('https://api.example.com', '/events', {
        count: 42,
        active: true,
        archived: false,
      })
      expect(result).toBe('https://api.example.com/events?count=42&active=true&archived=false')
    })

    it('should handle base URL without trailing slash', () => {
      const result = createSseUrl('https://api.example.com', '/events', { test: 'value' })
      expect(result).toBe('https://api.example.com/events?test=value')
    })

    it('should handle base URL with trailing slash', () => {
      const result = createSseUrl('https://api.example.com/', '/events', { test: 'value' })
      expect(result).toBe('https://api.example.com//events?test=value')
    })
  })

  describe('removeTrailSlash', () => {
    it('should remove trailing slash from URL', () => {
      expect(removeTrailSlash('https://example.com/')).toBe('https://example.com')
      expect(removeTrailSlash('/path/to/resource/')).toBe('/path/to/resource')
      expect(removeTrailSlash('relative/path/')).toBe('relative/path')
    })

    it('should not modify URL without trailing slash', () => {
      expect(removeTrailSlash('https://example.com')).toBe('https://example.com')
      expect(removeTrailSlash('/path/to/resource')).toBe('/path/to/resource')
      expect(removeTrailSlash('relative/path')).toBe('relative/path')
    })

    it('should handle multiple trailing slashes by removing only the last one', () => {
      expect(removeTrailSlash('https://example.com//')).toBe('https://example.com/')
      expect(removeTrailSlash('/path///')).toBe('/path//')
    })

    it('should handle empty string', () => {
      expect(removeTrailSlash('')).toBe('')
    })

    it('should handle single slash', () => {
      expect(removeTrailSlash('/')).toBe('')
    })

    it('should handle root URL with trailing slash', () => {
      expect(removeTrailSlash('https://example.com/')).toBe('https://example.com')
    })
  })

  describe('normalizePath', () => {
    it('should normalize single path with multiple slashes', () => {
      expect(normalizePath('path//to///resource')).toBe('path/to/resource')
      expect(normalizePath('/path//to///resource/')).toBe('/path/to/resource')
    })

    it('should normalize single path with backslashes', () => {
      expect(normalizePath('path\\to\\resource')).toBe('path/to/resource')
      expect(normalizePath('path\\\\to\\\\\\resource')).toBe('path/to/resource')
    })

    it('should normalize mixed slashes and backslashes', () => {
      expect(normalizePath('path\\to//resource\\file')).toBe('path/to/resource/file')
      expect(normalizePath('\\path//to\\\\resource//')).toBe('/path/to/resource')
    })

    it('should combine multiple paths and normalize', () => {
      expect(normalizePath('path', 'to', 'resource')).toBe('path/to/resource')
      expect(normalizePath('/path/', '/to/', '/resource/')).toBe('/path/to/resource')
      expect(normalizePath('path//', '//to//', '//resource')).toBe('path/to/resource')
    })

    it('should handle URLs with protocols correctly', () => {
      expect(normalizePath('https://domain.com//path///to//resource/')).toBe('https://domain.com/path/to/resource')
      expect(normalizePath('http://localhost:3000///api//v1///users/')).toBe('http://localhost:3000/api/v1/users')
    })

    it('should preserve protocol slashes', () => {
      expect(normalizePath('https://example.com')).toBe('https://example.com')
      expect(normalizePath('file:///path/to/file')).toBe('file://path/to/file') // The regex removes duplicate slashes after colon
    })

    it('should handle complex example from JSDoc', () => {
      const input = 'https://domain.com/asdf///sf\\sdfg\\\\\\asdf//asdf////asdf/'
      const expected = 'https://domain.com/asdf/sf/sdfg/asdf/asdf/asdf'
      expect(normalizePath(input)).toBe(expected)
    })

    it('should handle empty paths', () => {
      expect(normalizePath('')).toBe('')
      expect(normalizePath('', '', '')).toBe('/') // Empty strings joined with '/' create a single slash
    })

    it('should handle single slash', () => {
      expect(normalizePath('/')).toBe('/')
      expect(normalizePath('//')).toBe('/')
    })

    it('should handle root path with multiple slashes', () => {
      expect(normalizePath('///')).toBe('/')
      expect(normalizePath('/', '/', '/')).toBe('/')
    })

    it('should handle paths with only slashes and backslashes', () => {
      expect(normalizePath('\\\\//\\\\//')).toBe('/') // Should normalize to single slash
    })

    it('should handle relative paths', () => {
      expect(normalizePath('./path//to/resource')).toBe('./path/to/resource')
      expect(normalizePath('../path\\to\\resource')).toBe('../path/to/resource')
    })

    it('should handle as in example', () => {
      expect(normalizePath('https://domain.com/asdf///sf\\dfg\\\asdf//asdf////asdf/')).toBe(
        'https://domain.com/asdf/sf/dfg/asdf/asdf/asdf',
      )
    })

    it('should handle Windows-style paths', () => {
      expect(normalizePath('C:\\path\\to\\file')).toBe('C:/path/to/file')
      expect(normalizePath('C:\\\\\path\\\\to\\\\file')).toBe('C://path/to/file')
    })

    it('should handle query parameters and fragments', () => {
      expect(normalizePath('/path//to/resource?param=value')).toBe('/path/to/resource?param=value')
      expect(normalizePath('/path//to/resource#section')).toBe('/path/to/resource#section')
    })

    it('should handle mixed scenarios', () => {
      expect(normalizePath('path1\\\\', '//path2//', '\\path3')).toBe('path1/path2/path3') // Should normalize all paths
      expect(normalizePath('/root/', 'sub//path\\', 'file.txt')).toBe('/root/sub/path/file.txt')
    })
  })
})
