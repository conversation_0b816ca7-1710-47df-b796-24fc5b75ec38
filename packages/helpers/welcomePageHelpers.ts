/**
 * Helper utilities for working with Welcome Page v2 responses
 */

import { RHIN<PERSON>AYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS } from '@repo/constants/common/rhinoLayer'
import type {
  WelcomePageV2Response,
  WelcomePageSection,
  BannerSection,
  InfoSection,
  GamePreview,
  GamesSection,
  AppSection,
  VipSection,
  TestimonialLander,
  PaymentSection,
  FaqSection,
  SeoSection,
} from '@repo/types/api/s3/welcome-page-v2'

export function isBannerSection(section: WelcomePageSection): section is BannerSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.BANNER_SECTION
}

export function isInfoSection(section: WelcomePageSection): section is InfoSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.INFO_SECTION
}

export function isGamePreview(section: WelcomePageSection): section is GamePreview {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.GAME_PREVIEW
}

export function isGamesSection(section: WelcomePageSection): section is GamesSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.GAMES_SECTION
}

export function isAppSection(section: WelcomePageSection): section is AppSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.APP_SECTION
}

export function isVipSection(section: WelcomePageSection): section is VipSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.VIP_SECTION
}

export function isTestimonialLander(section: WelcomePageSection): section is TestimonialLander {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.TESTIMONIAL_LANDER
}

export function isPaymentSection(section: WelcomePageSection): section is PaymentSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.PAYMENT_SECTION
}

export function isFaqSection(section: WelcomePageSection): section is FaqSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.FAQ_SECTION
}

export function isSeoSection(section: WelcomePageSection): section is SeoSection {
  return section.type === RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS.SEO_SECTION
}

export function getSectionsByType<T extends WelcomePageSection>(
  welcomePage: WelcomePageV2Response,
  typeGuard: (section: WelcomePageSection) => section is T,
): T[] {
  return welcomePage.welcome_page_v2.filter(typeGuard)
}
