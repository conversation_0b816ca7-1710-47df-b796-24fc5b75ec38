import { TimeUnitLabels } from '@repo/constants/common/sidebar'
import type { ITimeObject } from '@repo/types/time'

/**
 * Formats a number to always display with at least 2 digits by adding leading zeros
 * @param num - The number to format
 * @returns A string with at least 2 digits (e.g., 5 -> "05", 12 -> "12")
 */
export const formatTimeValueWithLeadingZero = (num: number): string => {
  return num.toString().padStart(2, '0')
}

/**
 * Gets the display label for a time unit
 * @param unit - The time unit string (years, months, days, hours, minutes, seconds)
 * @returns The corresponding display label from TimeUnitLabels enum
 */
export const getTimeUnitLabel = (unit: string): string => {
  switch (unit) {
    case 'years':
      return TimeUnitLabels.YEARS
    case 'months':
      return TimeUnitLabels.MONTHS
    case 'days':
      return TimeUnitLabels.DAYS
    case 'hours':
      return TimeUnitLabels.HOURS
    case 'minutes':
      return TimeUnitLabels.MINUTES
    case 'seconds':
      return TimeUnitLabels.SECONDS
    default:
      return ''
  }
}

/**
 * Calculates the time difference between two dates
 * @param params - Object containing startTime and endTime as Date objects
 * @returns An object containing years, months, days, hours, minutes, and seconds remaining
 */
export const calculateTimeDifference = ({ startTime, endTime }: { startTime: Date; endTime: Date }): ITimeObject => {
  const difference = endTime.getTime() - startTime.getTime()

  if (difference > 0) {
    const totalSeconds = Math.floor(difference / 1000)
    const totalMinutes = Math.floor(totalSeconds / 60)
    const totalHours = Math.floor(totalMinutes / 60)
    const totalDays = Math.floor(totalHours / 24)

    const years = Math.floor(totalDays / 365.25)
    const remainingDaysAfterYears = totalDays - Math.floor(years * 365.25)
    const months = Math.floor(remainingDaysAfterYears / 30.44)
    const days = Math.floor(remainingDaysAfterYears - months * 30.44)

    return {
      years,
      months,
      days,
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
    }
  } else {
    return { years: 0, months: 0, days: 0, hours: 0, minutes: 0, seconds: 0 }
  }
}
