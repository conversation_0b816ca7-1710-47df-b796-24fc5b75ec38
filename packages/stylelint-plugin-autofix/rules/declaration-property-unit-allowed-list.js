import stylelint from 'stylelint'

const { createPlugin, utils } = stylelint

const ruleName = 'plugin/declaration-property-unit-allowed-list'
const messages = utils.ruleMessages(ruleName, {
  rejected: (property, unit, allowed) =>
    `Unexpected unit "${unit}" for property "${property}". Allowed units: ${allowed.join(', ')}`,
})

/**
 * Extract all value-unit pairs from a CSS value, excluding units inside function calls
 * @param {string} value
 * @returns {Array<{value: string, unit: string, startIndex: number, endIndex: number}>}
 */
const getValueUnits = value => {
  const valueUnitRegex = /(-?\d*\.?\d+)([a-zA-Z%]+)/g
  const matches = []
  let match

  // Find all function calls to exclude their content
  const functionRegex = /\w+\([^)]*\)/g
  const functionRanges = []
  let funcMatch
  
  while ((funcMatch = functionRegex.exec(value)) !== null) {
    functionRanges.push({
      start: funcMatch.index,
      end: funcMatch.index + funcMatch[0].length
    })
  }

  // Check if a position is inside any function call
  const isInsideFunction = (position) => {
    return functionRanges.some(range => position >= range.start && position < range.end)
  }

  while ((match = valueUnitRegex.exec(value)) !== null) {
    // Skip units that are inside function calls (like calculate-rem(16px))
    if (!isInsideFunction(match.index)) {
      matches.push({
        value: match[1],
        unit: match[2],
        startIndex: match.index,
        endIndex: match.index + match[0].length,
      })
    }
  }

  return matches
}

/**
 * Check if a property matches a pattern
 * @param {string} property
 * @param {string} pattern
 * @returns {boolean}
 */
const matchesProperty = (property, pattern) => {
  if (pattern.startsWith('/') && pattern.endsWith('/')) {
    const regexPattern = new RegExp(pattern.slice(1, -1))
    return regexPattern.test(property)
  }
  return property === pattern
}

/**
 * Find allowed units for a property
 * @param {string} property
 * @param {Object} allowedList
 * @returns {string[] | null}
 */
const getAllowedUnits = (property, allowedList) => {
  for (const [pattern, units] of Object.entries(allowedList)) {
    if (matchesProperty(property, pattern)) {
      return units
    }
  }
  return null
}

/**
 * Convert a rem value to px value using standard browser default of 16px = 1rem
 * @param {string} value
 * @returns {string}
 */
const remToPx = value => {
  const remValue = parseFloat(value)
  const pxValue = remValue * 16
  return `${pxValue}px`
}

/**
 * Wrap a px value in calculate-rem function
 * @param {string} pxValue
 * @returns {string}
 */
const wrapInCalculateRem = pxValue => {
  return `calculate-rem(${pxValue})`
}

/** @type {import('stylelint').Rule} */
const ruleFunction = (primaryOptions, secondaryOptions) => {
  return (root, result) => {
    const validOptions = utils.validateOptions(
      result,
      ruleName,
      {
        actual: primaryOptions,
        possible: [actual => typeof actual === 'object' && !Array.isArray(actual)],
      },
      {
        optional: true,
        actual: secondaryOptions,
        possible: {
          message: [actual => typeof actual === 'string', actual => typeof actual === 'function'],
        },
      },
    )

    if (!validOptions) {
      return
    }

    const allowedList = primaryOptions

    root.walkDecls(decl => {
      const { prop, value } = decl

      // Skip values without numeric values or special values
      if (
        value === '0' ||
        value.includes('var(') ||
        value === 'inherit' ||
        value === 'initial' ||
        value === 'unset' ||
        value === 'auto' ||
        value === 'none' ||
        !/\d/.test(value)
      ) {
        return
      }

      // Find the allowed units for this property
      const allowedUnits = getAllowedUnits(prop, allowedList)
      if (!allowedUnits) {
        return
      }

      // Extract all value-unit pairs from the value
      const valueUnits = getValueUnits(value)
      if (!valueUnits.length) {
        return
      }

      // Track if we need to fix anything
      let needsFix = false
      let fixedValue = value

      // Check each value-unit pair
      for (const { unit, value: numericValue, startIndex, endIndex } of valueUnits) {
        // Check if this property should use rem units but we found px
        const shouldUseRem = allowedUnits.includes('rem') && unit === 'px'
        const shouldUseCalculateRemForPx = shouldUseRem && !value.includes('calculate-rem(')
        
        // Check if this property allows rem and we found a rem value that should be converted to calculate-rem(px)
        const shouldConvertRemToCalculateRem = allowedUnits.includes('rem') && unit === 'rem' && !value.includes('calculate-rem(')
        
        // Skip if the unit is allowed and doesn't need conversion
        if (allowedUnits.includes(unit) && !shouldUseCalculateRemForPx && !shouldConvertRemToCalculateRem) {
          continue
        }

        // Get the index of the start of the value in the declaration
        // Index needs to be relative to the start of the declaration, not just the value
        const valueIndex = decl.toString().indexOf(decl.value)

        // Report the issue
        let customMessage
        if (shouldUseCalculateRemForPx) {
          customMessage = `Use rem units with calculate-rem() function. Example: calculate-rem(${numericValue}px)`
        } else if (shouldConvertRemToCalculateRem) {
          const pxValue = remToPx(numericValue)
          customMessage = `Use calculate-rem() function instead of rem units. Example: calculate-rem(${pxValue}px)`
        } else {
          customMessage = messages.rejected(prop, unit, allowedUnits)
        }
          
        utils.report({
          message: customMessage,
          node: decl,
          index: valueIndex + startIndex,
          endIndex: valueIndex + endIndex,
          result,
          ruleName,
        })

        // Apply fixes
        if (allowedUnits.length > 0) {
          // Auto-fix: convert rem to px with calculate-rem for properties that should use px
          if (unit === 'rem' && allowedUnits.includes('px')) {
            needsFix = true
            // Convert this specific value-unit
            const originalValue = numericValue + unit
            const pxValue = remToPx(numericValue)
            const convertedValue = wrapInCalculateRem(pxValue)
            fixedValue = fixedValue.replace(originalValue, convertedValue)
          }
          // Auto-fix: convert rem to calculate-rem(px) for properties that use rem
          else if (shouldConvertRemToCalculateRem) {
            needsFix = true
            // Convert this specific rem value to calculate-rem(px)
            const originalValue = numericValue + unit
            const pxValue = remToPx(numericValue)
            const convertedValue = wrapInCalculateRem(pxValue)
            fixedValue = fixedValue.replace(originalValue, convertedValue)
          }
          // Auto-fix: wrap px values in calculate-rem for properties that should use rem
          else if (shouldUseCalculateRemForPx) {
            needsFix = true
            // Wrap this specific px value in calculate-rem
            const originalValue = numericValue + unit
            const convertedValue = wrapInCalculateRem(originalValue)
            fixedValue = fixedValue.replace(originalValue, convertedValue)
          }
        }
      }

      // If we need to fix rem to px with calculate-rem, apply the fixed value
      if (needsFix) {
        decl.value = fixedValue
      }
    })
  }
}

const meta = {
  url: 'https://stylelint.io/user-guide/rules/declaration-property-unit-allowed-list',
  fixable: true,
}

ruleFunction.ruleName = ruleName
ruleFunction.messages = messages
ruleFunction.meta = meta

export default createPlugin(ruleName, ruleFunction)
