import stylelint from 'stylelint'

const { createPlugin, utils } = stylelint

const ruleName = 'plugin/require-calculate-rem-import'

const messages = utils.ruleMessages(ruleName, {
  missing: "Missing @use '@theme/functions.scss' as *; import required for calculate-rem() function",
})

const ruleFunction = primary => {
  return (root, result) => {
    const validOptions = utils.validateOptions(result, ruleName, {
      actual: primary,
      possible: [true, false],
    })

    if (!validOptions || !primary) {
      return
    }

    // Check if the file contains calculate-rem() function calls
    let hasCalculateRem = false
    let hasThemeImport = false

    // Check for calculate-rem usage
    root.walkDecls(decl => {
      if (decl.value.includes('calculate-rem(')) {
        hasCalculateRem = true
      }
    })

    // Check for proper theme import
    root.walkAtRules('use', rule => {
      if (
        rule.params.includes('@theme/functions') ||
        rule.params.includes('@theme/functions.scss') ||
        rule.params.includes('@theme/index.scss')
      ) {
        hasThemeImport = true
      }
    })

    // Report error if calculate-rem is used without proper import
    if (hasCalculateRem && !hasThemeImport) {
      utils.report({
        message: messages.missing,
        node: root,
        result,
        ruleName,
        fix: () => {
          // Add the @use import at the beginning of the file
          const useRule = `@use '@theme/functions.scss' as *;`

          // Check if there are any existing @use rules
          let lastUseRule = null
          root.walkAtRules('use', rule => {
            lastUseRule = rule
          })

          // If there are existing @use rules, add after the last one
          if (lastUseRule) {
            lastUseRule.after(`\n${useRule}`)
          } else {
            // If no @use rules exist, add at the beginning of the file
            // Check if there are any comments at the top
            let firstNode = root.first

            // Skip over initial comments
            while (firstNode && firstNode.type === 'comment') {
              firstNode = firstNode.next()
            }

            if (firstNode) {
              firstNode.before(`${useRule}\n\n`)
            } else {
              // If file is empty or only comments, append to root
              root.append(`${useRule}\n`)
            }
          }
        },
      })
    }
  }
}

const meta = {
  url: 'https://github.com/rhinoent/rhino-next',
  fixable: true,
}

ruleFunction.ruleName = ruleName
ruleFunction.messages = messages
ruleFunction.meta = meta

export default createPlugin(ruleName, ruleFunction)
