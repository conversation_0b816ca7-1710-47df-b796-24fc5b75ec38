[{"ROOT": {"type": "PromotionRoot", "props": {"key": "ROOT", "seoTitle": "", "seoDescription": "", "children": [{"key": "oj1oc1kf", "type": "Text", "props": {"content": "<p>Join the Wild Wins round of our Grand Holiday Spinomenal tournament and collect points for every win multiplier. The more points you collect, the higher your position on the leaderboard. The 70 first players on the leaderboard will share a prize of €15,000!</p>\n<p></p>\n<p>How to participate:</p>\n<p>1. Place an eligible bet on any participating game</p>\n<p>2. Score points with every win</p>\n<p>3. Climb the Leaderboard and be in for a cash prize</p>\n<p></p>\n<p></p>\n"}, "children": []}, {"key": "w8as3cem", "type": "Text", "props": {"content": "<p>Most important:</p>\n<p>&nbsp;- This tournament runs from 31.08 02:01 CEST until 07.09 01:59 CEST</p>\n<p>&nbsp;- Each win rewards you with a score based on the win multiplier. Points received are summed up throughout the Tournament period.</p>\n<p>&nbsp;- Only real money bets count towards this promotion</p>\n<p>&nbsp;- Minimum qualifying bet per spin to participate in the Tournament is €0.2</p>\n"}, "children": []}, {"key": "ouqktr2y", "type": "Reference", "props": {"meta": {"h2my82ny": {"id": "h2my82ny", "key": "list", "type": "LIST_FIELD_COMPONENT", "content": ["1", "3", "24", "25", "9", "4", "59", "52"]}}, "reference": "RaffleComponent"}, "children": []}, {"key": "gcz1lulk", "type": "GameThumbnails", "props": {"gameIds": [{"id": 25747, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fposeidons-rising.jpg", "name": "Poseidon's Rising", "slug": "poseidons-rising", "mobileId": "spm_poseidonsrising", "desktopId": "spm_poseidonsrising"}, {"id": 24195, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fbook-of-demi-gods-iv.jpg", "name": "Book of Demi Gods IV", "slug": "book-of-demi-gods-iv", "mobileId": "spm_bookofdemigodsiv", "desktopId": "spm_bookofdemigodsiv"}, {"id": 18479, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fwolf-fang-iron-wolf.jpg", "name": "Wolf Fang Iron Wolf", "slug": "wolf-fang-iron-wolf", "mobileId": "spm_wolffan<PERSON><PERSON>wolf", "desktopId": "spm_wolffan<PERSON><PERSON>wolf"}, {"id": 14, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Faloha-cluster-pays.jpg", "name": "Aloha! Cluster Pays", "slug": "aloha-cluster-pays", "mobileId": "aloha_mobile_html", "desktopId": "aloha_not_mobile_sw"}, {"id": 16, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Farcane-reel-chaos.jpg", "name": "Arcane: <PERSON><PERSON> chaos", "slug": "arcane-reel-chaos", "mobileId": "arcane_mobile_html", "desktopId": "arcane_not_mobile"}, {"id": 17, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Farchangels-salvation.jpg", "name": "Archangels Salvation", "slug": "archangels-salvation", "mobileId": "archangels_mobile_html", "desktopId": "archangels_not_mobile"}, {"id": 18, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fasgardian-stones.jpg", "name": "Asgardian Stones", "slug": "asgardian-stones", "mobileId": "asgardianstones_mobile_html", "desktopId": "asgardianstones_not_mobile"}, {"id": 19, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fberryburst.jpg", "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "berryburst", "mobileId": "berryburst_mobile_html", "desktopId": "ntnberryburst_not_mobile_sw"}, {"id": 20, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fberryburst-max.jpg", "name": "Berryburst MAX", "slug": "berryburst-max", "mobileId": "berryburstmax_mobile_html", "desktopId": "berryburstmax_not_mobile"}, {"id": 21, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fblood-suckers.jpg", "name": "Blood Suckers", "slug": "blood-suckers", "mobileId": "bloodsuckers0000", "desktopId": "bloodsuckers0000"}, {"id": 22, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fblood-suckers-ii.jpg", "name": "Blood Suckers II", "slug": "blood-suckers-ii", "mobileId": "bloodsuckers2000", "desktopId": "bloodsuckers2000"}, {"id": 23, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fbollywood-story.jpg", "name": "Bollywood Story", "slug": "bollywood-story", "mobileId": "bollywoodstory_mobile_html", "desktopId": "bollywoodstory_not_mobile"}, {"id": 24, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fark-of-mystery.jpg", "name": "Ark of Mystery", "slug": "ark-of-mystery", "mobileId": null, "desktopId": "arkofmystery"}, {"id": 26, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fbig-bot-crew.jpg", "name": "Big Bot Crew", "slug": "big-bot-crew", "mobileId": null, "desktopId": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 27, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fcrystal-queen.jpg", "name": "Crystal Queen", "slug": "crystal-queen", "mobileId": null, "desktopId": "crystalqueen"}, {"id": 28, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fdiamond-duke.jpg", "name": "Diamond Duke", "slug": "diamond-duke", "mobileId": null, "desktopId": "diamondduke"}, {"id": 29, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fdivine-dreams.jpg", "name": "Divine Dreams", "slug": "divine-dreams", "mobileId": null, "desktopId": "divinedreams"}, {"id": 32, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fdragon-shrine.jpg", "name": "Dragon Shrine", "slug": "dragon-shrine", "mobileId": null, "desktopId": "<PERSON><PERSON>ine"}, {"id": 33, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fdurian-dynamite.jpg", "name": "Durian Dynamite", "slug": "durian-dynamite", "mobileId": null, "desktopId": "duriandynamite"}, {"id": 35, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2F6-wild-sharks.jpg", "name": "6 Wild Sharks", "slug": "6-wild-sharks", "mobileId": null, "desktopId": "6wildsharks"}, {"id": 36, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Firon-bank.jpg", "name": "Iron Bank", "slug": "iron-bank", "mobileId": null, "desktopId": "ironbank"}, {"id": 37, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2F1-2-3-boom.jpg", "name": "1 2 3 Boom", "slug": "1-2-3-boom", "mobileId": null, "desktopId": "123boom"}, {"id": 38, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Ftiki-infinity-reels-megaways.jpg", "name": "Tiki Infinity Reels Megaways", "slug": "tiki-infinity-reels-megaways", "mobileId": "tikiinfinityreelsmegaways", "desktopId": "tikiinfinityreelsmegaways"}, {"id": 39, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Ftnt-tumble.jpg", "name": "TNT Tumble", "slug": "tnt-tumble", "mobileId": null, "desktopId": "tnttumble"}, {"id": 40, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fcaveman-bob.jpg", "name": "Caveman <PERSON>", "slug": "caveman-bob", "mobileId": null, "desktopId": "cavemanbob"}, {"id": 41, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Ferik-the-red.jpg", "name": "<PERSON> the Red", "slug": "erik-the-red", "mobileId": null, "desktopId": "er<PERSON><PERSON>ed"}, {"id": 43, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fmega-masks.jpg", "name": "Mega Masks", "slug": "mega-masks", "mobileId": null, "desktopId": "megamasks"}, {"id": 45, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Farcader.jpg", "name": "<PERSON><PERSON>", "slug": "arcader", "mobileId": null, "desktopId": "tk-astronaut"}, {"id": 46, "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Fbabushkas.jpg", "name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "mobileId": null, "desktopId": "tk-s1-g5"}]}, "children": []}, {"key": "h3w0qquu", "type": "TableBlock", "props": {"data": [{"ayz38j": "€2000", "ladb1d": "1"}, {"ayz38j": "€1800", "ladb1d": "2"}, {"ayz38j": "€1600", "ladb1d": "3"}, {"ayz38j": "€1400", "ladb1d": "4"}, {"ayz38j": "€1200", "ladb1d": "5"}, {"ayz38j": "€900", "ladb1d": "6"}, {"ayz38j": "€800", "ladb1d": "7"}, {"ayz38j": "€600", "ladb1d": "8"}, {"ayz38j": "€400", "ladb1d": "9"}, {"ayz38j": "€200", "ladb1d": "10"}, {"ayz38j": "€110", "ladb1d": "11-20"}, {"ayz38j": "€90", "ladb1d": "21-30"}, {"ayz38j": "€80", "ladb1d": "31-40"}, {"ayz38j": "€45", "ladb1d": "41-60"}, {"ayz38j": "€40", "ladb1d": "61-70"}], "order": ["ladb1d", "ayz38j"], "columns": {"ayz38j": "Prize", "ladb1d": "Position"}}, "children": []}]}}}]