import { HttpResponse, http, delay } from 'msw'
// import essentialConfig from './mobileApp/essentialConfig.json'
import translations from './translations/en_ROW.json'
import appConfig from './configs/app-config_en_ROW.json'
import appConfigGlobal from './configs/app-config-global.json'
import welcomePage from './configs/welcome-page_en_ROW.json'
import welcomePageV2 from './configs/welcome-page-v2_en_ROW.json'
// import mobileAppLocalConfig from './configs/mobile-app-config_en_ROW.json'
// import mobileAppGlobalConfig from './configs/mobile-app-global-config.json'
// import mobileAppQaConfig from './configs/mobile-app-qa-config.json'
import currencyDetails from './configs/currency-details-global.json'
// import balanceConfig from './configs/balance-config_en_ROW.json'
// import gameCategories from './configs/game-categories_en_ROW.json'
import games from './games/index.json'
import lobbyCasino from './lobby/casino_en_ROW.json'
import liveLobbyCasino from './lobby/live-casino_en_ROW.json'
import promotionSpinomenal from './promotion/spinomenal-1-grand-holiday_en_ROW.json'
import footerConfig from './configs/footer-config_en_ROW.json'
// import blackListedCountries from './configs/blacklisted-countries_en_ROW.json'
// import blackListedStates from './configs/blacklisted-states_en_ROW.json'
// import limitsConfig from './configs/limits-config_en_ROW.json'
// import countryDetails from './configs/country-details-global.json'
// import mobileAppRoutes from './configs/mobile-app-routes-global.json'
import sidebarGlobalConfig from './configs/sidebar-config-global.json'
import timerComponentConfig from './configs/timer-component-global.json'
import featuredOfferGlobal from './configs/featured-offer_en_ROW.json'
import sidemenuGlobalConfig from './configs/sidemenu-config-global.json'
import homePageGlobalConfig from './configs/home-page-config-global.json'
import promotionsPageGlobalConfig from './configs/promotions-page-config-global.json'

export const s3Handlers = [
  http.get('*/games/index.json', () => {
    return HttpResponse.json(games, {
      headers: {
        'Content-Type': 'application/json',
        'Last-Modified': 'Wed, 20 Dec 2023 12:44:39 GMT',
        Etag: '"2b7f2032b5073b7481e001f2d8e362d9"',
      },
    })
  }),
  // http.get('*/mobileApp/essentialConfig.json', () => {
  //   return HttpResponse.json(essentialConfig)
  // }),
  http.get('*/translations/en_ROW.json', () => {
    return HttpResponse.json(translations)
  }),
  http.get('*/configs/app-config_en_ROW.json', () => {
    return HttpResponse.json(appConfig)
  }),
  http.get('*/configs/app-config-global.json', () => {
    return HttpResponse.json(appConfigGlobal)
  }),
  http.get('*/configs/currency-details-global.json', () => {
    return HttpResponse.json(currencyDetails)
  }),
  // http.get('*/configs/mobile-app-config_en_ROW.json', () => {
  //   return HttpResponse.json(mobileAppLocalConfig)
  // }),
  // http.get('*/configs/mobile-app-global-config.json', () => {
  //   return HttpResponse.json(mobileAppGlobalConfig)
  // }),
  // http.get('*/configs/mobile-app-qa-config.json', () => {
  //   return HttpResponse.json(mobileAppQaConfig)
  // }),
  http.get('*/configs/welcome-page_en_ROW.json', () => {
    return HttpResponse.json(welcomePage)
  }),
  http.get('*/configs/welcome-page-v2_en_ROW.json', () => {
    return HttpResponse.json(welcomePageV2)
  }),
  // http.get('*/configs/balance-config_en_ROW.json', () => {
  //   return HttpResponse.json(balanceConfig)
  // }),
  // http.get('*/configs/game-categories_en_ROW.json', () => {
  //   return HttpResponse.json(gameCategories)
  // }),
  http.get('*/lobby/casino_en_ROW.json', () => {
    return HttpResponse.json(lobbyCasino)
  }),
  http.get('*/lobby/live-casino_en_ROW.json', () => {
    return HttpResponse.json(liveLobbyCasino)
  }),
  http.get('*/promotions/spinomenal-1-grand-holiday_en_ROW.json', async () => {
    await delay(100)
    return HttpResponse.json(promotionSpinomenal)
  }),
  // http.get('*/configs/raffle-config_en_ROW.json', async () => {
  //   return HttpResponse.json(raffleConfig)
  // }),
  http.get('*/configs/footer-config_en_ROW.json', async () => {
    return HttpResponse.json(footerConfig)
  }),
  // http.get('*/configs/blacklisted-countries_en_ROW.json', async () => {
  //   await delay(50)
  //   return HttpResponse.json(blackListedCountries)
  // }),
  // http.get('*/configs/blacklisted-states_en_ROW.json', async () => {
  //   await delay(50)
  //   return HttpResponse.json(blackListedStates)
  // }),
  // http.get('*/configs/limits-config_en_ROW.json', async () => {
  //   await delay(50)
  //   return HttpResponse.json(limitsConfig)
  // }),
  // http.get('*/configs/country-details.json', async () => {
  //   await delay(50)
  //   return HttpResponse.json(countryDetails)
  // }),
  // http.get('*/configs/mobile-app-routes.json', async () => {
  //   await delay(50)
  //   return HttpResponse.json(mobileAppRoutes)
  // }),
  http.get('*/configs/sidebar-config-global.json', async () => {
    await delay(50)
    return HttpResponse.json(sidebarGlobalConfig)
  }),
  http.get('*/configs/timer-component-global.json', async () => {
    await delay(50)
    return HttpResponse.json(timerComponentConfig)
  }),
  http.get('*/configs/featured-offer.json', async () => {
    await delay(50)
    return HttpResponse.json(featuredOfferGlobal)
  }),
  http.get('*/configs/sidemenu-config-global.json', async () => {
    await delay(50)
    return HttpResponse.json(sidemenuGlobalConfig)
  }),
    http.get('*/configs/home-page-config-global.json', async () => {
    await delay(50)
    return HttpResponse.json(homePageGlobalConfig)
  }),
  http.get('*/configs/promotions-page-config-global.json', async () => {
    await delay(50)
    return HttpResponse.json(promotionsPageGlobalConfig)
  }),
]
