{"currency": {"separators": {"decimalSeparator": ".", "thousandSeparator": ","}}, "header_logos": ["test"], "logos_config": {"footerLogo": {"name": "casino days", "imgUrl": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Flogos%2Flogowhite.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "headerLogo": {"name": "casino days", "imgUrl": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Flogos%2Flogo.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "defaultFooterLogo": {"name": "casino days", "imgUrl": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Flogos%2Flogowhite.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "defaultHeaderLogo": {"name": "casino days", "imgUrl": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Flogos%2Flogo.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}}, "smart_banner": {"content": {"ios": {"title": "Casino Days: Online Casino", "subTitle": "GET - On the App Store", "buttonLabel": "GET"}, "android": {"title": "Casino Days: Online Casino", "subTitle": "FREE - Google Play Store", "buttonLabel": "Install"}}, "brandIcon": {"alt": "brand icon CD", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Ficons%2Fbrand-icon-cd.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "conditions": {"maximumLoginsTarget": 12, "minimumLoginsTarget": 2}, "appDownloadLink": "https://casinodays.onelink.me/FbE4/jsxs10ik"}, "industry_list": [{"label": "Agriculture, Maintenance, Repair, and Skilled Crafts", "value": "Agriculture, Maintenance, Repair, and Skilled Crafts"}, {"label": "Architecture and Engineering Occupations", "value": "Architecture and Engineering Occupations"}, {"label": "Business, Executive, Management, and Financial Occupations", "value": "Business, Executive, Management, and Financial Occupations"}, {"label": "Education, Training, and Library Occupations", "value": "Education, Training, and Library Occupations"}, {"label": "Healthcare Practitioners and Technical Occupations", "value": "Healthcare Practitioners and Technical Occupations"}, {"label": "Healthcare Support Occupations", "value": "Healthcare Support Occupations"}, {"label": "Other Professional Occupations", "value": "Other Professional Occupations"}, {"label": "Office and Administrative Support Occupations", "value": "Office and Administrative Support Occupations"}, {"label": "Services Occupations", "value": "Services Occupations"}, {"label": "Transportation Occupations", "value": "Transportation Occupations"}, {"label": "Other Occupations", "value": "Other Occupations"}], "limits_config": {"showNewFlow": true, "limitOptions": {"options": [{"label": "Loss limit", "value": "lossLimit"}, {"label": "Wager limit", "value": "wagerLimit"}, {"label": "Deposit limit", "value": "depositLimit"}], "defaultOption": "depositLimit", "lossLimitPeriods": {"periodOptions": [{"label": "Monthly", "value": "monthly"}, {"label": "Weekly", "value": "weekly"}]}, "wagerLimitPeriods": {"defaultOption": "daily", "periodOptions": [{"label": "Daily", "value": "daily"}, {"label": "Weekly", "value": "weekly"}, {"label": "Monthly", "value": "monthly"}]}, "depositLimitPeriods": {"defaultOption": "weekly", "periodOptions": [{"label": "Daily", "value": "daily"}, {"label": "Weekly", "value": "weekly"}, {"label": "Monthly", "value": "monthly"}]}, "lossLimitDefaultAmounts": [250, 350, 450, 550], "wagerLimitDefaultAmounts": [100, 200, 300, 400], "depositLimitDefaultAmounts": [300, 600, 900, 1200]}, "pauseOptions": {"options": [{"label": "Self Exclusion", "value": "selfExclusion"}, {"label": "Take a break", "value": "timeOut"}], "defaultOption": "timeOut", "timeOutPeriods": {"defaultOption": "24 hours", "periodOptions": [{"unit": "hour", "label": "24 Hours", "value": "24 hours"}, {"unit": "hour", "label": "48 Hours", "value": "48 hours"}, {"unit": "week", "label": "1 week", "value": "1 week"}]}, "selfExclusionPeriods": {"defaultOption": "24 hours", "periodOptions": [{"unit": "hour", "label": "24 Hours", "value": "24 hours"}, {"unit": "week", "label": "1 Week", "value": "1 week"}, {"unit": "month", "label": "1 Month", "value": "1 month"}]}}, "minWagerLimit": 10, "defaultAmounts": [400, 500, 700, 1000], "minDepositLimit": 10, "rgTimeoutPeriods": [{"unit": "hour", "duration": "24"}, {"unit": "week", "duration": 1}, {"unit": "hour", "duration": "72"}], "myAccountTimeoutPeriods": [{"unit": "hour", "duration": "24"}, {"unit": "week", "duration": "1"}, {"unit": "month", "duration": "1"}, {"unit": "month", "duration": "2"}, {"unit": "month", "duration": "5"}], "areExistingLimitsAvailable": true}, "wallet_config": {"bonus_fallback_image": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fgames%2Ffixed-rl.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "register_popup": {"steps": [{"interval": 10000, "intervalBefore": 15000}, {"interval": 10000}, {}], "imagePath": "/banners/splash-desktop-en-pop-up.jpg", "excludedPaths": ["/en/login", "/en/terms-and-conditions", "/game", "?login=true", "?bonus=true", "?terms=", "?privacy=true", "/register"]}, "cashback_config": {"card_background": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2F299d3fad-517b-4ebf-9311-e80c088850d5.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "dashboard_cards": {"bonus": {"card_background_image": {"alt": "", "src": "", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "card_thumbnail_fallback": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Frewards%2Fcd_generic_bonus.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "raffle": {"card_background_image": {"alt": "", "src": "", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "card_thumbnail_fallback": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Frewards%2Fcd_generic_bonus.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "cashback": {"card_background_image": {"alt": "", "src": "", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "card_thumbnail_fallback": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Frewards%2Fcd_generic_bonus.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "promotion": {"card_background_image": {"alt": "", "src": "", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "card_thumbnail_fallback": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Frewards%2Fcd_generic_bonus.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}, "splash_game": {"card_background_image": {"alt": "", "src": "", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "card_thumbnail_fallback": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Frewards%2Fcd_generic_bonus.jpg", "width": "100%", "device": "all", "height": "auto", "target": "all"}}}, "no_index_routes": [""], "brite_pnp_config": {"amountButtons": [30, 75, 300, 600], "defaultAmount": 150, "depositRoundUp": {"_49": 5, "_99": 10, "_999": 100, "default": 500}}, "occupations_list": {"Other Occupations": [{"label": "Military", "value": "Military"}, {"label": "Student", "value": "Student"}, {"label": "Unemployed", "value": "Unemployed"}, {"label": "Other Occupation", "value": "Other Occupation"}], "Services Occupations": [{"label": "Protective Service (e.g., Fire Fighting, Police Officer, Correctional Officer)", "value": "Protective Service (e.g., Fire Fighting, Police Officer, Correctional Officer)"}, {"label": "Chef or Head Cook", "value": "Chef or Head Cook"}, {"label": "Cook or Food Preparation Worker", "value": "Cook or Food Preparation Worker"}, {"label": "Food and Beverage Serving Worker (e.g., <PERSON><PERSON>, <PERSON>er, Waitress)", "value": "Food and Beverage Serving Worker (e.g., <PERSON><PERSON>, <PERSON>er, Waitress)"}, {"label": "Building and Grounds Cleaning and Maintenance", "value": "Building and Grounds Cleaning and Maintenance"}, {"label": "Personal Care and Service (e.g., Hairdresser, Flight Attendant, Concierge)", "value": "Personal Care and Service (e.g., Hairdresser, Flight Attendant, Concierge)"}, {"label": "Sales Supervisor, Retail Sales", "value": "Sales Supervisor, Retail Sales"}, {"label": "Retail Sales Worker", "value": "Retail Sales Worker"}, {"label": "Insurance Sales Agent", "value": "Insurance Sales Agent"}, {"label": "Sales Representative", "value": "Sales Representative"}, {"label": "Real Estate Sales Agent", "value": "Real Estate Sales Agent"}, {"label": "Other Services Occupation", "value": "Other Services Occupation"}], "Transportation Occupations": [{"label": "Aircraft Pilot or Flight Engineer", "value": "Aircraft Pilot or Flight Engineer"}, {"label": "Motor Vehicle Operator (e.g., Ambulance, Bus, Taxi, or Truck Driver)", "value": "Motor Vehicle Operator (e.g., Ambulance, Bus, Taxi, or Truck Driver)"}, {"label": "Other Transportation Occupation", "value": "Other Transportation Occupation"}], "Healthcare Support Occupations": [{"label": "Nursing, Psychiatric, or Home Health Aide", "value": "Nursing, Psychiatric, or Home Health Aide"}, {"label": "Occupational and Physical Therapist Assistant or Aide", "value": "Occupational and Physical Therapist Assistant or Aide"}, {"label": "Other Healthcare Support Occupation", "value": "Other Healthcare Support Occupation"}], "Other Professional Occupations": [{"label": "Arts, Design, Entertainment, Sports, and Media Occupations", "value": "Arts, Design, Entertainment, Sports, and Media Occupations"}, {"label": "Computer Specialist, Mathematical Science", "value": "Computer Specialist, Mathematical Science"}, {"label": "Counselor, Social Worker, or Other Community and Social Service Specialist", "value": "Counselor, Social Worker, or Other Community and Social Service Specialist"}, {"label": "Lawyer, Judge", "value": "Lawyer, Judge"}, {"label": "Life Scientist (e.g., Animal, Food, Soil, or Biological Scientist, Zoologist)", "value": "Life Scientist (e.g., Animal, Food, Soil, or Biological Scientist, Zoologist)"}, {"label": "Physical Scientist (e.g., Astronomer, Physicist, Chemist, Hydrologist)", "value": "Physical Scientist (e.g., Astronomer, Physicist, Chemist, Hydrologist)"}, {"label": "Religious Worker (e.g., <PERSON><PERSON><PERSON>, Director of Religious Activities or Education)", "value": "Religious Worker (e.g., <PERSON><PERSON><PERSON>, Director of Religious Activities or Education)"}, {"label": "Social Scientist and Related Worker", "value": "Social Scientist and Related Worker"}, {"label": "Other Professional Occupation", "value": "Other Professional Occupation"}], "Architecture and Engineering Occupations": [{"label": "Architect, Surveyor, or Cartographer", "value": "Architect, Surveyor, or Cartographer"}, {"label": "Engineer", "value": "Engineer"}], "Education, Training, and Library Occupations": [{"label": "Postsecondary Teacher (e.g., College Professor)", "value": "Postsecondary Teacher (e.g., College Professor)"}, {"label": "Primary, Secondary, or Special Education School Teacher", "value": "Primary, Secondary, or Special Education School Teacher"}, {"label": "Other Teacher or Instructor", "value": "Other Teacher or Instructor"}, {"label": "Other Education, Training, and Library Occupation", "value": "Other Education, Training, and Library Occupation"}], "Office and Administrative Support Occupations": [{"label": "Supervisor of Administrative Support Workers", "value": "Supervisor of Administrative Support Workers"}, {"label": "Financial Clerk", "value": "Financial Clerk"}, {"label": "Secretary or Administrative Assistant", "value": "Secretary or Administrative Assistant"}, {"label": "Material Recording, Scheduling, and Dispatching Worker", "value": "Material Recording, Scheduling, and Dispatching Worker"}, {"label": "Other Office and Administrative Support Occupation", "value": "Other Office and Administrative Support Occupation"}], "Healthcare Practitioners and Technical Occupations": [{"label": "Chiropractor", "value": "Chiropractor"}, {"label": "Dentist", "value": "Dentist"}, {"label": "Dietitian or Nutritionist", "value": "Dietitian or Nutritionist"}, {"label": "Optometrist", "value": "Optometrist"}, {"label": "Pharmacist", "value": "Pharmacist"}, {"label": "Physician", "value": "Physician"}, {"label": "Physician Assistant", "value": "Physician Assistant"}, {"label": "Podiatrist", "value": "Podiatrist"}, {"label": "Registered Nurse", "value": "Registered Nurse"}, {"label": "Therapist", "value": "Therapist"}, {"label": "Veterinarian", "value": "Veterinarian"}, {"label": "Health Technologist or Technician", "value": "Health Technologist or Technician"}, {"label": "Other Healthcare Practitioners and Technical Occupation", "value": "Other Healthcare Practitioners and Technical Occupation"}], "Agriculture, Maintenance, Repair, and Skilled Crafts": [{"label": "Construction and Extraction (e.g., Construction Laborer, Electrician)", "value": "Construction and Extraction (e.g., Construction Laborer, Electrician)"}, {"label": "Farming, Fishing, and Forestry", "value": "Farming, Fishing, and Forestry"}, {"label": "Installation, Maintenance, and Repair", "value": "Production Occupations"}, {"label": "Other Agriculture, Maintenance, Repair, and Skilled Crafts Occupation", "value": "Other Agriculture, Maintenance, Repair, and Skilled Crafts Occupation"}], "Business, Executive, Management, and Financial Occupations": [{"label": "Chief Executive", "value": "Chief Executive"}, {"label": "General and Operations Manager", "value": "General and Operations Manager"}, {"label": "Advertising, Marketing, Promotions, Public Relations, and Sales Manager", "value": "Advertising, Marketing, Promotions, Public Relations, and Sales Manager"}, {"label": "Operations Specialties Manager (e.g., IT or HR Manager)", "value": "Operations Specialties Manager (e.g., IT or HR Manager)"}, {"label": "Construction Manager", "value": "Construction Manager"}, {"label": "Engineering Manager", "value": "Engineering Manager"}, {"label": "Accountant, Auditor", "value": "Accountant, Auditor"}, {"label": "Business Operations or Financial Specialist", "value": "Business Operations or Financial Specialist"}, {"label": "Business Owner", "value": "Business Owner"}, {"label": "Other Business, Executive, Management, Financial Occupation", "value": "Other Business, Executive, Management, Financial Occupation"}]}, "general_tmb_badge": {"tag": "34", "watermark": "drops-and-wins.png"}, "live_spins_config": {"liveSpinsSlug": "livespins", "casinoCategoryId": 161, "liveSpinsSdkConfig": {"tenant": "CASINODAYS", "language": "EN", "serverConfig": {"ui": "https://play-stage.eu1.livespins.cloud", "api": "https://api-stage.eu1.livespins.cloud/stream"}}, "liveSpinsCategoryId": 149, "liveCasinoCategoryId": 149}, "pause_ticker_item": 2000, "registration_form": {"registerStepOne": {"id": "register-step-1", "name": "Register step 1", "fields": [{"id": "fullName", "name": "fullName", "label": "", "fields": [{"id": "firstName", "name": "firstName", "type": "text", "label": "labels.firstName", "component": "text", "placeholder": "labels.firstName", "validations": [{"type": "min", "params": [2, "errors.minCharsRequired"]}, {"type": "trim", "params": ["errors.firstNameRequired"]}, {"type": "required", "params": ["errors.firstNameRequired"]}], "validationType": "string"}, {"id": "lastName", "name": "lastName", "type": "text", "label": "labels.lastName", "component": "text", "placeholder": "labels.lastName", "validations": [{"type": "min", "params": [2, "errors.minCharsRequired"]}, {"type": "trim", "params": ["errors.lastNameRequired"]}, {"type": "required", "params": ["errors.lastNameRequired"]}], "validationType": "string"}], "classes": "name", "component": "field_group"}, {"id": "email", "name": "email", "type": "email", "label": "labels.emailAddress", "component": "text", "placeholder": "labels.emailAddress", "validations": [{"type": "email", "params": ["errors.emailInvalid"]}, {"type": "required", "params": ["errors.emailRequired"]}], "validationType": "string"}, {"id": "password", "name": "password", "type": "password", "label": "labels.password", "component": "password", "placeholder": "labels.password", "validations": [{"type": "password"}], "validationType": "custom"}, {"id": "country", "name": "country", "type": "select", "label": "labels.countryRegion", "component": "select", "optionsProp": "countries", "placeholder": "labels.countryRegion", "validations": [{"type": "min", "params": [2, "errors.alpha2Required"]}, {"type": "max", "params": [2, "errors.alpha2Required"]}, {"type": "required", "params": ["errors.countryRequired"]}], "validationType": "string", "initialValueProp": "countryValue"}], "component": "page"}, "registerStepTwo": {"id": "register-step-2", "name": "Register step 2", "fields": [{"id": "dob", "name": "dob", "label": "labels.birthdate", "fields": [{"id": "bDay", "name": "bDay", "type": "select", "label": "labels.day", "component": "select", "optionsProp": "days", "placeholder": "labels.day", "validations": [{"type": "required", "params": ["errors.required"]}], "validationType": "string"}, {"id": "b<PERSON>onth", "name": "b<PERSON>onth", "type": "select", "label": "labels.month", "component": "select", "optionsProp": "months", "placeholder": "labels.month", "validations": [{"type": "required", "params": ["errors.required"]}], "validationType": "string"}, {"id": "bYear", "name": "bYear", "type": "select", "label": "labels.year", "component": "select", "optionsProp": "years", "placeholder": "labels.year", "validations": [{"type": "required", "params": ["errors.required"]}], "validationType": "string"}], "classes": "birthday", "component": "field_group", "validations": [{"type": "dob", "params": []}], "validationType": "custom"}, {"id": "gender", "name": "gender", "type": "select", "label": "labels.gender", "component": "select", "optionsProp": "gender", "placeholder": "labels.gender", "validations": [{"type": "matches", "params": ["^(F|M)$", "errors.genderInvalid"]}, {"type": "required", "params": ["errors.genderRequired"]}], "validationType": "string"}, {"id": "address", "name": "address", "type": "text", "label": "labels.streetAddress", "component": "text", "placeholder": "labels.streetAddress", "validations": [{"type": "trim", "params": ["errors.addressRequired"]}, {"type": "required", "params": ["errors.addressRequired"]}], "validationType": "string"}, {"id": "cityZip", "name": "cityZip", "label": "", "fields": [{"id": "city", "name": "city", "type": "text", "label": "labels.city", "classes": "city", "component": "text", "placeholder": "labels.city", "validations": [{"type": "matches", "params": ["^((?!\\d+).)*$", "errors.cityShouldContainLetters"]}, {"type": "trim", "params": ["errors.cityRequired"]}, {"type": "required", "params": ["errors.cityRequired"]}], "validationType": "string"}, {"id": "zip", "name": "zip", "type": "text", "label": "labels.zipCode", "classes": "zip", "component": "text", "useParser": true, "placeholder": "labels.zipCode", "validations": [{"type": "min", "params": [3, "errors.zipCodeMinLength"]}, {"type": "required", "params": ["errors.zipCodeRequired"]}], "validationType": "string"}], "classes": "city-zip", "component": "field_group"}, {"id": "province", "name": "province", "type": "province", "label": "labels.province", "component": "select", "optionsProp": "province", "placeholder": "labels.province", "validations": [{"type": "province", "params": ["errors.provinceRequired"]}], "restrictions": {"enabledForCountry": ["ca", "in"]}, "validationType": "custom"}, {"id": "areaCountryCode", "name": "areaCountryCode", "type": "select", "label": "labels.countryCode", "component": "select", "optionsProp": "countryCodeOptions", "placeholder": "labels.countryCode", "validations": [{"type": "required", "params": ["errors.countryCodeRequired"]}], "validationType": "string", "initialValueProp": "country"}, {"id": "phone", "name": "phone", "type": "tel", "label": "labels.phoneNumber", "component": "text", "useParser": true, "placeholder": "labels.phoneNumber", "validations": [{"type": "min", "params": [5, "errors.phoneNumberMinLength"]}, {"type": "matches", "params": ["^[\\d \\-()]*$", "errors.phoneNumberInvalid"]}, {"type": "required", "params": ["errors.phoneNumberRequired"]}], "validationType": "string"}], "component": "page"}}, "user_details_form": {"fields": [{"id": "fullName", "name": "fullName", "label": "", "fields": [{"id": "firstName", "name": "firstName", "type": "firstName", "label": "labels.firstName", "component": "text", "placeholder": "labels.firstName", "validations": [{"type": "min", "params": ["2", "errors.minCharsRequired"]}, {"type": "required", "params": ["errors.firstNameRequired"]}], "validationType": "string"}, {"id": "lastName", "name": "lastName", "type": "text", "label": "labels.lastName", "component": "text", "placeholder": "labels.lastName", "validations": [{"type": "min", "params": ["2", "errors.minCharsRequired"]}, {"type": "required", "params": ["errors.lastNameRequired"]}], "validationType": "string"}], "classes": "name", "component": "field_group"}, {"id": "address", "name": "address", "type": "text", "label": "labels.streetAddress", "component": "text", "placeholder": "labels.streetAddress", "validations": [{"type": "required", "params": ["errors.addressRequired"]}], "validationType": "string"}, {"id": "industry", "name": "industry", "type": "select", "label": "labels.industry", "component": "select", "optionsProp": "industriesList", "placeholder": "labels.industry", "validations": [{"type": "required"}], "validationType": "string", "initialValueProp": "industry"}, {"id": "occupation", "name": "occupation", "type": "select", "label": "labels.occupation", "component": "select", "optionsProp": "occupations", "placeholder": "labels.occupation", "validations": [{"type": "required"}], "validationType": "string", "initialValueProp": "occupation"}, {"id": "phone", "name": "phone", "type": "tel", "label": "labels.phoneNumber", "component": "phone", "useParser": true, "optionsProp": "countryCodeOptions", "placeholder": "labels.phoneNumber", "validations": [{"type": "min", "params": ["5", "errors.phoneNumberMinLength"]}, {"type": "matches"}, {"type": "required", "params": ["required", "errors.phoneNumberRequired"]}], "validationType": "string", "initialValueProp": "country"}, {"id": "radio", "name": "radio", "type": "radio", "component": "radio", "validations": [{"type": "required"}], "radioButtons": [{"label": "labels.yes", "value": "YES"}, {"label": "labels.no", "value": "NO"}]}]}, "lobby_games_config": {"tags": {"popularSearchTagId": 37, "popularLiveSearchTagId": 40}, "categories": [{"categoryId": "7", "gtmEventName": "dfgddgfdgdgfgd"}, {"categoryId": "5", "gtmEventName": "lobby-popular-games-click"}], "lobbySections": {"popularLiveGamesSectionId": 7}}, "user_details_check": {"count": "2", "timeInterval": "days"}, "app_download_config": {"icon": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Ficons%2Fdownload-icon.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "limit": "5", "button": {"icon": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Ficons%2Fandroid.svg", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "link": "https://s3.eu-central-1.amazonaws.com/middleware.assets/production/casinodays/mobileApp/CasinoDays.apk", "type": "primary", "label": "Download Android APK", "fullWidth": false}}, "providers_tmb_badge": {"tvbet": "tvbet.png", "mgs_mga": "mga.png", "hub88_ebet": "ebet.png", "daily_drops": "drops-and-wins.png", "hub88_ezugi": "provider-ezugi-cd.png", "mgs_liveg24": "liveg24.png", "pragmaticplay": "prahmatic-play.png", "mgs_betgamestv": "betgamestv.png", "mgs_microgaming": "microgaming-real-deal.png", "hub88_asiagaming": "asiagaming.png", "hub88_evolutiongaming": "evolution-gaming.png", "hub88_lottoinstantwin": "lottoinstantwin.png", "hub88_superspadegames": "super-spade-games.png"}, "traffic_attribution": {"enabled": true}, "available_date_range": 6, "bonus_image_fallback": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2F1145ddf7-7cc7-43b0-aca4-314d6de6e95a.jpeg", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "popular_game_studios": {"0": {"key": "evoplayentertainment", "name": "EvoPlay"}, "1": {"key": "quickspin", "name": "Quickspin"}, "2": {"key": "nolimitcity", "name": "Nolimit City"}}, "responsible_gambling": {"pauses": {"timeout": {"options": [{"unit": "hour", "value": 24}, {"unit": "hour", "value": 48, "isDefault": true}, {"unit": "hour", "value": 72}], "isDefault": true}, "selfExclusion": {"options": [{"unit": "hour", "value": 24, "isDefault": true}, {"unit": "hour", "value": 48}, {"unit": "day", "value": 5}], "isDefault": true}}, "settings": {"loss": {"periods": {"daily": {"options": [100, 200, 300, 400, 500]}, "weekly": {"options": [100, 200, 300, 400, 500], "isDefault": true}, "monthly": {"options": [100, 200, 300, 400, 500]}}}, "wager": {"periods": {"daily": {"options": [100, 200, 300, 400, 500]}, "weekly": {"options": [100, 200, 300, 400, 500], "isDefault": true}, "monthly": {"options": [100, 200, 300, 400, 500]}}}, "deposit": {"periods": {"daily": {"options": [100, 200, 300, 400, 500]}, "weekly": {"options": [100, 200, 300, 400, 500], "isDefault": false}, "monthly": {"options": [100, 200, 300, 400, 500], "isDefault": true}}, "isDefault": true}}, "showNewFlow": true, "arePausesEnabled": true, "areSettingsEnabled": true, "areExistingLimitsAvailable": true}, "all_time_highs_config": {"maxItems": 10, "winMultiplier": "x30", "cardBackgroundImage": {"alt": "", "src": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging%2Fcasinodays%2Fimages%2Fdashboard-cards%2Fathbackground.jpeg", "width": "100%", "device": "all", "height": "auto", "target": "all"}, "placeholderCardLink": "casino"}, "dashboard_games_config": {"categories": [{"categoryId": "921", "gtmEventName": "lobby-popular-games-click"}]}, "live_spins_category_id": 161, "navigation_menu_mobile": {"gamePageMenuIcons": ["back", "quick-deposit", "dock", "more"], "loggedOutMenuIcons": ["search", "more"], "navMenuWithButtons": {"loggedOut": [{"item": "login", "isButton": true, "buttonType": "primary"}, {"item": "signup", "isButton": true, "buttonType": "secondary"}, {"item": "search"}, {"item": "more"}]}}, "search_categories_tabs": {"allGames": {"show": true}, "features": {"show": true, "excludedTags": [1, 3]}, "liveGames": {"show": true}, "gameStudios": {"show": true}}, "show_search_categories": true, "market_modal_background": "/banners/splash_banner_desktop.jpg", "cashier_disabled_by_tags": {"tags": ["DP/WD Block"], "enabled": true}, "brite_affiliate_pnp_config": {"655278": {"minimumAmount": 50}}, "is_enabled_welcome_page_v2": true, "show_crash_games_menu_item": false, "cashier_currency_separators": {"decimalSeparator": ".", "thousandSeparator": ","}, "limits_notification_time_out": 0, "is_game_jackpot_ticker_enabled": false, "last_played_games_category_row": 2, "lobby_low_balance_notification": {"depositTargets": {"depositSize": 30, "showLessDeposits": 5, "showMoreDeposits": 10}, "averageDeposits": {"eur": {"promptOnAmount": [{"promptOn": 3, "averageDepositMax": 30, "averageDepositMin": 0}, {"promptOn": 5, "averageDepositMax": 100, "averageDepositMin": 30}, {"promptOn": 10, "averageDepositMax": 250, "averageDepositMin": 100}], "promptOnPercentage": [{"roundUpTo": 10, "averageDepositMin": 250, "promptOnPercentage": 5, "averageDepositTarget": 100}]}, "usd": {"promptOnAmount": [{"promptOn": 3, "averageDepositMax": 30, "averageDepositMin": 0}, {"promptOn": 5, "averageDepositMax": 100, "averageDepositMin": 30}, {"promptOn": 10, "averageDepositMax": 250, "averageDepositMin": 100}], "promptOnPercentage": [{"roundUpTo": 10, "averageDepositMin": 250, "promptOnPercentage": 5, "averageDepositTarget": 100}]}, "default": {"promptOnAmount": [{"promptOn": 3, "averageDepositMax": 30, "averageDepositMin": 0}, {"promptOn": 5, "averageDepositMax": 100, "averageDepositMin": 30}, {"promptOn": 10, "averageDepositMax": 250, "averageDepositMin": 100}], "promptOnPercentage": [{"roundUpTo": 10, "averageDepositMin": 250, "promptOnPercentage": 5, "averageDepositTarget": 100}]}}, "notificationDuration": 5000}, "post_register_offer_modal_config": {"showPostRegisterOfferModal": false}}