{"generic": {"termsAndConditions": "Terms & conditions", "loadMore": "Load more", "readMore": "Read more", "myProfile": "My profile", "transactions": "Transactions", "identityVerification": "Identity verification", "accountClosure": "Account closure", "bonus": "Bonus", "balance": "Balance", "rewardsTitle": "Rewards", "rewardsDescription": "Welcome to your rewards section.", "confirmChange": "Save", "pickDuration": "Time period", "chooseReason": "Time out reason", "choosePeriod": "Choose a period", "passwordUpdated": "Your password has been updated", "passwordConfirmation": "Please enter your password, to confirm this action", "enterPassword": "Enter Password", "actionConfirmation": "Are you sure you want to perform this action?", "timeLeft": "Time left", "enterReason": "Enter reason", "closeAccount": "Close account", "welcomeToOurCasino": "CASINO GAMES", "welcomeToOurLiveCasino": "LIVE DEALER CASINO", "successful": "Completed successfully", "active": "Active", "history": "History", "notRecievedEmail": "Didn't receive an email?", "games": "Games", "account": "Account", "noRecordsFound": "No transactions", "playNow": "Play now", "openFreeAccount": "Open a free account now", "play": "Play", "playRealMoney": "Play real money", "demo": "Demo", "appUpdateOccurred": "Website update occurred", "gameStageBait": "You are playing for fun, <a href=\"?login=true\">Play for real instead!</a>", "depositWithBonusBtn": "Deposit and get bonus", "depositWithoutBonusBtn": "Deposit without bonus", "welcomeBonus": "Welcome bonus", "remaining": "remaining", "signedUp": "Signed up", "playForReal": "Sign up and play for real", "playForFun": "Try for fun", "yourBalance": "Your balance", "bets": "Bets", "payments": "Payments", "newUpdateAvailable": "New update available", "accept": "Accept", "linksSeparator": "/", "tryAgain": "Try again", "goToLobby": "Go to casino lobby", "noThanks": "No thanks", "welcomeName": "Welcome {{firstName}}", "claimSuccess": "Bonus claimed successfully", "helloName": "Hello {{firstName}}", "getBonusAndPlayForReal": "Get bonus and play for real", "loggedIn": "Logged in", "claimFailed": "Failed to claim bonus", "bonuses": "Bonuses", "instantBonusBuy": "Instant bonus buy", "pendingReview": "Pending review", "newRegulation": "New regulation", "confirmYourDetails": "Confirm your details", "sessions": "Sessions", "subscriptionUpdated": "<p>Changes have been saved. Bravo</p>", "kycPendingReviewCashierBlocked": "Uh oh! Seems like your ability to make a deposit, withdrawal or gameplay is temporarily restricted, a measure we have had to take due to regulatory requirements. We may need extra info or documents from you to get you back up and running again. Please contact our Customer Support team which will be more than happy to help you.", "depositNowBtn": "Deposit now", "setLimitsInfo": "You can set your limits here.", "newVersion": "A new version of the Casino Days app is available! To continue, please update the app to the latest version.", "myBalance": "My balance", "industryStepTitle": "In which line of business do you work?", "ontarioMigrationInfo": "As part of our transition to the local Ontario license we will need you to provide some additional information.<br /><br />Please answer a couple of easy questions about your employment.", "occupationStepTitle": "What is your role in the company?", "kycPendingReviewDescription": "Your KYC documents are pending review. Please contact customer support if you have any questions. Meanwhile some of the website functions will not be available.", "ontarioPepAndSanctionInfo": "Are you or is any member of your family or your close associate: <br /> <br/> \n<ul><li>A politicly exposed person</li><li>Head of international organization</li></ul>", "pepAreYouSureMessage": "By answering with Yes, your account might be restricted upon registration. Are you sure you want to continue?", "termsAndConditionsProfile": "Updated terms", "loginIfYouHaveAnAccount": "Already have an account? Log in", "and": "and", "verificationCodeSent": "A text message with a verification code has been sent. Enter the code here to verify your phone and get started!", "updateAvailable": "Update Available", "newVersionAvailable": "We are constantly improving our app. Update to the latest version {{version}} to get the best user experience.", "uploadFile": "Upload a File", "takePicture": "Take a Picture", "uploadPicture": "Upload a Picture", "newMessage": "You have a new message", "cookieUsage": "We use <a href=\"?privacy=true\">cookies</a> to improve site experience.", "responsibleGaming": "Responsible gambling?", "joinNow": "Join Now", "joinDraw": "Join draw", "open": "Open", "thankYou": "Thank you", "areYouPep": "Are you a PEP or a HIO?"}, "languages": {"en": "International (English)", "de": "German", "no": "Norway (Norwegian)", "nz": "New Zealand (English)", "in": "India (English)", "jp": "Japan (Japanese)", "za": "South Africa (English)", "fi": "Finland (Finnish)", "currentSelectedLanguage": "Your current language: English", "ca": "Canada (English)", "fr_ca": "Canada (French)", "th": "Thailand (Thai)", "oj_ca": "Ontario (English)", "cl": "Chile (Spanish)", "ee": "Estonia (Estonian)"}, "navigation": {"logout": "Log out", "signup": "Sign up", "livecasino": "Live Casino", "more": "More", "promotions": "Promotions", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "home": "Home", "search": "Search", "info": "Info", "chat": "Cha<PERSON>", "settings": "Settings", "help": "Help", "rewards": "Rewards", "cashier": "Cashier", "news": "News", "getMore": "Get more", "loyalty": "Loyalty", "faqs": "FAQs", "casinoGuide": "Casino guides", "slots": "Slots", "jackpot": "Jackpot", "games": "Games", "roulette": "Roulette", "blackjack": "Blackjack", "aboutUs": "About us", "contactUs": "Contact us", "termAndConditions": "Terms and conditions", "privacyPolicy": "Privacy policy", "bonus": "Bonus", "goToPromotions": "Go to promotions", "withdrawal": "<PERSON><PERSON><PERSON>", "viewBonus": "View bonus", "bonusPolicy": "Bonus policy", "instantBonus": "Instant bonus", "account": "Account", "verificationNeeded": "Verification needed", "visit": "Visit", "livegames": "Live games", "myaccount": "My account", "wallet": "Wallet", "userDashboard": "Home", "casino": "Casino", "login": "Log in", "cookiePolicy": "<PERSON><PERSON>", "preferences": "Preferences"}, "instantBonus": {"sortNewest": "Newest", "priceFrom": "From {{priceFrom}}", "noInstantBonusPrimaryBtn": "Deposit and continue playing", "noInstantBonusPrimaryLink": "?cashier=deposit", "buyBonusAndPlay": "Buy bonus and play", "instantBonusDescription": "Choose your bet and start with an instant bonus round straight away.", "bonusRoundCost": "Bonus round cost {{betRoundCost}}", "sortMostPopular": "Most popular", "sortPriceLowToHigh": "Price: low to high", "sortName": "Name A-Z", "infinite_spins_tag": "Infinite spins", "re_spins_tag": "Re-Spins", "colossal_wilds_tag": "Colossal wilds", "falling_symbols_tag": "Falling symbols", "sticky_wilds_tag": "Sticky wilds", "noInstantBonusTitle": "Temporary unavailable", "noInstantBonusText": "Instant bonuses aren't available at the moment, please retry a little bit later or refresh now.", "noInstantBonusSecondaryBtn": "Go to Casino Lobby", "instantBonusesTransactionsDescription": "Instant bonuses are special feature that allows you to play the bonus rounds of games without waiting for the bonus symbols to trigger the bonus feature.", "tryInstantBonus": "Try Instant bonuses now", "sortPriceHighToLow": "Price: high to low", "sortMaxExposure": "Highest payout potential", "disabledBonusRound": "These bonus rounds can be used once any prior bonus rounds have been completed.", "sort": "Sort", "filter": "Filter", "instantBonus": "Instant Bonus", "previouslyPurchased": "Previously purchased", "noInstantBonusSecondaryLink": "/casino", "instantBonusRounds": "instant Bonus Rounds", "mystery_symbols_tag": "Mystery symbols", "walkin_wilds_tag": "Walkin wilds", "scatters_tag": "<PERSON><PERSON><PERSON>", "stacked_symbols_tag": "Stacked symbols"}, "RhinoCashier": {"tether": "USDT - Tether", "buttonContinueWithFee": "{charge} - {feeAmount} fee", "standard": "Standard", "errorInvalidPassword": "Invalid Password", "errorInvalidEmail": "<PERSON><PERSON><PERSON>", "fieldPhCryptoCurrencyWalledId": "Wallet address", "fieldPhDestinationTag": "Destination Tag", "fieldPhBankBranchName": "Branch name", "errorInvalidBankBranchName": "Invalid Branch name", "fieldPhAccountName": "Account name", "errorInvalidAccountName": "Invalid Account holder name", "fieldPhNationalId": "National ID", "fieldPhAccountType": "Account type", "windhoek": "Windhoek", "errorInvalidFinancialInstitutionNumber": "Invalid Financial institution number", "validationMinErrorMessage": "Minimum {value} allowed", "fieldPhFinancialInstitutionNumber": "Financial institution number", "fieldPhTransitNumber": "Transit number", "errorInvalidTransitNumber": "Invalid Transit number", "groupMethodsNameOtherDeposits": "Other deposits methods", "groupMethodsNameOtherWithdrawals": "Other withdrawals methods", "contactChatWithUsSecondLine": "10:00 - 23:00", "whatIsYourFavoriteColor": "What is your favorite color?", "receiptButtonCloseCashier": "Go back", "whatIsTheNameOfYourHometown": "What is the name of your hometown?", "whatWasTheNameOfYourFirstPet": "What was the name of your first pet?", "bitcoinCash": "BCH - Bitcoin Cash", "litecoin": "LTC - Litecoin", "absa": "ABSA", "capitec": "Capitec", "fnb": "Fnb", "nedbank": "Nedbank", "investec": "Investec", "bidvest": "Bidvest", "tyme": "Tyme", "afribank": "Afribank", "oldmutual": "Oldmutual", "fieldPhBankName": "Bank name", "whatIsYourFavoriteAnimal": "What is your favorite animal?", "whatIsTheNameOfYourEldestCousin": "What is the name of your eldest cousin", "whatIsTheNameOfYourFirstRoommate": "What is the name of your first roommate?", "receiptDetails_bonusMoney": "Bonus money given", "bitcoin": "BTC - Bitcoin", "etherium": "ETH - Ethereum", "fieldPhUsername": "Username", "fieldPhPassword": "Password", "fieldPhVoucherNumber": "Voucher number", "errorInvalidVoucherNumber": "Invalid Voucher Number", "errorInvalidCommunityBankBranchCode": "Branch Code required", "fieldPhBankCode": "Banks", "errorInvalidBankCode": "Bank required", "fieldPhCommunityBankBranchCode": "Branch Code", "EW": "E-Wallets", "CR": "Crypto", "BT": "Bank Transfer", "CA": "Cards", "RC": "Recommended", "VO": "Vouchers", "OT": "Other", "deposit": {"pspcode": {"2": "Transaction failed due to blocked account. Please check your email for more information"}}, "netellerSecondaryInfo": "secondary info content", "receiptWithdrawalInfoMessageSuccess": "Your withdrawal has been reversed", "receiptWithdrawalInfoMessageError": "Unable to cancel your withdrawal", "receiptButtonCancelWithdrawal": "Cancel withdrawal", "webredirectblixtpaydepositSecondaryInfo": "en -row market", "skrillSecondaryInfo": "Skrill Info ROW", "bonusEligibilityInfo": "Deposit at least {value} to get the bonus"}, "errors": {"required": "This value is required", "unexpected": "Unexpected error", "problemLoadingGame": "There was a problem loading the game.", "problemLoadingCashier": "There was a problem loading cashier", "unableToFindGame": "Unable to find a game.", "problemLoadingPage": "There was a problem loading the page", "pageNotAvailableHeaders": "Sorry!", "pageNotAvailableTitle": "The website is not available", "pageNotAvailableCaption": "The website not available", "pageErrorHeaders": "Unexpected error!", "pageErrorTitle": "The page is currently unavailable", "pageErrorCaption": "The page is currently unavailable, please try again later.", "serverError404Headers": "Sorry!", "serverError404Title": "The website is not available", "serverError404Caption": "The website not available", "serverError500Headers": "Unexpected error!", "serverError500Title": "The page is currently unavailable", "serverError500Caption": "The page is currently unavailable, please try again later.", "credentialsMismatch": "Email and/or password is incorrect. Please try again.", "passwordMinlength": "Password should contain at least 8 characters", "passwordRequireNumber": "Password should contain numbers", "passwordRequireSymbol": "Password should contain at least 1 special character", "passwordContainUpperCase": "Password should contain at least 1 uppercase letter", "passwordRequired": "Password is required", "emailInvalid": "<PERSON><PERSON> is invalid", "emailExists": "This email already exists", "emailRequired": "Email is required", "firstNameRequired": "Firstname is required", "lastNameRequired": "Lastname is required", "phoneNumberRequired": "Phone number is required", "phoneNumberExists": "Phone number already exists", "countryCodeRequired": "Country code is required", "loadGames": "An unexpected error occurred while loading games, please try refreshing the page or contact customer support.", "wrongPassword": "You entered a wrong password", "passwordMatch": "Passwords doesn't match", "somethingWentWrong": "Something went wrong", "unableToComplete": "Unable to complete the current action", "monthlyGreaterThanWeekly": "Monthly limit is lower than weekly limit", "weeklyGreaterThanDaily": "Weekly limit is lower than daily limit", "accountLockedDueToRG": "Your account is blocked, please contact support", "zipCodeRequired": "Enter zip/postal code", "countryRequired": "Please select a country", "cityRequired": "Enter city", "addressRequired": "Enter address", "invalidDate": "Date should be in DD/MM/YYY format", "dob18Required": "You have to be at least 18 years old", "dobRequired": "Date is required", "genderRequired": "Please make a selection", "genderInvalid": "Gender should be either male or female", "accountNotFoundDueToClosure": "Your Account is closed. If you wish to reopen, contact Customer Support.", "invalidBonusId": "Sorry, invalid bonus id", "inactiveBonusId": "Sorry, inactive bonus id", "invalidOfferAmount": "Sorry, invalid offer amount", "passwordResetToken": "The password link is old, please request a new one to reset your password.", "fetchingTransactionsHistoryFailed": "Could not load transactions history.", "pageNotFound": "We can't seem to find the page you're looking for.", "missingGameUrl": "This game is currently not available, please try again or contact customer support.", "genericGameLoadError": "An error occurred while starting the game, please try again or contact customer support.", "numberRequired": "Enter amount in numbers", "pendingWeeklyLimit": "Unable to complete due to pending weekly limit", "pendingDailyLimit": "Unable to complete due to pending daily limit", "pendingMonthlyLimit": "Unable to complete due to pending monthly limit", "minCharsRequired": "Minimum 2 characters required", "cityShouldContainLetters": "City should contain only letters", "phoneNumberInvalid": "Phone number has invalid format", "phoneNumberMinLength": "Phone number should have at least 5 digits", "phoneNumberMaxLength": "Phone number should have maximum 15 digits", "noPromotions": "There are currently no available promotions.", "notFound": "Content not found.", "alpha2Required": "Invalid code", "authenticationError": "Email and/or password is incorrect. Please try again", "invalidParameters": "Invalid parameters", "loginFailedTitle": "<PERSON><PERSON> failed", "loginFailedMessage": "An unexpected error occured. Please try logging in again.", "pnpPlayerExists": "User with this email address has been already registered.", "pnpAmountOutOfRange": "The selected amount is out of range. Please try again.", "pnpDepositNotAllowed": "Deposit is not allowed. Please try again or contact Customer Support.", "pnpDepositLimitExceed": "Deposit limit are exceeded. Please try again or contact Customer Support.", "pnpIncorrectTransactionAmount": "Incorrect amount is selected. Please try again.", "pnpNotExistingUser": "User doesn't exists. Please try again or contact Customer Support.", "pnpCoolOff": "Your account is in cool off period. Please, contact Customer Support.", "pnpClosed": "Your account is closed. If you wish to reopen, contact Customer Support.", "cancelWithdrawal": "An unexpected error occurred during withdrawal cancel request", "somethingWentWrongMessage": "Something went wrong here. Please try and refresh the page.", "backToHome": "Back to home", "backToGames": "Back to games", "reloadPage": "Reload page", "pageNotFoundTitle": "Oops!", "pageNotFoundMessage": "We can't seem to find the page you're looking for.", "gameNotFoundTitle": "Oh snap!", "gameNotFoundMessage": "We can't seem to find the game you're looking for.", "kycFailed": "Your KYC validation failed. Please contact Customer Support.", "profileVerificationSessionExpired": "The code has expired.", "noPromotionsTitle": "Oh, no promotions", "duplicateEmail": "This email already exists", "duplicateUsername": "This username already exists", "insufficientFunds": "Your balance is too low, please make a <a href=\"?cashier=deposit\">deposit</a> or decrease the bet size", "gameplayLocationServiceMessage": "Please enable your web location services", "fileUploadSessionExpired": "Request failed as the session is expired.", "fileUploadExternalEcr": "The request contains External ECR ID of another partner.", "fileUploadProfileIncomplete": "Request failed as the profile is incomplete.", "tooManyFiles": "The file {{fileName}} could not be uploaded. The maximum number of files is {{maxFiles}}", "fileUploadCorrupted": "The file didn’t upload properly or the file is corrupted, please upload the file again.", "fileUploadInvalidRequest": "The request contains an invalid data.", "fileUploadInvalidPartnerId": "The request contains an invalid Partner ID.", "fileUploadInvalidRefreshToken": "The request contains an expired or invalid Refresh <PERSON><PERSON>.", "fileUploadInvalidPartner": "The request is not allowed for a specific Partner.", "fileUploadInvalidExternalEcrId": "The request contains an invalid External ECR ID.", "fileTooLarge": "The file {{fileName}} could not be uploaded. File size is larger than {{maxSize}}.", "fileInvalidType": "The file {{fileName}} could not be uploaded. Only files of type PNG, JPG or PDF can be uploaded.", "fileApiFailed": "Something went wrong", "minAmountIsZero": "The minimum amount is 0.", "depositWeeklyMoreThanDailyLimit": "Your weekly limit must be higher than daily.", "depositMonthlyMoreThanWeeklyLimit": "Your monthly limit must be higher than weekly and daily.", "lossMonthlyMoreThanWeeklyLimit": "Your monthly limit must be higher than weekly and daily.", "lossWeeklyMoreThanDailyLimit": "Your weekly limit must be higher than daily.", "limitsAlreadySet": "Limits are already set.", "wagerMaxLimit": "You have reached max spend limit", "lossMaxLimit": "You have reached max loss limit", "mfaEnableFailedTitle": "Method failed", "verificationCodeLength": "Code must be exactly 6 digits", "betweenLimit": "A number between {{min}} and {{max}} is accepted", "loginFailedMessageWithTimer": "An unexpected error occured. Please try logging in again in {{timer}} seconds.", "otpMfaInvalid": "The code is incorrect.", "otpMfaNotEnabled": "Multi factor authentication is not enabled", "otpSessionExpired": "Your verification session has expired. Please login again", "registrationDisabledTitle": "Registration not allowed", "vpnUsageTitle": "Oh snap!", "gameplayLocationBlockTitle": "Uh oh!", "errorOccurredTryAgain": "Sorry, an error occurred while logging in. Please try again or contact us.", "xpointAppNotRunning": "In order to start playing you need to have Xpoint Verify installed and running.", "fileUploadLimit": "File upload limit is reached. Please make sure you are uploading maximum {{maxFiles}} files.", "xpointOutOfState": "We have detected that you are attempting to wager from outside the province of Ontario. Unfortunately, due to regulatory reasons, we cannot offer our casinodays.com/on services to you. Our services will be available when you are physically located in the province of Ontario.  If you belive this not to be your case, please contact our customer support team.", "xpointNoEnoughData": "We cannot verify if you are within the permitted area of Ontario. To help us verify your location, make sure your Location Services is turned on and that your WiFI connection is stable. Please address items above, then try again. If you belive this not to be your case, please contact our customer support team.", "somethingWentWrongTitle": "Uh oh!", "xpointCloseToBorder": "You’re close to a border area so we cannot verify that you are within the permitted area of Ontario. To help us verify your location, make sure your WiFi is turned on and that your Wifi connection is stable. Kindly address items above, then try again.  If you belive this not to be your case, please contact our customer support team.", "isDocumentTypeSelected": "Please select value from the dropdown below.", "xpointPotentialFraud": "For security purposes, your account has been flagged for potential fraud. Please be aware that it is illegal to bet or wager from outside the permitted area of Ontario. We may unflag your account if our fraud team determine this activity was inadvertent.  If you belive this not to be your case, please contact our customer support team.", "xpointAppNotInstalled": "In order to start playing you need to have Xpoint Verify installed and running. Download and install XPoint Verify below.", "provinceRequired": "Please select a province", "dashboardNotFoundTitle": "Dashboard not found", "dashboardNotFoundMessage": "This page isn't available at the moment, please refresh the page or try later.", "invalidNationalId": "Wrong national ID", "minimumLimit": "The minimum deposit limit is {{translationValue}}", "invalidStateName": "Invalid state name. Please try again", "passwordContainWhitespace": "Password must not contain whitespaces", "mfaFailedMessage": "An unexpected error occured. Failed to enable the authentication method. Please try again", "gameplayLocationBlockMessage": "Gameplay is available only for users physically located in Ontario.", "closedForAml": "Your account has been blocked due to regulatory requirements. Please check your inbox/spam folder for information.", "profileVerificationInvalidCode": "The code is incorrect.", "profileVerificationMaxLimitReached": "Maximum attempts limit reached. Please request new verification code.", "profileVerificationNotInitiated": "Profile verification is not initiated", "depositMaxLimit": "You have reached max deposit limit", "rafflesClaimFailed": "Failed to claim Raffle {{name}}", "pendingLimits": "Unable to complete due to pending limit", "authFailedOutsideLegislation": "You were not logged in successfully, as it was detected that you are not physically located in Ontario.", "mfaInvalidPhoneNumber": "The mobile number you’ve entered is not correct. In order to use SMS authentication your mobile number must match the number of your account.", "vpnUsageMessage": "You seem to be using a VPN or proxy service. To participate in gameplay, please turn off any of these services and try again.", "noPromotionsSecondaryBtn": "See available rewards", "gameplayBlocked": "Uh oh! Seems like your gameplay might have been temporarily restricted, a measure we've had to take due to regulatory requirements. We may need extra info or documents from you to get you back up and playing again. Please contact our support team which will be more than happy to help", "fileDuplicate": "{{nameOfDuplicateFile}} already exists,  please try again", "confirmFitToPlay": "Please confirm that you are able to play before loggin in", "unexpectedError": "An unexpected error has occurred, please contact Customer Support.", "gameplayLocationServiceOffTitle": "Your location services are off", "enableLocationServices": "If you continue experiencing issues with verifying your location, please be aware, that in some cases, your device's location services also need to be enabled - you can find how to do this in our FAQ - or feel free to contact us <a href='/help'>here</a>. Let the fun begin", "reloadApp": "Reload app", "unableToCompletePending": "Unable to complete the current action due to pending request", "limitsRangeErrorGeneric": "The amount must be higher than the previous and lower than the next limit.", "registrationDisabled": "Unfortunately, registration is not allowed.", "monthlyGreaterThanDaily": "Monthly limit is lower than daily limit", "registrationCompletedWithErrors": "Registration completed with errors, please go to the account section to complete your profile.", "zipCodeMinLength": "Zip code should contain at least 3 characters", "registrationCompletedWithErrorsLogin": "Registration completed with errors, please login and complete your profile.", "linkedUserInTheSamePartner": "Your registration was successful but your accounts has been automatically blocked. It seems that you might have an account with us already. Please reach out to our customer service team for support", "duplicateScreenName": "The email address provided has already been used.", "pnpRgLimitsExceed": "Responsible gambling limits are exceeded. Please contact Customer Support.", "internalUser": "User is internal user", "netLimitsExceeded": "Net limits are exceeded. Please contact Customer Support.", "networkError": "Network error", "inputInvalid": "Invalid input", "inputRequired": "Field is required"}, "labels": {"name": "Full name", "firstName": "First name", "lastName": "Last name", "username": "Username", "address": "Address", "streetAddress": "Street address", "phone": "Phone", "phoneNumber": "Phone number", "city": "City", "postalCode": "Postal code", "country": "Country", "countryRegion": "Country/Region", "countryCode": "Country code", "email": "Email", "emailAddress": "Email address", "gender": "Gender", "dob": "Date of birth", "birthdate": "Birthdate", "state": "State", "zipCode": "Zip code", "directEmail": "Direct email", "sms": "SMS", "password": "Password", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm new password", "signup": "Sign up", "languages": "Languages", "useful": "Useful", "general": "General", "showLess": "Show less", "hide": "<PERSON>de", "show": "Show", "personalDetails": "PERSONAL DETAILS", "changePassword": "Change password", "marketing": "RECEIVE PROMOTIONS BY", "timeOut": "TIME OUT", "selfExclusion": "SELF EXCLUSION", "realityCheck": "REALITY CHECK", "amount": "Amount", "day": "Day", "days": "Days", "hour": "Hour", "hours": "Hours", "minute": "Minute", "minutes": "Minutes", "second": "Second", "seconds": "Seconds", "month": "Month", "months": "Months", "year": "Year", "years": "Years", "permanently": "Permanently", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "expires": "Expires", "notExpires": "Does not expire", "close": "Close", "confirm": "Confirm", "save": "Save", "saveNewPassword": "Save new password", "left": "left", "submit_btn": "Submit changes", "logout": "Log out", "male": "Male", "female": "Female", "cancel": "Cancel", "resend": "Resend", "chat": "Cha<PERSON>", "forefit": "Cancel bonus", "games": "Games", "popularSearch": "Popular Searches", "clear": "Clear", "welcome": "Welcome!", "back": "Back", "messages": "Messages", "marketingPromotions": "Receive promotions, offers and updates from Casino Days.", "seeAll": "See all", "seeLess": "See less", "showMore": "Show more", "seeAvailableBonuses": "See all available bonuses", "depositOrWithdrawMoney": "Deposit or withdraw money", "seeAllGames": "See all games", "yes": "Yes", "changeEmail": "Change email", "removeLimits": "Remove limits", "viaEmail": "via Email", "startPlaying": "Start playing", "viaSMS": "via SMS", "continue": "Continue", "verifyEmail": "Verify email", "chatWithUs": "Chat with us", "chooseLanguage": "Choose language", "quit": "Quit", "language": "Language", "saveNewLanguage": "Save new language", "change": "Change", "purchased": "Purchased", "verificationSubmit": "Please submit the documents requested to securely verify your information.", "resendResetLink": "Resend reset link", "set": "Set", "noLimit": "No limit", "setNewLimit": "Set new limit", "depositLimit": "Deposit limit", "lossLimit": "Loss limit", "setYourLimits": "Set your limits", "connect": "Connect", "verificationCode": "Verification code", "verifyAndLogin": "Verify and log in", "secretKey": "Secret key", "secretKeyCopied": "Secret key copied", "you": "you", "changeLanguage": "Change", "verifyAccount": "Verify account", "verifyPhone": "Verify phone", "claimNow": "Claim Now", "ohSnap": "Oh snap!", "buyMoreBonusRounds": "Buy more bonus rounds", "bonusRounds": "Bonus rounds", "update": "Update", "province": "Province", "allGameStudios": "All game studios", "allFeatures": "All features", "bonusProgress": "Bonus progress", "addMoney": "Add money", "cashGuarantee": "Cash guarantee", "anyGame": "Any game", "setLossLimits": "Set your loss limits", "continueToChat": "Continue to chat", "resendCode": "Resend code", "add": "Add", "remove": "Remove", "companyName": "Company name", "businessAddress": "Business address", "businessPhoneNumber": "Business phone number", "skip": "<PERSON><PERSON>", "optional": "Optional", "clearAll": "Clear All", "currentLimit": "Current limit", "amountBeforeLimitReached": "Amount before limit is reached", "no": "No", "nextTime": "Next time", "changePhoneNumber": "Change phone number", "expiresIn": "Expires in", "seeAllPendingWd": "See all pending withdrawals", "qualifingGames": "Qualifing games", "verificationInfoMessage": "Verify your account", "showAll": "Show all", "timeout": "TIME OUT", "luckyDraw": "Lucky Draw", "endsIn": "Ends in", "goBack": "Go back", "abbrHours": "h", "abbrDays": "d", "abbrMinutes": "m", "abbrSeconds": "s", "chooseLanguageApp": "Continue to Casino Days in", "updateAndVerify": "Update and verify", "wagerLimit": "Spend limit", "abbrYears": "y", "abbrMonths": "mo", "players": "players", "sports": "Sports", "casinoDays": "Casino Days", "casino": "Casino", "help": "Help", "moreInfo": "More Info", "removeLimit": "Remove limit", "changeLimit": "Change limit", "cashBalance": "Cash balance", "industry": "Industry", "occupation": "Occupation", "updateNow": "Update Now"}, "login": {"forgotPassword": "Forgot password?", "notRegistered": "Don't have an account?", "ontarioFitToPlay": "I confirm that I am fit to play.", "backToLogin": "Back"}, "footer": {"gameProviders": "The Best Game Studios", "paymentProviders": "Secure Payment Solutions", "connect": "Connect", "trademark": "Casino Days All rights reserved", "disclaimer": "{{brandName}} is owned and operated by White Star B.V. that is incorporated under the laws of Curacao with company registration number 153150 and having its registered address at Korporaalweg 10, Curacao.  White Star B.V. is operating under E-gaming license No. 8048/JAZ2021-089 issued by Antillephone N.V. authorized by the government of Curacao. Privado Ltd. is a company incorporated under the laws of Cyprus with company registration number HE 417876 and having its registered address at Chytron, 1, Flat/Office 301, 1075, Nicosia, Cyprus. Privado Ltd. is a facilitating company for White Star B.V. White Star BV, is pending application by the GCB.<br /><br /> Persons under 18 years of age are not permitted to gamble.<br /><br /> If you want to get in touch with us, drop us an <NAME_EMAIL>", "certificates": "Certificates"}, "registration": {"rewardTitle": "Get your 100% match bonus up to $1,000!", "errorHeader": "Registration error", "headerStepOne": "Sign up", "headerStepTwo": "Finish signing up", "subheaderStepOne": "Create your account in two easy steps. It only takes a minute.", "subheaderStepTwo": "Hey {{firstName}}, just a couple of more details and you're ready to start playing.", "buttonStepOne": "Agree and continue", "buttonStepTwo": "Complete and start playing", "marketingSubscriptionsMessage": "Casino Days will send you special offers by email and SMS. You can opt out of receiving these at any time in your account settings.", "marketingSubscriptionCheckbox": "I don't want to receive marketing from Casino Days.", "privacyAndTerms": "By selecting Agree and continue below, I agree to Casino Days <a href=\"?terms=true\" preventLocationCache=\"true\">Terms and Conditions</a> and <a href=\"?privacy=true\" preventLocationCache=\"true\">Privacy Policy</a>. Copies of these are available upon request.", "downloadTnc": "Download Terms and Conditions", "sideDescription": "<div>Over 5000 games</div><div>Safe and secure</div><div>Fast payments</div><div>Support 24/7</div>", "backToSignUp": "Back to sign up", "completeSignUp": "Leaving now and you’ll miss out on all the fun. Our quick sign up takes less than a minute to complete.", "hangOn": "Hang on a sec!"}, "search": {"startTyping": "Start Typing...", "noResultTitle": "Ooops!", "searching": "Searching...", "noResult": "We can‘t find any matches for '{{searchTerm}}'.  Make sure you spelled correctly or test with a different keyword.", "noAvailableGames": "Please type in to search for games", "gamesNumber": "{{gamesNumber}} games"}, "games": {"all": "All Games", "cdFreeSpins": "Free Spins", "cdLiveWheels": "Fortune Wheels", "cdLotto": "Lotto", "cdPopularCasino": "Top Games in Canada", "cdPopularLive": "Popular live games in Canada", "cdMegaways": "Megaways", "cdHighRoller": "Epic wins over x10,000", "cdJackpots": "Jackpots", "cdOcean": "Stories from the Sea", "cdBonusWheels": "Bonus Wheels", "cdFallingSymbols": "Falling Symbols", "cdWinBothWays": "Win Both Ways", "cdRoulette": "Roulette", "cdBlackjack": "Blackjack", "cdCards": "Live poker & card games", "cdLiveLobby": "Live studio collection", "cdDice": "Live dice games", "cdBoard": "Board", "cdBaccarat": "Baccarat", "cdOtherLive": "Other live games", "cdNew": "New releases", "cdTournament": "Tournaments", "cdSupernatural": "Supernatural & fairy tales", "cdClassic": "Classic fruit machines", "cdIndia": "Indian games", "cdClusterPays": "Cluster pays", "cdRomeEgypt": "Rome & Egypt", "lastPlayed": "Last played", "oneSessionAssertion": "You can only have one game session active on any given time. Please close your active game session before playing another game.", "closeOtherSessions": "Close other game sessions", "backToGames": "Back to games", "noGamesInSection": "There are no games in this section yet.", "splashTechTypeQUIZ": "Quiz", "splashTechTypeSLOT": "Slot"}, "seo": {"logoAltText": "Casino Days logo", "defaultTitle": "Play the Best New Casino Games Online", "titlePostfix": "| Casino Days", "gameStageDescription": "Play {{gameName}} Slot Game Online for Real Money! Join Casino Days today and claim your free bonus up to €100 and win big in our Jackpots!", "accountPageTitle": "Account", "profilePageTitle": "Profile", "transactionsPageTitle": "Transactions", "bonusesPageTitle": "Bonuses", "responsibleGamingPageTitle": "Responsible gaming", "accountClosurePageTitle": "Account closure", "identityVerificationPageTitle": "Identity verification", "defaultDescription": "Choose from our wide selection of exciting new online slots and action games and win big today! Join now and get a free deposit bonus for new players.", "gameTitle": ": Play with Real Money"}, "realityCheck": {"modalHeader": "Your recent game activity", "timePlayed": "Time Played:", "totalBets": "Bets placed:", "totalWins": "Bets won:", "stopButton": "Stop Playing", "transactionButton": "Game History", "acknowledgeButton": "Close"}, "responsibleGaming": {"activateDepositLimit": "Active deposit limits", "waitingActivation": "Waiting activation", "setLimit": "<PERSON>", "setTimeout": "Set Timeout", "setExclusion": "Set Exclusion", "chooseinterval": "Select an interval", "depositLimit": "Here you can set and change financial and session limits at any time. The deposit limits are independent of the maximum limits mentioned in the deposit section. You can reduce your financial limits here, with immediate effect. If you wish to remove or increase your financial limits, a 24 hour waiting period comes into place. To discuss further changes to your limits please contact our Customer Support. <br/><br/> Maximum Limit: €10,000. If you wish to further increase your limits please contact support", "timeOut": "If you wish to take a short break, you can do so by selecting one of the time periods below. You will not be able to log in to this account for the duration of the chosen period. <br/><br/> Once your time out period has ended, your account will automatically become accessible.", "selfExclusion": "Self-exclusion allows you to take a break from gambling, whether you just want to restrict your play or if it becomes something else other than fun and entertainment. You will not be able to access your account during the self-exclusion period. <br/><br/> Once the self-exclusion period has expired, your accounts are re-opened automatically. <NAME_EMAIL> to set longer self-exclusion periods.", "realityCheck": "The Reality Check allows you to display an on-screen notification which shall appear at regular intervals according to the set time of your choosing. When the notification appears, you can either continue playing or stop your gaming session", "setLimits": "Set limits", "postRegLimitsHeadline": "To enjoy a safe and responsible online casino experience, you need to set your limits to control your gambling. This can be change in your account at any time."}, "timeoutReason": {"temporaryClose": "I'd like to close my account temporarily", "notEnoughTime": "I don't have time to play at the moment", "differentCasino": "I would like to play with a different casino at the moment", "noreason": "No particular reason"}, "accountClosure": {"visitSelfExclusionDescription": "If you believe that you have or may be developing a gambling problem, you are kindly requested to visit our", "selfExclusionPage": "self-exclusion page", "goodbyeMessage": "We value your participation in the Casino Days community and we are sad to see you go.", "readInstructionsForClose": "Please read the following instructions and select a reason for closing your account", "settleBetsCondition": "Any open bets need to be settled prior to closure of account", "balanceWithdrawnCondtion": "Outstanding balance needs to be withdrawn (if you have a balance less than the minimum withdrawable balance please contact customer support)", "activeBonusesCondition": "Any active bonuses need to be forfeited", "personalDataCondition": "Following closure of account, your personal data will be processed in accordance with our Data Retention Policy found within the Privacy Notice. All additional personal data not required to comply with legal obligations will be anonymised or erased.", "reasonForClosing": "Please enter the reason for closing the account (Optional)", "balanceValidationError": "Your account can not be closed, as your balance is greater then 0.", "bonusesValidationerror": "You have active bonuses, the account can not be closed."}, "faq": {"searchEmptyTitle": "Sorry!", "title": "Help", "header": "How can we help you?", "popularTitle": "Popular Articles", "guidesTitle": "Guides", "searchTitle": "What can we help you with today?", "searchPlaceHolder": "Search help articles", "searchPopularTopics": "Popular topics:", "searchResults": "Results for '{{searchTerm}}'", "searchEmptyText1": "We looked everywhere, but could not find anything matching your search '{{searchTerm}}'.", "searchEmptyText2": "Try a different search, or browse our <a href=\"{{faqLink}}\">help topics</a> instead.", "showMoreButton": "Show more", "contactUsButton": "Contact us", "chatButton": "Cha<PERSON>", "emailButton": "Email", "emailUs": "Email us", "moreHelp": "Do you need further help?", "chatWithUs": "Chat with us 24/7", "chatInfo": "Our chat is open 24/7", "searchEmptyText2Mobile": "Try a different search or browse our help topics instead", "contactEmailUs": "Email us"}, "missingProfile": {"header": "We didn't manage to save all your details during registration, please complete the form to access your account:", "modalHeader": "Complete Profile"}, "calendar": {"invalidDateMessage": "Invalid date format", "invalidLabel": "Invalid date", "maxDateMessage": "Date should not be after maximal date", "minDateMessage": "Date should not be before minimal date", "cancelLabel": "Cancel", "clearLabel": "Clear", "okLabel": "OK", "todayLabel": "Today", "bonusDateFilterTitle": "Bonuses between:", "from": "From", "to": "To", "selectDate": "Select date"}, "tablePagination": {"previousPage": "Previous page", "rowsPerPage": "Results per page:", "nextPage": "Next page", "ofTotalPages": "of", "moreThan": "more than"}, "bonus": {"date": "Date", "name": "Name", "status": "Status", "noActiveBonuses": "No active bonuses.", "noHistoryBonuses": "No history bonuses.", "statusCompleted": "Completed", "statusDropped": "Cancelled", "statusAvailable": "Ongoing", "statusExpired": "Expired", "statusInactive": "Inactive", "currentAmount": "Current amount", "wagered": "Wagered", "wageringLeft": "Wagering left", "wageredPrice": "{{wagered}} of {{wageringLeft}}", "initialWagering": "Initial wagering", "expires": "Expires", "forfeitBonusQuestion": "Are you sure that you want to forfeit your bonus?", "forfeitBonus": "Forfeit bonus", "moneyWager": "Bonus money to wager", "cashToBeReleased": "Cash to be released", "noRewardsContentType": "text", "noRewardsBtn": "Deposit and Play", "onlyTodayLabel": "Only today", "availableBonusTitle": "Available bonuses", "imgAlt": "Bonus card image", "noRewardsLink": "?cashier=deposit", "getBonus": "Get bonus", "activeBonusDescription": "If you have a balance consisting of real money and bonus money, real money will always be used first.", "bonusMoneyDescription": "Bonuses of the type \"bonus money\" are bonuses that are added to your balance once they have been claimed. Bonus money must be wagered before any withdrawal can be made, once wagering conditions have been met the amount left of the bonus will be given as real cash. Only bonus money for current bonus counts towards the wagering conditions", "cashReloadDescription": "Bonuses of the type \"cash reload\" are bonus guarantees. The complete cash balance to be released will be given to you in real cash upon wager completion. Only real money wager counts towards the wagering conditions.", "noBonusesDescription": "When you claim a bonus, you'll see the status here.", "noRewardsTitle": "No rewards", "noRewardsPrimaryBtn": "Deposit and continue playing", "noRewardsPrimaryLink": "?cashier=deposit", "noRewardsSecondaryBtn": "See available promotions", "noRewardsSecondaryLink": "/promotions", "getBonusAndPlay": "Get bonus and play", "noBonusThanks": "No thanks, I don’t want any bonus", "bonusProgressLabel": "You have {{validityDays}} days to wager {{wagerAmount}} to get your guaranteed {{amount}} cash. You can deposit more to help your progress.", "of": "of", "noRewardsText": "No bonuses at the moment, continue to play at Casino Days to get more rewards.", "bonusReleaseRestrictedProgressLabel": "You have {{validityDays}} days to wager {{wagerAmount}} to get your guaranteed {{amount}} cash. You can deposit more to help your progress.", "bonusMoney": "Bonus money", "amount": "Amount given", "wageredInfoMessage": "Wagered {{totalWagerProgressAmountFormatted}} of {{totalWagerAmountFormatted}}"}, "recoverPassword": {"emailStepSuccessDescription": "A link to reset your password has been sent to {{email}}.", "emailStepSuccessFooter": "Don't have an account? Register here.", "emailStepHeader": "Forgot password?", "emailStepDescription": "Forgetful? Enter your email and we will send instructions.", "recoverStepHeader": "Password recovery help", "recoverStepDescription": "Password recovery help", "resetPassword": "Reset password", "sendResetEmailBtn": "Send reset link", "emailStepSuccessHeader": "Reset link sent"}, "info": {"supportEmail": "<EMAIL>"}, "chat": {"availiablity": "10:00 - 22:00 CET"}, "transaction": {"netPosition": "Net position", "totalDeposit": "Total deposit", "totalNet": "Total net", "dateTime": "Date/Time", "type": "Type", "win": "Win", "game": "Game", "status": "Status", "amount": "Amount", "winning": "Winning", "bet": "Bet", "id": "ID", "transactionId": "Transaction ID", "balance": "Balance", "transactionsBetween": "Transactions between:", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "reversed": "Cancelled", "success": "Success", "noPayments": "No payments", "noPaymentsDescription": "When you deposit or withdraw money, you'll see the transactions here.", "noBets": "No bets", "noBetsDescription": "When you play games with real money, you'll see your bets here.", "amountWon": "Amount won", "startBalance": "Start balance", "noSessions": "No sessions", "date": "Date", "method": "Method", "pendingTitle": "Pending transaction", "endBalance": "End balance", "noSessionsDescription": "When you start playing, you’ll see your game sessions here.", "wageredAmount": "Wagered amount", "pendingMessage": "We are aware of your last transaction and kindly ask you to wait until processed."}, "accountTransactionType": {"REAL_CASH_DEPOSIT": "<PERSON><PERSON><PERSON><PERSON>", "REAL_CASH_WITHDRAW": "<PERSON><PERSON><PERSON>", "REAL_CASH_CASHOUT_REVERSAL": "Cancelled", "REAL_CASH_ADDITION_BY_CS": "Manual credit", "REAL_CASH_REMOVAL_BY_CS": "Manual debit", "CREDIT_CASINOBONUS_INSTANT_BONUS": "Instant bonus buy", "DEBIT_CASINOBONUS_INSTANT_BONUS": "Instant bonus debit"}, "accountTransactionStatus": {"SUCCESS": "Completed"}, "gameTransactionType": {"CASINO_BUYIN": "Bet/Win", "CASINO_FREESPIN_WIN": "Free spins winnings"}, "balance": {"cashToRelease": "Cash to release", "viewBonus": "Bonus details", "lowBalance": "Your balance is low"}, "seoGame": {"welcomeOfferCTA": "Claim now<span>It only takes a minute</span>", "welcomeOfferStepOne": "Fill in our quick form", "welcomeOfferStepTwo": "Select your welcome bonus", "welcomeOfferStepThree": "Deposit and play your favourite games", "welcomeOfferTitle": "UP TO €100 WELCOME OFFER", "welcomeOfferDescription": "<p>Love playing games like this? Well we have some great news for you because at Casino Days you can try out this game and over 2000 other great slots and table games plus over 200 Live Casino tables with our €100 Welcome Offer!</p><p>To take advantage of your Welcome Offer all you need to do is click on sign-up, take 2 minutes to register and then head straight to your rewards section – simple! After that the only thing left to do is select from our wide range of deposit methods and you'll be ready to play for big cash prizes on any of our great games.</p><p>Don't waste another second – jump into the action today!</p>"}, "units": {"day_one": "1 Day", "day_other": "{{value}} Days", "day_zero": "0 Days", "month_one": "1 Month", "month_other": "{{value}} Months", "month_zero": "0 Months", "year_one": "1 Year", "year_other": "{{value}} Years", "year_zero": "0 Years", "second_one": "1 Second", "second_other": "{{value}} Seconds", "second_zero": "0 Seconds", "minute_one": "1 Minute", "minute_other": "{{value}} Minutes", "minute_zero": "0 Minutes", "hour_one": "1 Hour", "hour_other": "{{value}} Hours", "hour_zero": "0 Hours", "week_one": "1 Week", "week_other": "{{value}} Weeks", "week_zero": "0 Weeks", "dayOne": "1 Day"}, "snackbar": {"success": "Your details have been updated successfully", "error": "Something went wrong. Please try again"}, "providers": {"thunderkick": "Thunderkick", "booming": "Booming Games", "booongo": "Booongo", "habanero": "Habanero", "playson": "<PERSON><PERSON>", "redtiger": "Red Tiger", "relax_quickspin": "Quickspin", "relax_pushgaming": "Push Gaming", "relax_relax": "Relax Gaming", "relax_bigtime": "Big Time Gaming", "relax_felt": "Felt", "relax_stakelogic": "Stakelogic", "relax_fantasma": "Fantasma", "relax_nolimitcity": "Nolimit City", "pragmaticplay": "Pragmatic Play", "netent": "NetEnt", "onextwo": "1x2", "hub88_asiagaming": "Asia Gaming", "hub88ebet": "eBet", "hub88_fugaso": "Fugaso", "hub88_gamatron": "Gamatron", "hub88_gameart": "GameArt", "hub88_ganapati": "G<PERSON><PERSON><PERSON>", "hub88_goldenrace": "Golden Race", "hub88_lottoinstantwin": "Lotto Instant Win", "hub88_oryxgaming": "Oryx Gaming", "hub88_superspadegames": "Super Spade Games", "hub88_wazdan": "<PERSON>azdan", "hub88_playngo": "Play'n GO", "hub88_gamomat": "Gamomat", "hub88_spadegaming": "Spade Gaming", "hub88_ezugi": "<PERSON><PERSON><PERSON>", "hub88_goldenhero": "Golden Hero", "hub88_indislots": "Indi slots", "hub88_onetouch": "One Touch", "hub88_ebet": "eBET", "mgs_2by2": "2By2", "mgs_adoptit": "AdoptIt", "mgs_alchemygaming": "Alchemy", "mgs_betgamestv": "Betgames.tv", "mgs_bigtimegaming": "Big Time Gaming", "mgs_blablablastudios": "BlaBlaBla", "mgs_colossusbets": "Colossus Bets", "mgs_crazytoothstudio": "<PERSON> Tooth", "mgs_dtech": "D-Tech", "mgs_electricelephant": "Electric Elephant", "mgs_fantasma": "Fantasma", "mgs_fortunefactorystudios": "Fortune Factory", "mgs_foxium": "Foxium", "mgs_gameburgerstudios": "Gameburger", "mgs_gameplay": "GamePlay", "mgs_gamevy": "<PERSON><PERSON>", "mgs_genesisgaming": "Genesis Gaming", "mgs_goldcoinstudios": "Gold Coin", "mgs_goldenrockstudios": "Golden Rock", "mgs_halfpixelstudios": "Half Pixel", "mgs_jftw": "JFTW", "mgs_lightningboxgames": "Lightning Box", "mgs_liveg24": "Live G24", "mgs_mga": "MGA", "mgs_microgaming": "Microgaming", "mgs_nekogames": "Neko", "mgs_neonvalleystudios": "Neon Valley", "mgs_oldskool": "Old Skool", "mgs_pariplay": "<PERSON><PERSON><PERSON>", "mgs_pearfiction": "Pear Fiction", "mgs_plankgaming": "Plank Gaming", "mgs_presentcreative": "Present Creative", "mgs_probabilityjones": "Probability Jones", "mgs_pulse8": "Pulse 8", "mgs_rabcat": "<PERSON><PERSON><PERSON>", "mgs_realisticgames": "Realistic", "mgs_redrakegaming": "Red Rake", "mgs_sigmagaming": "Sigma Gaming", "mgs_skillzzgaming": "Skillzz", "mgs_slingshotstudios": "Slingshot", "mgs_snowbornstudios": "<PERSON>born", "mgs_spearheadstudios": "<PERSON><PERSON><PERSON>", "mgs_spinplaygames": "SpinPlay", "mgs_stormcraftstudios": "Stormcraft", "mgs_switchstudios": "Switch", "mgs_tripleedgestudios": "Triple Edge", "mgs_zonelock": "Zonelock", "mgs_eyecon": "Eyecon", "mgs_inspired": "Inspired Gaming", "playngo": "Play'n GO", "elk": "ELK", "yggdrasil": "Yggdrasil", "tvbet": "TVBET", "hub88_genii": "Genii", "pushgaming": "Push Gaming", "mgs_ainsworthgames": "<PERSON><PERSON>", "mgs_all41studios": "All41", "hub88_blueprintgaming": "Blueprint", "hub88_evolutiongaming": "Evolution", "hub88_tomhorn": "<PERSON>", "isoftbet": "iSoftBet", "quickspin": "Quickspin", "relax_swintt": "<PERSON><PERSON><PERSON>", "hub88_spribe": "Spribe Gaming", "hub88_turbogames": "Turbo Gaming", "hub88_evoplayentertainment": "EvoPlay"}, "postRegister": {"modalHeader": "Welcome to Casino Days", "bannerOfferMessage": "Get an extra 20 Free Spins when you deposit in the next", "depositWithBonusBtn": "Deposit and get bonus", "depositWithoutBonusBtn": "Deposit without bonus"}, "wallet": {"modalHeader": "Wallet", "balanceHeader": "Your balance", "closeToReachMessageFirstPart": "Just a heads up that you are close to reaching your", "closeToReachMessageSecondPart": "deposit limit. You can change your limits here.", "availableBonusTitle": "Available bonuses", "bonusEndLabel": "Ends in", "noRewards": "Wallet no rewards"}, "account": {"personalInfoMenu": "Personal info", "personalInfoMenuDescription": "Your personal details and how we can reach you.", "transactionsMenu": "Transactions", "transactionsMenuDescription": "Review your deposits, withdrawals and game rounds.", "bonusesMenuDescription": "See your active bonuses and review your bonus history.", "settings": "Settings", "identityVerification": "Verification", "rgContactPhoneNumber": "***********", "rgContactPhoneNumberHeadline": "Call ConnexOntario", "rgContactPhoneNumberLabel": "**************", "depositLimitMenu": "Deposit limit", "timeoutMenu": "Take a break", "selfExclusionMenu": "Self-exclusion", "wagerLimitMenu": "Spend limit", "settingsOtpAuthMenu": "Confirm mobile number", "settingMenuDescription": "Set language, authentication and marketing preferences.", "test": {"key": {"one": "<p>Work</p>"}}, "bonusesMenu": "Bonuses", "settingsMenuDescription": "Set authentication and marketing preferences.", "rgPageLinkLabel": "Read more about Responsible Gaming", "settingsMenu": "Settings", "limitsMenu": "Limits", "lossLimitMenu": "Loss limit", "limitsMenuDescription": "View and set your limits to keep your gambling in control.", "rgContactMessage": "If you feel you need to talk to a professional organisation about your gambling, get in touch with <a href='https://www.connexontario.ca/' target='_blank'>https://www.connexontario.ca/</a>. They provide a free and confidential space to talk about problem gambling.", "settingsAppAuthMenu": "Connect authenticator app", "identityVerificationDescription": "Submit your KYC documents and verify your account.", "gamblingPause": "Gambling pause", "gamblingSettings": "Gambling settings"}, "testKey": {"test1": "Just a test one", "test2": "It's a test 2", "test3": "It's just a test three"}, "demo": {"testValue": "ROW Test value for demo"}, "limitTypeSelect": {"depositLimit": "<p>With the deposit limit settings, you can choose to set a cap on how much money you can afford to lose in a given time period.</p>\n<p>Deposit limit features:</p>\n<ul>\n<li>Set a custom loss cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you won’t be able to continue to play if you have reached your cap for the period.</li>\n</ul>", "wagerLimit": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>\n<p>Use take a break if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>", "selfExclusion": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>\n<p>Use take a break if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>", "timeOut": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>\n<p>Use take a break if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>", "lossLimit": "<p>With the loss limit settings, you can choose to set a cap on how much money you can afford to lose in a given time period.</p>\n<p>Loss limit features:</p>\n<ul>\n<li>Set a custom loss cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you can still continue to play.</li>\n</ul>", "timeout": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>\n<p>Use take a break if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>", "introduction": "<h2>Pick a limit</h2>\n<p>Choose your responsible gambling measure to start configuring your settings.</p>", "lossLimits": "<p>With the loss limit settings, you can choose to set a cap on how much money you can afford to lose in a given time period.</p>\n<p>Loss limit features:</p>\n<ul>\n<li>Set a custom loss cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you can still continue to play.</li>\n</ul>"}, "paymentMethod": {"IWALLET_HOSTED": "iWallet", "JCB_HOSTED": "JCB", "JETON_HOSTED": "<PERSON><PERSON>", "KLUWP_HOSTED": "KLUWP", "MAESTRO_HOSTED": "Maestro", "MASTERCARD_HOSTED": "Mastercard", "MC_PAYMENT_HOSTED": "MC Payment", "MIFINITYEWALLET_HOSTED": "MiFinity eWallet", "MUCHBETTER_HOSTED": "MuchBetter", "NEOSURFVOUCHER_HOSTED": "Neosurf Voucher", "ONLINE_BANKING_HOSTED": "Online Banking", "PARAMOUNT_HOSTED": "Paramount", "PAYKASA_HOSTED": "<PERSON><PERSON><PERSON>", "PAYMENTIQ_HOSTED": "PaymentIQ", "PAYMENTSBANK_HOSTED": "Payments Bank", "PAYMENTSWALLET_HOSTED": "Payments Wallet", "PAYMENTSWEBREDIRECT_HOSTED": "Payments Web Redirect", "PAYPAL_HOSTED": "PayPal", "PAY_SAFE_CARD_HOSTED": "Paysafecard", "PRADEXX_HOSTED": "Pradexx", "PREMIERPAYDIRECT_HOSTED": "PremierPay Direct", "PREMIERPAYWALLET_HOSTED": "PremierPay Wallet", "QIWI_HOSTED": "QIWI", "QUICKPAY_HOSTED": "Quickpay", "REFUND_HOSTED": "Refund", "REVERSAL_HOSTED": "Reversal", "RUPEEPAYMENTSCASH_HOSTED": "Rupee Payments Cash", "SOFORT_HOSTED": "SOFORT", "STICPAY_HOSTED": "STICPAY", "VEGA_HOSTED": "Vega", "VENUSPOINT_HOSTED": "Venus Point", "VISA_HOSTED": "Visa", "VOID_HOSTED": "Void", "WEBREDIRECT_HOSTED": "Web Redirect", "WEB_MONEY_HOSTED": "WebMoney", "SKRILL_HOSTED": "skrill", "BANK_HOSTED": "Bank", "NETELLER_HOSTED": "<PERSON><PERSON>", "AMERICANEXP_HOSTED": "American Express", "APCO_HOSTED": "Apco", "ASTROPAYBANK_HOSTED": "AstroPay Bank", "ASTROPAYCARD_HOSTED": "AstroPay Card", "ASTROPAYDIRECT_HOSTED": "AstroPay Direct", "BANKDIRECT_HOSTED": "Bank Direct", "BANKDOMESTIC_HOSTED": "Bank Domestic", "BANKIBAN_HOSTED": "Bank IBAN", "BANKLOCAL_HOSTED": "Bank Local", "BESTPAYBANKTRANSFER_HOSTED": "Bestpay Bank Transfer", "BESTPAY_HOSTED": "Bestpay", "BRITEPLAY_HOSTED": "Brite Play", "CAPTURE_HOSTED": "Capture", "CASHLIB_HOSTED": "Cashlib", "COINGATE_HOSTED": "CoinGate", "COINIFY_HOSTED": "Coinify", "COMMUNITYBANK_HOSTED": "Community Bank", "CREDITCARD_HOSTED": "Credit Card", "CRYPTOCURRENCY_HOSTED": "Crypto Currency", "CUBITS_HOSTED": "<PERSON><PERSON><PERSON>", "CUP_HOSTED": "China Union Pay", "DIMOCO_HOSTED": "DIMOCO", "EASYEFT_HOSTED": "EasyEFT", "ECOPAYZ_HOSTED": "ecoPayz", "ENTERCASH_HOSTED": "Entercash", "EPS_HOSTED": "EPS", "EZEEBILLBANK_HOSTED": "Ezeebill Bank", "FACILEROBANK_HOSTED": "Facilero Bank", "FLEXEPIN_HOSTED": "Flexepin", "FUNANGA_HOSTED": "Funanga", "GIROPAY_HOSTED": "Giropay", "HEXOPAY_HOSTED": "Hexopay", "ICARD_HOSTED": "iCard", "IDEAL_HOSTED": "iDeal", "INOVAPAYWALLET_HOSTED": "INOVAPAY Wallet", "INTERAC_DIRECT_HOSTED": "Interac Direct", "INTERAC_HOSTED": "Interac"}, "limitPeriodSelect": {"introduction": "<h2>Pick a time period</h2>\n<p>Choose a time period for your limit.</p>\n<p>Be aware that this is a periodical time limit and that your limit will continue to be applied until you change or remove your limit. This is not a one time limit.</p>"}, "settings": {"communicationLabel": "Bonuses and communication", "communicationTitle": "Communication", "authenticationTitle": "Authentication", "communicationDescription": "These settings give you the control to get bonuses, promotions, offers and other updates.\nIf you’re still missing out on bonus offers, make sure that your details are correct or check your spam folder.", "passwordLabel": "Change your password", "passwordTitle": "Password"}, "cashier": {"recomended": "Recommended", "customerFavourite": "Customer Favorite", "withdrawalAmount": "Withdrawal amount", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "availableBonusTitle": "Available bonuses", "availableWithdrawal": "Available for withdrawal", "withdrawalWillLoseBonuses": "Please note that if you make a withdrawal you will lose any bonuses.", "depositSuccessful": "Deposit successful!", "depositFailed": "Deposit failed!", "depositFailedFallbackMsg": "The status of your deposit could not be confirmed and therefore we kindly ask you to contact our Customer Support team for further assistance. Please kindly note that your transaction can be checked and credited manually from Monday to Friday between 8am and 5pm.", "depositWithBonus": "Deposit with selected bonus", "depositWithoutBonus": "Deposit without bonus", "withdrawalFailed": "Withdrawal successful!", "withdrawalSuccessful": "<PERSON><PERSON><PERSON> failed", "withdrawalFailedFallbackMsg": "Something went wrong with your withdrawal request, please try again or get in touch with us.", "withdrawalMsgAmountLeftToPlay": "{{currencySymbol}}{{amount}} left on your account to play with", "pendingWithdrawals": "Pending withdrawals", "depositLimitMessage": "You have an active deposit limit and can deposit a maximum of {{amount}}.", "receiptTotalBalance": "Total balance", "receiptBonusGiven": "Bonus Given", "receiptDeposited": "Deposited", "withdrawalsIsBeingReviewed": "Your withdrawal is being reviewed. Once your withdrawal is approved the money will be in your bank instantly.", "makeANewDeposit": "Make a new deposit", "currentBalance": "Current balance: {{amount}} {{currency}}", "transactionSuccessfull": "{{method}} successful!", "transactionFailed": "{{method}} failed"}, "limitAmountSelect": {"introduction": "<h2>Set your amount</h2>\n<p>Pick an amount or enter your desired limit.</p>"}, "countries": {"en": "the country you are located in", "fr_ca": "Canada", "za": "South Africa", "nz": "New Zealand", "no": "Norway", "fi": "Finland", "jp": "Japan", "ca": "Canada", "ee": "Estonia", "on": "Canada", "in": "India"}, "chatLabels": {"replyField": "Reply", "chatHeader": "Chat with Us", "pushNotificationQuestion": "Don't miss out on any replies! Allow push notifications?", "csatQuestion": "Did we address your concerns?", "searchField": "Search", "csatReply": "Add your comments here", "chatHelpDescription": "Reach out to us if you have any questions", "csatYesQuestion": "How would you rate this interaction?", "offlineMessage": "We are currently away. Please leave us a message", "csatNoQuestion": "How could we have helped better?", "csatThankYou": "Thanks for the response", "csatSubmitRate": "Submit your rating here", "appName": "Casinodays support", "emailPlaceholderField": "Enter email", "namePlaceholderField": "Name", "phonePlaceholderField": "Enter phone number", "questionPlaceholderField": "Any questions?", "departmentPlaceholderField": "Department field", "onlineTitle": "Online", "offlineTitle": "Offline"}, "marketModal": {"exitLabel": "Close", "infoMessage": "Experience Casino Days with tailor-made offers, payment solutions and relevant games for {{nationality}}", "continueLabel": "Yes, please", "mainMessage": "Casino Days is available in {{market}}!"}, "brite": {"modalDepositTitle": "Deposit and play", "buttomDepositAndPlay": "Deposit and play", "loginModalTitle": "Continue playing with Bank ID", "postRegistrationDescription": "You're ready to start playing. Choose if you want to have your bonus now or save it for later.", "postRegisterBonusWaiting": "There is a bonus waiting for you.", "finishSignUp": "Deposit successful", "marketingHeading": "Preferences", "marketingDescription": "Yes, I would like to receive bonuses, free stuff and special offers from Casino Days.", "depositSuccessBonusClaimed": "Deposit has been completed. Bonus claimed successfully.", "postRegistrationActivateBonusDescription": "Your bonus is activated.", "requiredExistingPassword": "You will only need to use your existing password to confirm your account. Future logins will be using your bank identification.", "playerEmailAlreadySignedUp": "Ah, it looks like you already signed up with us using the email address:", "confirmExistingAccount": "To confirm your existing account please enter your password to continue.", "modalLoginPlain": "Continue playing", "maximumWithdrawalAmountError": "Maximum withdrawal is {{amount}}", "minimumWithdrawalAmountError": "Minimum withdrawal is {{amount}}", "registrationSecondsCount": "10", "accountNotExistTitle": "No account found", "completeRegistrationDescription": "Hi {{firstName}}, your account has been created. Complete the last step to start playing.", "maximumDepositAmountError": "Maximum deposit is {{amount}}", "depositInfoBalance": "Your deposit of €{{amount}} is completed.\nTotal balance is €{{amount}}.", "depositInfoDescription": "You're ready to start playing.", "accountNotExist": "Please register a new account to get your welcome package of €500 cash guarantee and 100 free spins in Book of Dead. You will be redirected to the registration in {{timer}} seconds.", "depositFailedLogin": "Sorry, something went wrong with your deposit. Please try again.", "minimumDepositAmountError": "Minimum deposit is {{amount}}", "disclaimer": "By selecting <PERSON><PERSON><PERSON><PERSON> and play above, I agree to Casino Days <a href=\"?terms=true\">Terms and Conditions</a> and <a href=\"?privacy=true\">Privacy Policy</a>.", "postRegisterHeaderTitle": "Hooray!", "cashierInfoMsg": "Please be informed that from 00:00 EEST till 06:00 EEST OP Financial Group has scheduled a planned maintenance so during this period you will not be able to deposit via this bank.", "buttonContinuePlaying": "Continue playing"}, "profileVerification": {"cancelBtn": "Cancel", "resendNewCode": "Resend a new code", "emailVerificationSent": "An email with a verification code has been sent to <span>{{email}}</span>.", "moreHelp": "Do you need help?", "areYouSureModalTitle": "Are you sure you want to quit verification?", "resendNewCodeSecondsTimer": "50", "emailTitle": "Email", "phoneNumberTitle": "Phone number", "addCodeEmail": "Enter the code here to verify your email and get started!", "addCodeSms": "Enter the code here to verify your phone and get started!", "savePhoneNumber": "Save Phone Number", "accountVerificationIntro": "To make sure your Casino Days account is secure, we have to verify your identity.", "accountVerificationChooseMethod": "Please select if you want to verify your account by email or SMS.", "codeIsValidFor": "The code is valid for", "smsCodeToBeRecieved": "It may take a minute to receive your code.<br/>Didn’t receive a text message?", "verifyWithEmail": "Verify with email", "verifyWithSms": "Verify with SMS", "smsVerificationSent": "A text message with a verification code has been sent to <span>{{phone}}</span>.", "resendNewCodeCompleted": "You can resend a new code in <span>{{seconds}}</span> seconds.", "codeNotReceived": "Didn’t receive the code?", "codeToBeRecieved": "It may take a minute to receive your code. Make sure you check your spam folder.", "verificationSuccess": "Verification successful.", "changeBtn": "Change", "saveEmail": "Save email", "modalText": "You won’t be able to log in until you fully verify your email.", "changeText": "(Change)"}, "test": {"test": "123", "fake": {"text": "Placeholder"}, "new": "Select 1;", "one": {"two": {"something": "test"}}}, "location": {"checkingGeoLocksVerifyStatus": "Checking GeoLocks Verify status", "checkingXpointVerifyStatus": "Checking XPoint Verify status", "xpointAppRequired": "You need XPoint Verify in order to place any bet. Please install or launch it here.", "xpointVerify": "Xpoint verify", "downloadAndInstall": "Download and install", "errorOnRunCheck": "Please make sure that Xpoint Verify application is running and location permissions are allowed on your device. Run check again or refresh the page.", "xpointAppDescription": "XPoint is the next generation geolocation and fraud prevention platform for eCommerce and iGaming.", "verificationComplete": "Verification complete!", "runCheck": "Run check", "launchXpointVerify": "Launch XPoint Verify"}, "blockedCountry": {"btnLabel": "Play Now", "btnUrl": "https://playorbet.com/en/casino/review/germany", "title": "Sorry, but we do not accept players from this region.", "description": "However you can visit here to find lots of great sites and offers where you can play today."}, "verification": {"browseFiles": "Browse files or take photo", "provideDocuments": "Please upload a copy of your Passport, ID Card or Drivers License. The Document must be valid and show: \n<ul>\n  <li>\n    Full Name,\n  </li>\n  <li>\n    Date of Birth,\n  </li>\n  <li>\n    Issuing date,\n  </li>\n  <li>\n     Expiration date,\n  </li>\n  <li>\nYour Signature,\n   </li>\n   <li>\n      All four corners of the document. \n   </li>\n</ul>\nKindly note that you might need to upload two sides (front and back) of your document to show all required details.\n<br>\nWe can accept copies of documents in jpg, png and pdf format, with a maximum size of 10 Megabytes.", "submitNeeded": "Why is this needed?", "statusRequested": "Requested", "statusRejected": "Rejected", "statusPending": "Pending", "statusVerified": "Verified", "requested": "Requested", "submitt": "Submitted", "pleaseSubmit": "Please submit the documents requested to securely verify your information.", "sourceOfIncome": "Source of income", "address": "Address", "payment": "Payment Instrument", "identity": "Identity", "showExample": "Show example document", "fileTypeAndSize": "PNG, JPG or PDF. 10MB max file size.", "submit": "Submit documents", "reviewedMessage": "Your document has been approved.", "emailNotification": "You will receive an email notification within 7 days once we have reviewed your documents.", "backToVerification": "Back to verification", "verified": "Verified", "failed": "Failed", "pending": "Pending", "messageFailed": "Your document has been rejected.", "modalHelp": "Do you need help?", "typeOfDocument": "Type of document", "promptModalTitle": "Leave Page?", "documentTooOld": "<b>The issuing date of your document is too old.</b>\n<br>Please upload another document, issued within the timeframe stipulated in the instructions.", "uploadError": "The file {{fileName}} could not be uploaded. Only files of type PNG, JPG or PDF with a file size of max 10MB can be uploaded.", "initMessage": "Your document has been uploaded and is awaiting our review.", "reviewedMessageConfirmation": "Please rest assured that we do our best to check all documents as fast as possible and the status of the document will updated on this page as soon as our KYC team had a chance to check your copies. <br><br>If we won't be able to accept your documents, you will be notified about the reason within this section and you will also be able to upload new documents.", "creditCardNumber": "<b>The copy of your card does not show the required digits from the card number.</b>\n<br>Please upload another copy of your card showing only first 6 and last 4 digits of the card number.", "accountOwnership": "<b>Your document does not show the name of the account holder.</b>\n<br>Please upload another copy of your document containing the required information.", "accountNumber": "<b>Your document does not show the account number/identifier.</b>\n<br>Please upload another copy of your document containing the required information.", "scannedDocument": "<b>The uploaded document seems to be a photo of a previously scanned document.</b>\n<br>Please upload a photo of an original document according to the instructions available on our page.", "taxDeclarationOption": "Tax declaration", "creditCardNotSigned": "<b>The back side of your credit card is not signed.</b>\n<br>Please upload another copy of your card with a valid signature.", "bankStatementOption": "Bank statement", "inufficientQualityOfThePicture": "<b>The quality of the uploaded document is not sufficient.</b>\n<br>Please upload another copy of your document with higher quality.", "creditCardHolder": "<b>The card holder's name is not visible on your document.</b>\n<br>Please upload another copy of your card where the name of the card holder is clearly visible.", "cornersNotVisible": "<b>All four corners of the document are not visible.</b>\n<br>Please upload another copy of your document, making sure that all four corners are visible on the picture.", "uploadDocumentAgain": "Upload document again", "salesOfSharesOrOtherInvestments": "Sales of shares or other investments", "saleOfProperty": "Sale of property", "dividentPayment": "Divident payment", "fixedDepositSavings": "Fixed deposit savings", "promptModalContent": "You have unsaved changes. Are you sure you want to leave this page?", "savingsFromSalary": "Savings from salary", "proofOfOwnershipTitle": "<p>Proof of Ownership of Your Bank/E-wallet Account</p>", "paymentOtherDocumentsTitle": "<p>Other Documents</p>", "incorrectDocumentType": "<b>The type of uploaded document is not correct. </b>\n<br>Please upload another document following the instructions available on our page.", "proofOfPaymentTitle": "<p>Proof of Payment/Deposit</p>", "paymentOtherDocumentsDescription": "<p>We might request other additional documents that can help us to verify your account or a payment method and specific instructions related to these documents will be provided to you via email.</p>", "areYouSureModalTitle": "Are you sure you want to quit verification?", "sourceOfIncomeMessage": "Please upload a document showing the proof of your regular income.\n<br><br>\nThe type of the supporting document will be different depending on the source of your income. \n<br><br>\nExamples of acceptable documents are listed below:", "creditCardTitle": "Credit Card", "isDocumentTypeSelected": "Please select value from the dropdown below.", "creditCardDescription": "<p>If we asked for copies of your credit card, please kindly upload a high-resolution picture of the front and back of the credit card.&nbsp;</p><br>\n<p>For your own safety and the integrity of your credit card, please blank out the digits so that only the first 6 and last 4 digits are visible. Please also cover the CVC/CVV code on the back of your card.&nbsp;</p><br>\n<p>Please note that your card must have your signature in the designated field to be valid.</p><br>\n<p>We can accept copies of documents in jpg, png and pdf format, with a maximum size of 10 Megabytes.</p>", "proofOfPaymentDescription": "<p>If we asked for proof of deposit or payment made on a particular date or from a particular account/e-wallet/card. Please kindly upload a copy of a physical document* or the original PDF file* showing the transaction or transactions indicated in our email as well as the account number and the account holder's name.</p><br>\n<p>*A bank/e-wallet statement sent by your bank showing your name and address, the bank’s name, physical address, and your account number.</p><br>\n<p>We can accept copies of documents in jpg, png and pdf format, with a maximum size of 10 Megabytes.</p>", "messageFailedDescription": "Unfortunately we could not verify your document. Please kindly refer to the message sent to your registered email address for more details.", "messageRequested": "It's time to verify your account.  <br><br>\nPlease check the list of required documents below and upload your copies according to the instructions.", "companySale": "Company sale", "addressMessage": "Please upload PDF or photo of either a Utility Bill, Bank or Credit Card Statement or similar official document. \nThe Document must contain: \n<ul>\n  <li>\n    Your Full Name,\n  </li>\n  <li>\n    Your Address,\n  </li>\n  <li>\n    The issuing date, which is not older than 3 months.\n  </li>\n</ul>\nWe can accept copies of documents in jpg, png and pdf format, with a maximum size of 10 Megabytes.", "incompleteDetails": "<b>Your document does not show all required details.</b>\n<br>Please upload another document, making sure that all mandatory details mentioned in the instructions are visible.", "documentExpired": "<b>Your document is expired.</b>\n<br>Please upload another valid document according to the instructions available on our site.", "missingDetails": "<b>Your document does not contain the required information.</b>\n<br>Please upload another document, making sure that all mandatory details mentioned in the instructions are visible.", "payslipOption": "The most recent Payslip", "companyProfits": "Company profits", "paymentMessage": "You have been requested to verify the payment method used for deposit or withdrawal on our site. \n<br><br>\nWe sent you an email containing the list of the payment methods that required verification. Please kindly refer to that email and upload the required documents below.\n<br><br>\nIn order to make it easier, we included a detailed description of what details have to be included in your documents in order to ensure the successful and hassle-free completion of the verification process.", "proofOfOwnershipDescription": "<p>If we asked for proof of ownership of your bank or e-wallet account, please kindly upload a copy of a physical document* or the original PDF file* showing the account number and the account holder's name.</p><br>\n<p>*A bank/e-wallet statement sent by your bank/financial institution showing your name and address, the bank’s name, physical address, and your account number. This cannot be older than 3 months.</p><br>\n<p>We can accept copies of documents in jpg, png and pdf format, with a maximum size of 10 Megabytes.</p>", "inheritance": "Inheritance", "gift": "Gift", "loan": "Loan", "headline": "Currently you are not required to upload any documents to verify your account. \nPlease note, that the verification process can be initied for different reasons and you will be notified via email when that happens. \nThe list of required documents will be available in this secition, together with clear instructions and the option to upload your documents."}, "limits": {"lossLimitsHeadline": "Put a limit on the amount you can loose during a specific time period.", "wagerLimitsHeadline": "Put a limit on the amount you can spend during a specific time period.", "pendingLimit": "Your request has been sent and is waiting our approval.", "requestedLimit": "Your request for changing the limit value has been requested. {{value}} are needed for change to be effective.", "responsibleGamingHeadline": "To enjoy a safe and responsible online casino experience, we offer several features enabling you to control yourself and only play with what you can afford.", "depositLimitsHeadline": "Put a limit on the amount you can deposit during a specific time period.", "selfExclusionHeadline": "If you feel that your gameplay is having a negative effect on you personally, your situation or those around you, you are able to close your account. As soon as you start the time out, you cannot log back in and you will no longer receive any marketing material from us. Should you wish to re-open your account after the time period has passed, you will be required to contact our customer support services and go through a consideration period before the account will be re-opened again.", "depositLimitsInfoBox": "Put a limit on the amount you can deposit during a specific time period.", "lossLimitsInfoBox": "Put a limit on the amount you can loose during a specific time period.", "timeoutHeadline": "If you are worried about how much you are gambling, you can activate a time out. This will help you trigger a break from playing. As soon as you start the time out, you cannot log back in. You can access your Casino Days account again when the time out period expires.", "requestSentSuccessfully": "Your request has been sent. Changes will take effect in X days.", "pendingRequest": "Your request has been sent - changes will be made after X days"}, "multiFactorAuth": {"heading": "Multi-factor authentication", "appAuthTitle": "Authenticator App", "otpAuthTitle": "SMS authentication", "otpAuthLabel": "Use your mobile phone to receive verification codes.", "appAuthPageHeading": "Connect an authenticator app that generates one time verification codes.", "appAuthPageStepTwo": "Scan the QR code or manually input your secret key into the app.", "appAuthPageStepThree": "Enter the code generated by the app and connect.", "otpAuthPageHeading": "Please enter and confirm the mobile number of your account in order to use SMS authentication.", "otpVerifyCodeHeadline": "A text message with a code was just sent. Enter the code below to complete the verification process.", "otpVerifyCodeResendLabel": "Didn’t receive a text message?", "approveSignInRequest": "Approve sign in request", "appAuthLabel": "Use an authentication app to generate one time verification codes.", "description": "Provide enhanced security when accessing your Casino Days account.", "appAuthPageStepOne": "Download and install an authenticator app (such as Duo or Google Authenticator) on your mobile device.", "appVerifyCodeHeadline": "From the authenticator app, get a code and enter it below to complete the verification process."}, "disabledRegistration": {"btn": {"Text": "Disabled Registration"}}, "transactions": {"activeSession": "Active"}, "session": {"duration": "Session duration", "hour": "{{hour}}h", "seconds": "{{seconds}}sec", "minutes": "{{minutes}}min"}, "general": {"vpnUsageTitle": "Oh snap!", "vpnUsageMessage": "Seems like you are using a VPN to access the site."}, "searchCategory": {"gamesStudio": "Game studios", "popularGameStudios": "Popular game studios", "features": "Features", "liveGames": "Live Games", "allGames": "All Games"}, "blabla": {"dgfd": "Bonuses", "dfgfdg": "See your active bonuses and review your bonus history."}, "blablabla": {"dgfvdsv": "Bonuses", "dfgf": "See your active bonuses and review your bonus history."}, "raffles": {"ctaTitle": "Opt In", "description": "Earn tickets playing your favorite games and have the chance to win amazing prizes. <a href=\"https://casinodays.com\">linky</a>", "gameBoostersTitle": "Game boosters", "noGameBoosters": "No game boosters", "tickets": "Tickets", "deposits": "depositsdeposits", "wagers": "Wagers", "deposit_wager": "deposits and wagers", "template": {"point": "This booster gives you {{operator}} on {{target}} in the following games.", "deposit_wager": "This booster gives you {{operator}} of your {{target}} in the following games."}, "title": "Raffles", "booster": {"template": "This booster gives you {{operator}} on {{target}} in the following games.", "activeTimeLabel": "Active between {{startDate}} to {{endDate}}", "modal": {"header": "Your current Raffle"}}, "RaffleButtonTitle": "Read more", "yearsLeft": "years left", "monthLeft": "month left", "hourLeft": "1 hour left", "monthsLeft": "months left", "yearLeft": "year left", "daysLeft": "days left", "dayLeft": "day left", "minutesLeft": "minutes left", "minuteLeft": "minute left", "hoursLeft": "hours left", "progress": "Progress", "nextTicket": "Next ticket"}, "maintenance": {"title": "We'll be back soon", "reload": "Reload page", "description": "Casino Days is down for scheduled maintenance and will be back online as soon as possible."}, "blockedCountryFi": {"title": "The site is not available in this region"}, "label": {"visitBrandWeb": "Visit Casino Days Web", "countryUnavailable": "The application is currently not available in your region", "fb_sitename": "CasinoDays"}, "lobby": {"searchPlaceHolder": "Search Game or Studio"}, "rgConfirmationStep": {"introduction": "<h2>Are you sure?</h2>\n<p>You are about to set a limit on your account.</p>\n<p>Once the limit is set it will take x days to change the limit if you want to increase or remove it.</p>\n<p>Please review the limits and confirm that they are correct.</p>", "introductionPauses": "<h2>Are you sure?</h2><p>You are about to restrict access to your account and will not be able to log in for the set period.</p><p>Please review the restrictions and confirm that they are correct.</p>"}, "rgLabel": {"amount": "Amount", "period": "Time Period", "type": "Limit type", "dailyPeriod": "Daily", "weeklyPeriod": "weekly", "depositType": "deposit", "wagerType": "wager", "monthlyPeriod": "monthly", "lossType": "loss", "amountLeft": "Amount left to limit", "timeLeft": "Time left to limit reset"}, "verificationInfoMessage": {"labels": "Please verify your account to continue using the site."}, "verificationButtonMessage": {"labels": "Verify"}, "mobile": {"goToSettings": "Go To Settings.", "updatingError": "Updating Error", "updateManually": "Update manually", "noNewVersion": "You’re up-to-date!", "updateDownloading": "The update is downloading", "updateBeingInstalled": "The update is being installed"}, "activeLimitsStep": {"title": "Your active limits"}, "rgInfoStep": {"pauseIntroduction": "To enjoy a safe and responsible casino experience, you’re given the option to take a pause from gambling. Please see the different pauses to understand what suits your needs.", "timeout": "<h2>Take a break</h2>\n<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua..</p>\n<p>Use take a break if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>", "settingsContent": "<h2>Loss limits</h2>\n<p>With the loss limit settings, you can choose to set a cap on how much money you can afford to lose in a given time period.</p>\n<p>Loss limit features:</p>\n<ul>\n<li>Set a custom loss cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you can still continue to play.</li>\n</ul>\n\n<h2>Deposit limits</h2>\n<p>With the deposit limit settings, you can cap how much money you can deposit in a given period. Regardless if you are winning, you won’t be able to continue to play if you have reached your deposit limit.</p>\n<p>Deposit limit features:</p>\n<ul>\n<li>Set a custom deposit cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you won’t be able to continue to play if you have reached your cap for the period.</li>\n</ul>", "pauseContent": "<h2>Take a break</h2>\n<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>\n<p>Use take a break if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>\n\n<h2>Self exclusion</h2>\n<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>\n<p>Use self exclusion if you:</p>\n<ul>\n<li>Duis aute irure dolor in.</li>\n<li>Excepteur sint occaecat cupidatat.</li>\n<li>Ut enim ad minim veniam.</li>\n</ul>", "lossLimits": "<h2>Loss limits</h2>\n<p>With the loss limit settings, you can choose to set a cap on how much money you can afford to lose in a given time period.</p>\n<p>Loss limit features:</p>\n<ul>\n<li>Set a custom loss cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you can still continue to play.</li>\n</ul>", "introduction": "To enjoy a safe and responsible casino experience, you’ll have various options to choose from to control your gambling environment and only play with what you can afford.", "depositLimits": "<h2>Deposit limits</h2>\n<p>With the deposit limit settings, you can cap how much money you can deposit in a given period. Regardless if you are winning, you won’t be able to continue to play if you have reached your deposit limit.</p>\n<p>Deposit limit features:</p>\n<ul>\n<li>Set a custom deposit cap.</li>\n<li>Set a specific period for your cap.</li>\n<li>If you withdraw your winnings, you won’t be able to continue to play if you have reached your cap for the period.</li>\n</ul>"}, "voucherflexepinSecondaryInfo": {"RhinoCashier": "This is a secondary info text."}, "limitChangeAmount": {"typeDepositDaily": "daily deposit limit", "typeDepositMonthly": "monthly deposit limit", "typeLossWeekly": "weekly loss limit", "typeWagerDaily": "daily wager limit", "typeWagerMonthly": "monthly wager limit", "typeDepositWeekly": "weekly deposit limit", "typeLossDaily": "daily loss limit", "typeLossMonthly": "monthly loss limit", "typeWagerWeekly": "weekly wager limit", "introduction": "<h2>Set new amount for {{limitType}}</h2>\n<h2>Are you sure?</h2>\n<p>You are about to set a new limit on your account.</p>\n<p>Once the limit is set it will take 7 days to change the limit if you want to increase or remove it. Decreasing your limit will take effect instantly.</p>\n<p>Please set and review the limits and confirm that they are correct to change your current limit.</p>"}, "pausePeriodSelect": {"introduction": "<h2>Pick a time period</h2><p>Choose a time period for your pause.</p><p>Be aware that this is a periodical time limit and that your limit will continue to be applied until you change or remove your limit. This is not a one time limit.</p>"}, "pauseTypeSelect": {"introduction": "<h2>Choose your pause</h2><p>Choose your responsible gambling measure to start configuring your settings.</p>"}, "user": {"confirmDetails": "Confirm details", "checkDetailsMessage": "For your safety we need to confirm that your details are up to date.", "updateFailedDescription": "Something went wrong. Your personal details could not be updated.", "detailsUpdated": "Your personal details are up to date and you can keep playing safely.", "updateFailed": "Failed to update details", "pepTooltipMessage": "We need to know whether you are a politically exposed person (PEP) or family member or close associate of a PEP or the Head of an international organisation (HIO)."}, "download": {"appDownloadText": "If your download doesn't start automatically click here."}, "brand": {"casinodays": "Big Boost"}}