{"uuid": "cd5c7c70-e0a3-48cb-a091-49d4378c98fb", "lastMigration": 33, "name": "Mockoon Examples", "endpointPrefix": "", "latency": 250, "port": 8889, "hostname": "", "folders": [], "routes": [{"uuid": "7e10b648-5c3f-4976-a6df-d192297169d9", "type": "crud", "documentation": "Endpoint performing CRUD operations on a data bucket (automatically creates GET, POST, PUT, DELETE routes)", "method": "", "endpoint": "users", "responses": [{"uuid": "11badce3-8277-4f30-ada3-ab62640477f6", "body": "{}", "latency": 0, "statusCode": 200, "label": "Perform CRUD operations on the \"Users\" databucket (\"Data\" tab at the top)", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "uj4s", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "d0f22a55-e378-4140-b063-6a0981926120", "type": "http", "documentation": "Generate random body (JSON, text, CSV, etc) with templating", "method": "get", "endpoint": "template", "responses": [{"uuid": "4ef90610-a591-4486-80b9-e04a293d9c94", "body": "{\n  \"Templating example\": \"For more information about templating, click the blue 'i' above this editor\",\n  \"users\": [\n    {{# repeat (queryParam 'total' '10') }}\n      {\n        \"userId\": \"{{ faker 'number.int' min=10000 max=100000 }}\",\n        \"firstname\": \"{{ faker 'person.firstName' }}\",\n        \"lastname\": \"{{ faker 'person.lastName' }}\",\n        \"friends\": [\n          {{# repeat (faker 'number.int' 5) }}\n            {\n              \"id\": \"{{ faker 'string.uuid' }}\"\n            }\n          {{/ repeat }}\n        ]\n      },\n    {{/ repeat }}\n  ],\n  \"total\": \"{{queryParam 'total' '10'}}\"\n}", "latency": 0, "statusCode": 200, "label": "Creates 10 random users, or the amount specified in the 'total' query param", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "c76819ea-e1e0-4031-9b23-0deed9d3ba5a", "type": "http", "documentation": "Use multiple responses with rules", "method": "post", "endpoint": "content/:param1", "responses": [{"uuid": "11cdbb04-a207-4da0-abc7-a23b5a0c78b5", "body": "{\n  \"Rules example\": \"Default response. Served if route param 'param1' is not present.\"\n}", "latency": 0, "statusCode": 200, "label": "Default response", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}, {"uuid": "9fad0592-bf9c-48fe-9502-ea7c09a9b74e", "body": "{\n  \"Rules example\": \"Content XYZ. Served if route param 'param1' equals 'xyz'. (See in 'Rules' tab)\"\n}", "latency": 0, "statusCode": 200, "label": "Content XYZ", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [{"target": "params", "modifier": "param1", "value": "xyz", "invert": false, "operator": "equals"}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "3cdf5155-88af-478f-80d9-07a8eb7d1adf", "body": "{\n  \"Rules example\": \"Content not found. Served if route param 'param1' is not equal to 'xyz'. (See in 'Rules' tab)\"\n}\n", "latency": 0, "statusCode": 404, "label": "Content not found", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [{"target": "params", "modifier": "param1", "value": "^(?!.*xyz).*$", "invert": false, "operator": "regex"}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "6e6525af-1053-4006-b5c1-34d82b1cbfb1", "type": "http", "documentation": "Path supports various patterns", "method": "put", "endpoint": "path/with/pattern(s)?/*", "responses": [{"uuid": "464da66b-6eb8-4a86-8973-1f1b3987c787", "body": "The current path will match the following routes: \nhttp://localhost:3000/path/with/pattern/\nhttp://localhost:3000/path/with/patterns/\nhttp://localhost:3000/path/with/patterns/anything-else\n\nLearn more about Mockoon's routing: https://mockoon.com/docs/latest/api-endpoints/routing/", "latency": 0, "statusCode": 200, "label": "", "headers": [{"key": "Content-Type", "value": "text/plain"}], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "********-61f0-4cf7-a173-6ca284f72d27", "type": "http", "documentation": "\"Guard\" route protecting all routes starting with /protected/", "method": "all", "endpoint": "protected/*", "responses": [{"uuid": "7fb6f6e6-c82a-440b-b312-0c400912825c", "body": "{\n  \"error\": \"Unauthorized\"\n}", "latency": 0, "statusCode": 401, "label": "Requires the presence of an 'Authorization' header", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [{"target": "header", "modifier": "Authorization", "operator": "null", "invert": false, "value": ""}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": "FALLBACK", "streamingMode": null, "streamingInterval": 0}, {"uuid": "bc95a503-ac8b-427f-9d0c-eae227ee601a", "type": "http", "documentation": "Protected route", "method": "get", "endpoint": "protected/path", "responses": [{"uuid": "525633c9-1a5e-482f-abb8-1456d68910bf", "body": "You can serve the same responses based on the same rules for all or part of your endpoints by creating global routes using the fallback mode and a wildcard path. \nThis is useful if you want to protect all your endpoints by checking if an Authorization header is present or if you want to verify that all your requests contain a specific property in their body.\nTo learn more: https://mockoon.com/docs/latest/route-responses/global-routes-with-rules/", "latency": 0, "statusCode": 200, "label": "", "headers": [{"key": "Content-Type", "value": "text/plain"}], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "7e10b648-5c3f-4976-a6df-d192297169d9"}, {"type": "route", "uuid": "d0f22a55-e378-4140-b063-6a0981926120"}, {"type": "route", "uuid": "c76819ea-e1e0-4031-9b23-0deed9d3ba5a"}, {"type": "route", "uuid": "6e6525af-1053-4006-b5c1-34d82b1cbfb1"}, {"type": "route", "uuid": "********-61f0-4cf7-a173-6ca284f72d27"}, {"type": "route", "uuid": "bc95a503-ac8b-427f-9d0c-eae227ee601a"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [{"uuid": "09029f93-a8fc-4f7e-92eb-d816d41cde52", "id": "uj4s", "name": "Users", "documentation": "", "value": "[\n  {{#repeat 50}}\n  {\n    \"id\": \"{{faker 'string.uuid'}}\",\n    \"username\": \"{{faker 'internet.username'}}\"\n  }\n  {{/repeat}}\n]"}], "callbacks": []}