{"uuid": "0389a975-5408-4f1f-bfd4-241135db1d55", "lastMigration": 33, "name": "RL API", "endpointPrefix": "", "latency": 500, "port": 8880, "hostname": "", "folders": [], "routes": [{"uuid": "7d5629be-f812-4530-8bdb-14a8138e7641", "type": "http", "documentation": "", "method": "post", "endpoint": "auth/login", "responses": [{"uuid": "9e3c2d04-3625-4c31-afbf-28de489fad42", "body": "", "latency": 300, "statusCode": 200, "label": "successful response, correct creds", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "qsvp", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "email", "value": "<EMAIL>", "invert": false, "operator": "regex"}, {"target": "body", "modifier": "password", "value": "Test12345", "invert": false, "operator": "regex"}], "rulesOperator": "AND", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "6be7db98-644c-4766-be2c-8fab0991931c", "body": "{\n\t\"status\": \"Error\",\n\t\"timestamp\": \"2025-07-03T07:25:59.443Z\",\n\t\"data\": {\n\t\t\"message\": \"Unknown error, <NAME_EMAIL>:Test12345\",\n\t\t\"error\": \"Bad request\",\n\t\t\"statusCode\": 400\n\t}\n}", "latency": 0, "statusCode": 400, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "c6821be7-2fc7-4bce-ab0c-47d9be846335", "type": "http", "documentation": "", "method": "get", "endpoint": "auth/token/refresh", "responses": [{"uuid": "6e2c8aaa-69bc-4dbb-9045-610bb11af287", "body": "", "latency": 222, "statusCode": 200, "label": "", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "j3hk", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": true, "fallbackTo404": true, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "bbf88985-f7af-431a-acd0-7da959de39d7", "type": "http", "documentation": "here we imitate random 401 bahavior", "method": "get", "endpoint": "player/btag", "responses": [{"uuid": "87a9e42f-114a-492b-bd0d-30eb57dba6ff", "body": "{}", "latency": 0, "statusCode": 401, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}, {"uuid": "37e056de-1263-4f1a-8681-a4b6e1ed0dd1", "body": "{\n  \"btag\": \"test\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": "RANDOM", "streamingMode": null, "streamingInterval": 0}, {"uuid": "27b3b459-fe60-4dc8-8454-d32a2859d23a", "type": "http", "documentation": "", "method": "get", "endpoint": "banners", "responses": [{"uuid": "bc263ae3-a0f4-4081-a5a5-9aec09a3fe92", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/banners.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "97587ec8-9b09-4649-bb0e-0bd8a2335ac1", "type": "http", "documentation": "", "method": "get", "endpoint": "cashback", "responses": [{"uuid": "cc5f0813-0c1d-4fbd-81d3-11daf3af94cf", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/cashback.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "63f40de5-9ead-4235-82b5-d6a850ef5ec6", "type": "http", "documentation": "", "method": "get", "endpoint": "promotions", "responses": [{"uuid": "8bdbd62a-1995-4d22-917a-4f6935430c30", "body": "", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "promo", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "fdde09ca-3938-401d-91a8-21440e8b9de2", "type": "http", "documentation": "", "method": "get", "endpoint": "bonus", "responses": [{"uuid": "94bcead6-d159-4660-a1b5-b5b7b458fca7", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/bonus.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "65124ea8-feb7-40c4-9ee4-712a4e83ce9e", "type": "http", "documentation": "", "method": "get", "endpoint": "ecr/bonuses", "responses": [{"uuid": "bcb7cf97-0e26-4744-8125-d1b2a40b7d2e", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/active-bonuses-for-ecr.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "7d5629be-f812-4530-8bdb-14a8138e7641"}, {"type": "route", "uuid": "c6821be7-2fc7-4bce-ab0c-47d9be846335"}, {"type": "route", "uuid": "bbf88985-f7af-431a-acd0-7da959de39d7"}, {"type": "route", "uuid": "27b3b459-fe60-4dc8-8454-d32a2859d23a"}, {"type": "route", "uuid": "65124ea8-feb7-40c4-9ee4-712a4e83ce9e"}, {"type": "route", "uuid": "63f40de5-9ead-4235-82b5-d6a850ef5ec6"}, {"type": "route", "uuid": "97587ec8-9b09-4649-bb0e-0bd8a2335ac1"}, {"type": "route", "uuid": "fdde09ca-3938-401d-91a8-21440e8b9de2"}], "proxyMode": true, "proxyHost": "https://api-middleware.dev.rhinoent.net", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With, x-correlation-id"}], "proxyReqHeaders": [{"key": "X-Api-Key", "value": "c4e25f1d-4b3f-4db3-8bbf-2030f6d396d0"}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [{"uuid": "dd98d96c-1a11-4d8a-9fef-514df88e9f10", "id": "qsvp", "name": "auth response", "documentation": "", "value": "{\n    \"status\": \"Success\",\n    \"timestamp\": \"{{now 'yyyy-MM-dd\\'T\\'HH:mm:ss.SSS\\'Z\\''}}\",\n    \"data\": {\n       \"status\": \"SUCCESS\",\n       \"playerData\": {\n  \t\t  \t\"playerId\": \"000000001\",\n  \t\t  \t\"email\": \"<EMAIL>\"\n  \t\t  },\n  \t\t  \"sessionData\": {\n  \t\t    \"sessionToken\": \"{{faker 'internet.jwt'}}\",\n          \"refreshToken\": \"{{faker 'internet.jwt'}}\"\n  \t\t  }\n    }\n}"}, {"uuid": "b158c55e-d67d-49bc-b9b3-239dd247855c", "id": "j3hk", "name": "refresh token response", "documentation": "", "value": "{\n    \"status\": \"Success\",\n    \"timestamp\": \"{{now 'yyyy-MM-dd\\'T\\'HH:mm:ss.SSS\\'Z\\''}}\",\n    \"data\": {\n        \"accessToken\": \"{{faker 'internet.jwt'}}\",\n        \"refreshToken\": \"{{faker 'internet.jwt'}}\"\n    },\n    \"var\": \"{{getGlobalVar 'counter'}}\"\n}"}, {"uuid": "f5a8d9e6-3c2b-4f7a-9e8d-1b5c6a7f8e9d", "id": "promo", "name": "promotions", "documentation": "Dynamically generated promotions with Faker data", "value": "{\n  \"status\": \"Success\",\n  \"timestamp\": \"{{now 'yyyy-MM-dd\\'T\\'HH:mm:ss.SSS\\'Z\\''}}\",\n  \"data\": {\n    \"payload\": [\n      {{#repeat 21}}\n      {\n        \"id\": {{faker 'number.int' '{\"min\": 1, \"max\": 1000}'}},\n        \"slug\": \"{{faker 'lorem.slug'}}\",\n        \"name\": \"{{faker 'lorem.words' 2}}\",\n        \"startAt\": \"{{faker 'date.past' 365}}\",\n        \"expiresAt\": \"{{faker 'date.future' 365}}\",\n        \"caption\": \"{{faker 'lorem.words' 3}}\",\n        \"description\": \"<p>{{faker 'lorem.sentence'}}</p>\",\n        \"isWelcomeOffer\": {{faker 'datatype.boolean'}},\n        \"ctas\": {\n          \"{{faker 'string.alphanumeric' 8}}\": {\n            \"id\": \"{{faker 'string.alphanumeric' 8}}\",\n            \"body\": {\n              \"link\": \"register\",\n              \"mode\": \"normal\",\n              \"name\": \"Join Now\",\n              \"tags\": [\n                {\n                  \"id\": {{faker 'number.int' '{\"min\": 1, \"max\": 30}'}},\n                  \"name\": \"authentication:all\"\n                }\n              ],\n              \"type\": \"primary\",\n              \"position\": \"center\"\n            },\n            \"type\": \"BUTTON_COMPONENT\",\n            \"index\": 0,\n            \"position\": 0\n          }\n        },\n        \"publishPath\": \"staging/casinodays/promotions/{{faker 'lorem.slug'}}_en_ROW.json\",\n        \"isOngoing\": {{faker 'datatype.boolean'}},\n        \"ongoingStatus\": \"{{oneOf (array 'Welcome Offer' 'Limited Time' 'Weekend Special' '')}}\",\n        \"affiliateIds\": [],\n        \"title\": \"<h1>{{faker 'lorem.sentence'}}</h1>\",\n        \"image\": {\n          \"mobile\": \"https://picsum.photos/800/400\",\n          \"desktop\": \"https://picsum.photos/800/400\"\n        },\n        \"seo\": {\n          \"title\": \"{{faker 'lorem.sentence'}}\",\n          \"description\": \"{{faker 'lorem.paragraph'}}\"\n        },\n        \"terms\": {\n          \"content\": \"<ul><li>{{faker 'lorem.sentence'}}</li><li>{{faker 'lorem.sentence'}}</li><li>{{faker 'lorem.sentence'}}</li></ul>\",\n          \"hasTerms\": {{faker 'datatype.boolean'}}\n        },\n        \"tags\": [\n          {\n            \"id\": {{faker 'number.int' '{\"min\": 1, \"max\": 30}'}},\n            \"name\": \"{{oneOf (array 'authentication:all' 'display:web-only' 'display:app-only' 'display:both')}}\"\n          }\n        ]\n      }{{#unless @last}},{{/unless}}\n      {{/repeat}}\n    ],\n    \"totalItems\": 8,\n    \"totalPages\": {{faker 'number.int' '{\"min\": 1, \"max\": 5}'}},\n    \"currentPage\": 1\n  }\n}"}], "callbacks": []}