{"welcomeShown": true, "maxLogsPerEnvironment": 100, "truncateRouteName": true, "mainMenuSize": 246, "secondaryMenuSize": 200, "fakerLocale": "en", "fakerSeed": null, "lastChangelog": "9.2.0", "environments": [{"uuid": "0389a975-5408-4f1f-bfd4-241135db1d55", "path": "/Users/<USER>/Library/Application Support/mockoon/storage/demo.json", "cloud": false, "lastServerHash": null}], "disabledRoutes": {}, "collapsedFolders": {}, "enableTelemetry": true, "storagePrettyPrint": true, "fileWatcherEnabled": "disabled", "dialogWorkingDir": "", "startEnvironmentsOnLoad": false, "logTransactions": false, "environmentsCategoriesOrder": ["local", "cloud"], "environmentsCategoriesCollapsed": {"local": false, "cloud": false}, "envVarsPrefix": "MOCKOON_", "activeEnvironmentUuid": "0389a975-5408-4f1f-bfd4-241135db1d55", "enableRandomLatency": false, "recentLocalEnvironments": []}