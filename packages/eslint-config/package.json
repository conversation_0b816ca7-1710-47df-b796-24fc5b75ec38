{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.20.0", "@next/eslint-plugin-next": "^15.1.6", "eslint": "^9.20.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-turbo": "^2.4.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.15.0", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0", "typescript-plugin-css-modules": "^5.1.0"}}