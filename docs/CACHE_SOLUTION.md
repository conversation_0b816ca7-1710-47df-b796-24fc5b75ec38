# Custom Cache Solution with Stale-While-Revalidate

This custom caching solution was implemented to handle large data that exceeds Next.js's 2MB cache limit, featuring **stale-while-revalidate** logic similar to Next.js behavior.

## Problem Solved

The `getAllGamesList` API response was ~17MB, causing this error:

```
Error: Failed to set Next.js data cache, items over 2MB can not be cached (17766779 bytes)
```

## Solution

- **Memory-based cache**: Uses in-memory Map to store large data
- **Stale-while-revalidate**: Returns stale data instantly and refreshes in background
- **No automatic expiration**: Data persists until manually invalidated (no TTL)
- **Selective caching**: Only cache processed/filtered data, not raw responses
- **Manual invalidation**: API endpoints to manage cache manually
- **Background refresh**: Prevents duplicate fetches during refresh

## Usage

### Stale-While-Revalidate Caching (Recommended)

```typescript
const result = await withCache(
  'unique-cache-key',
  async () => {
    // Your expensive operation here
    return await fetchLargeData()
  },
  60, // Consider stale after 60 seconds
)
```

### Basic Caching (Legacy)

```typescript
const result = await withCache(
  'unique-cache-key',
  async () => {
    // Your expensive operation here
    return await fetchLargeData()
  },
  60, // Consider stale after 60 seconds (optional)
)
```

### Cache Management API

**Get cache statistics:**

```bash
GET /api/cache
```

**Invalidate games cache:**

```bash
DELETE /api/cache?type=games
```

**Clear all cache:**

```bash
DELETE /api/cache?type=all
```

### Games-specific Functions

```typescript
import { invalidateGamesCache, getGamesCacheStats } from '@/network/server-utils/cacheUtils'

// Invalidate all games cache entries
invalidateGamesCache()

// Get games cache statistics
const stats = getGamesCacheStats()
```

## How It Works

1. **Raw games data** (17MB) is fetched fresh each time (no caching)
2. **Processed games data** is cached using our custom memory cache
3. Cache keys include parameters to ensure correct invalidation
4. Automatic cleanup removes expired entries every 5 minutes
5. Manual invalidation available via API or direct function calls

## How Stale-While-Revalidate Works

This implementation follows the same pattern as Next.js:

1. **Fresh data** (within staleTime): Returns cached data immediately ⚡
2. **Stale data** (beyond staleTime, within TTL): Returns stale data immediately + refreshes in background 🔄
3. **No data** (beyond TTL): Fetches fresh data synchronously ⏳

### Example Timeline

```
Time 0s:    [MISS] Fetch fresh data (2s delay) → Cache indefinitely, stale after 1min
Time 5s:    [FRESH] Return cached data instantly
Time 65s:   [STALE] Return stale data instantly + refresh in background
Time 66s:   [STALE] Return stale data instantly (background refresh in progress)
Time 68s:   [FRESH] Return fresh data from completed background refresh
Time ∞:     Data persists until manually invalidated
```

## API Testing

### Demo Endpoint

Test the caching behavior:

```bash
GET /api/demo-cache
```

### Cache Management API

**Get basic cache statistics:**

```bash
GET /api/cache
```

**Get detailed cache statistics (with stale info):**

```bash
GET /api/cache?detailed=true
```

**Invalidate games cache:**

```bash
DELETE /api/cache?type=games
```

**Force refresh stale entries:**

```bash
DELETE /api/cache?type=stale
```

**List stale entries (without deleting):**

```bash
GET /api/cache?type=stale-list
```

**Clear all cache:**

```bash
DELETE /api/cache?type=all
```
