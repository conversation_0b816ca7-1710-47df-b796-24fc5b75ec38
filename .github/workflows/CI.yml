name: CI
on:
  pull_request:
    # branches:
    #   - master
    #   - release/**
  workflow_dispatch:
jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        uses: ./.github/actions/install
      - name: Run ESLint
        run: |
          export NODE_OPTIONS="--max_old_space_size=4096"
          yarn lint
      - name: Run check types
        run: yarn check-types
      - name: Run check duplicated code
        run: yarn check-duplicated-code
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        uses: ./.github/actions/install
      - name: Run Unit tests
        run: |
          export NODE_OPTIONS="--max_old_space_size=4096"
          node -v
          yarn jest --version
          yarn test
