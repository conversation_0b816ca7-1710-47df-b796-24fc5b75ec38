# Rhino Next Project Copilot Instructions

## 🏗️ Project Architecture

- **Monorepo Structure**: Turborepo with multiple Next.js apps (`core`, `luckyone`, `dreamspins`) and shared packages
- **Main Apps**:
  - `apps/core/` - Primary application with full component library
  - `apps/luckyone/` & `apps/dreamspins/` - Brand-specific apps that extend core
- **Shared Packages**: `@repo/ui`, `@repo/types`, `@repo/helpers`, `@repo/hooks`, `@repo/constants`, `@repo/api`

## 🎨 Styling System

- **Design System**: Style Dictionary + SCSS Modules + HeroUI + Tailwind CSS
- **Two SCSS Files Per Component**:
  1. `ComponentName.module.scss` - Custom styles using Style Dictionary variables
  2. `_ComponentName.sd.module.scss` - Auto-generated Style Dictionary tokens (DO NOT EDIT)
- **Import Pattern**:
  ```scss
  @use './ComponentName.sd.module' as *;
  @use '@theme/functions.scss' as *;
  @use '@theme/variables.scss' as *;
  ```
- **Sizing**: Use `calculate-rem()` function for consistent rem-based measurements
- **Colors**: Use Style Dictionary variables like `$color-primary`, `$color-surface-100`, etc.

## 📁 Component Structure

- **Location**: `src/components/` for reusable components, `src/components/@screens/` for page-specific
- **Naming**: PascalCase for directories and files (`RegistrationForm/RegistrationForm.tsx`)
- **Files per Component**:
  - `ComponentName.tsx` - Main component
  - `ComponentName.module.scss` - Styles
  - `_ComponentName.sd.module.scss` - Style Dictionary (auto-generated)
  - Optional: `.client.tsx`, `.stories.tsx`, `types.ts`

## ⚛️ React Patterns

- **Forms**: React Hook Form + Zod validation + Controller for HeroUI components
- **State Management**: React hooks, custom hooks in `hooks/` folder
- **UI Library**: HeroUI components (`@heroui/button`, `@heroui/input`, etc.)
- **Styling**: CSS Modules with camelCase class names
- **TypeScript**: Strict typing with interfaces prefixed with `I` (e.g., `IRegistrationFormProps`)

## 🛠️ Import Aliases

```typescript
@app/* → ./app/*
@screens/* → ./components/@screens/*
@modules/* → ./modules/*
@theme/* → ./theme/*
@repo/* → ../../packages/*
```

## 🎯 Component Development Guidelines

1. **Always use TypeScript** with proper interface definitions
2. **Use HeroUI components** as base building blocks
3. **Follow SCSS module pattern** with Style Dictionary integration
4. **Implement proper form validation** with react-hook-form + zod
5. **Use semantic HTML** and proper accessibility attributes
6. **Responsive design** with mobile-first approach
7. **Error handling** with proper error states and user feedback

## 🔧 Development Commands

- `yarn dev:core` - Start core app development server
- `yarn build` - Build all apps
- `yarn test:core` - Run tests for core app
- `yarn lint` - Lint all code
- `yarn generate-types-for-apis` - Generate API types

## 📦 Key Dependencies

- **UI**: HeroUI, Tailwind CSS, CSS Modules
- **Forms**: React Hook Form, Zod
- **State**: React hooks, custom hooks
- **Routing**: Next.js App Router
- **Styling**: SCSS, Style Dictionary
- **Testing**: Jest, Testing Library

## 🚨 Important Conventions

- **Never edit `_*.sd.module.scss` files** - they're auto-generated
- **Use `calculate-rem()` for all spacing/sizing**
- **Import Style Dictionary variables first in SCSS**
- **Follow component file naming exactly** (`ComponentName.tsx`, not `componentName.tsx`)
- **Use Controller component** for all HeroUI form inputs
- **Keep components focused and single-responsibility**
- **Export interfaces and types** for reusability

## 🎨 Current Form Context (RegistrationForm)

- Located in `@screens/register/components/RegistrationForm.tsx`
- Uses react-hook-form with Controller pattern
- Integrates HeroUI components (Input, Button, Checkbox, Alert)
- Implements conditional promo code field
- Has proper validation with error display
- Includes social login buttons placeholder
- Responsive tab interface (Sign up/Sign in)

## 💡 Best Practices for Future Requests

1. **Read existing code patterns** before implementing new features
2. **Follow the established file structure** exactly
3. **Use existing Style Dictionary tokens** rather than hardcoded values
4. **Implement proper TypeScript types** for all props and data
5. **Test components** with both valid and invalid states
6. **Consider mobile responsiveness** in all implementations
7. **Use semantic naming** for CSS classes and component props
8. **Follow accessibility guidelines** (ARIA attributes, keyboard navigation)

## 📝 Example Component Pattern

### TypeScript Component with HeroUI Integration

```tsx
'use client'
import React from 'react'
import { clsx } from 'clsx'
import { Controller } from 'react-hook-form'
import { Input } from '@heroui/input'
import { Button } from '@heroui/button'
import styles from './MyComponent.module.scss'

interface IMyComponentProps {
  onSubmit?: VoidFunction
  className?: string
}

export const MyComponent: React.FC<IMyComponentProps> = ({ onSubmit, className }) => {
  // Component logic here

  return (
    <div className={clsx(styles.container, className)}>
      <Controller
        name="fieldName"
        control={form.control}
        render={({ field }) => (
          <Input
            label="Field Label"
            placeholder="Enter value"
            variant="bordered"
            classNames={{
              input: styles.input,
              inputWrapper: styles.inputWrapper,
            }}
            {...field}
            errorMessage={form.formState.errors.fieldName?.message}
            isInvalid={!!form.formState.errors.fieldName?.message}
          />
        )}
      />
    </div>
  )
}
```

### SCSS Module Pattern

```scss
// MyComponent.module.scss
@use './MyComponent.sd.module' as *;
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  background: $background-color;
  padding: calculate-rem(16px);
  border-radius: $border-radius;

  @media (max-width: $breakpoint-mobile) {
    padding: calculate-rem(12px);
  }
}

.input {
  background: $input-background;
  color: $input-text-color;
}

.inputWrapper {
  border: $input-border;
  border-radius: $input-radius;
}
```

### Style Dictionary Module (Auto-generated)

```scss
// _MyComponent.sd.module.scss
/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$background-color: $color-surface-100;
$border-radius: $radius-md;
$input-background: $color-surface-200;
$input-text-color: $color-surface-1000;
$input-border: $border-width-default solid $color-surface-300;
$input-radius: $radius-sm;
```

These instructions provide the foundation for understanding and working effectively with the Rhino Next codebase architecture, styling system, and development patterns.
