name: Install dependencies
description: ""
runs:
  using: composite
  steps:
    # - name: Cache yarn dependencies
    #   uses: actions/cache@v4
    #   id: cache-yarn
    #   with:
    #     path: |
    #       **/node_modules
    #       .yarn/install-state.gz
    #     key: ${{ runner.os }}-yarn-cache-${{ hashFiles('yarn.lock') }}
    - name: Enable Corepack
      run: corepack enable
      shell: bash
    - uses: actions/setup-node@v4
      with:
        node-version: 22
        cache: "yarn"
    - name: Install yarn deps
      run: |
        export DETOX_DISABLE_POSTINSTALL=1
        yarn --version
        yarn --immutable
      shell: bash
